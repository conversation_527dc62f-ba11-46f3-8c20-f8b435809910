<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيق المهام المتقدم - TDL V1.0</title>
    <meta name="description" content="تطبيق مهام متقدم مع واجهة عصرية وميزات متقدمة">
    <meta name="keywords" content="مهام, قائمة مهام, إدارة مهام, تطبيق مهام, تطبيق ويب تقدمي, PWA">
    <meta name="author" content="TDL Team">
    <meta name="theme-color" content="#4a6cf7">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="TDL">
    <link rel="manifest" href="manifest.json">
    <link rel="icon" href="img/icons/icon-192x192.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="img/icons/icon-192x192.svg">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/optimized.css">
    <link rel="stylesheet" href="css/style.css">
    <!-- سكريبتات PWA -->    
    <script src="js/sw-register.js" defer></script>
    <script type="module" src="js/optimized.js"></script>
    <script type="module" src="js/db.js"></script>
    <script type="module" src="js/offline.js"></script>
</head>
<body>
    <!-- شاشة التحميل -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-container">
            <div class="loading-spinner"></div>
            <p class="loading-text">جاري تحميل التطبيق...</p>
        </div>
    </div>

    <header class="main-header">
        <button id="menuToggle" class="menu-toggle" aria-label="القائمة" title="القائمة">
                <i class="fas fa-bars"></i>
            </button>
        <h1><i class="fas fa-tasks"></i> قائمة المهام</h1>
        <button id="themeToggle" title="تبديل الوضع الليلي">
                <i class="fas fa-moon"></i>
            </button>
        </header>

    <!-- القائمة الجانبية المنبثقة -->
    <aside id="sideMenu" class="sidebar-popup">
        <div class="sidebar-indicator">القائمة الجانبية</div>
        <div class="sidebar-content">
            <button id="closeSideMenu" class="side-menu-close" aria-label="إغلاق القائمة" title="إغلاق القائمة">
                    <i class="fas fa-times"></i>
                </button>
            <nav>
                <div class="sidebar-section">
                    <div class="sidebar-title sidebar-title-main">الفلاتر</div>
                    <button class="nav-btn active" data-filter="all"><i class="fas fa-list-ul"></i> كل المهام</button>
                    <button class="nav-btn" data-filter="active"><i class="fas fa-spinner"></i> النشطة</button>
                    <button class="nav-btn" data-filter="completed"><i class="fas fa-check-circle"></i> المكتملة</button>
                    <div class="sidebar-view-toggle">
                        <button class="view-toggle-btn active" data-view="list"><i class="fas fa-list"></i> قائمة</button>
                        <button class="view-toggle-btn" data-view="grid"><i class="fas fa-th"></i> شبكة</button>
                    </div>
                </div>
                <hr>
                <div class="sidebar-section">
                    <div class="sidebar-title sidebar-title-main">الأقسام</div>
                    <button class="cat-btn active" data-category="all">كل الأقسام</button>
                    <button class="cat-btn" data-category="عمل">عمل</button>
                    <button class="cat-btn" data-category="شخصي">شخصي</button>
                    <button class="cat-btn" data-category="دراسة">دراسة</button>
                </div>
                <hr>
                <div class="sidebar-section">
                    <div class="sidebar-title sidebar-title-main">الأدوات</div>
                    <button class="nav-btn" id="settingsMenu"><i class="fas fa-cog"></i> إعدادات</button>
                    <button class="nav-btn" id="themeToggleSidebar" title="تبديل الوضع الليلي">
                        <i class="fas fa-moon"></i> تبديل الوضع
                    </button>
                    <button class="nav-btn" id="searchTaskBtn"><i class="fas fa-search"></i> بحث متقدم</button>
                    <button class="nav-btn" id="exportDataBtn"><i class="fas fa-download"></i> تصدير البيانات</button>
                    <button class="nav-btn" id="importDataBtn"><i class="fas fa-upload"></i> استيراد البيانات</button>
                </div>
                <hr>
                <div class="sidebar-section">
                    <div class="sidebar-title sidebar-title-main">الإحصائيات السريعة</div>
                    <div class="quick-stats">
                        <div class="stat-item">
                            <i class="fas fa-tasks"></i>
                            <span>المجموع: <span id="sidebarTotalTasks">0</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-check-circle"></i>
                            <span>مكتملة: <span id="sidebarCompletedTasks">0</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-clock"></i>
                            <span>معلقة: <span id="sidebarPendingTasks">0</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-percentage"></i>
                            <span>نسبة الإنجاز: <span id="sidebarCompletionRate">0%</span></span>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="sidebar-section">
                    <button class="nav-btn" id="logoutMenu"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</button>
                </div>
            </nav>
        </div>
    </aside>
    <div id="sidebarOverlay" class="sidebar-overlay"></div>

    <main>
        <!-- صندوق إضافة مهمة جديد -->
        <section class="task-input-bar">
            <button id="mainSearchBtn" class="search-task-btn" title="بحث"><i class="fas fa-search"></i></button>
            <input type="text" id="taskInput" class="task-input" placeholder="أضف مهمة جديدة...">
            <select id="taskCategory" class="task-category-select" title="اختر تصنيف المهمة">
                <option value="عام">عام</option>
                <option value="عمل">عمل</option>
                <option value="شخصي">شخصي</option>
                <option value="دراسة">دراسة</option>
                <option value="صحة">صحة</option>
                <option value="مالي">مالي</option>
            </select>
            <select id="taskPriority" class="task-priority-select" title="اختر أولوية المهمة">
                <option value="low">منخفضة</option>
                <option value="medium" selected>متوسطة</option>
                <option value="high">عالية</option>
                <option value="urgent">عاجل</option>
            </select>
            <button id="addTaskBtn" class="add-task-btn"><i class="fas fa-plus"></i> إضافة</button>
        </section>
        
        <!-- صندوق البحث المحسن -->
        <div id="searchBox" class="search-box" style="display: none;">
            <div class="search-container">
                <div class="search-header">
                    <h3><i class="fas fa-search"></i> البحث في المهام</h3>
                    <button id="closeSearchBtn" class="close-search-btn" title="إغلاق البحث">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="search-input-container">
                    <input type="text" id="searchInput" class="search-input" placeholder="اكتب للبحث في المهام...">
                    <div class="search-options">
                        <label class="search-option">
                            <input type="checkbox" id="searchInText" checked>
                            <span>البحث في نص المهمة</span>
                        </label>
                        <label class="search-option">
                            <input type="checkbox" id="searchInCategory">
                            <span>البحث في التصنيف</span>
                        </label>
                    </div>
                </div>
                <div class="search-results" id="searchResults">
                    <div class="search-stats">
                        <span id="searchCount">0</span> نتيجة
        </div>
                </div>
            </div>
        </div>
        <section class="stats-bar">
            <div class="stat-box stat-total">المهام الكلية <span>0</span></div>
            <div class="stat-box stat-completed">المكتملة <span>0</span></div>
            <div class="stat-box stat-active">النشطة <span>0</span></div>
        </section>
        <section class="task-list" id="taskList">
            <!-- بطاقات المهام ستظهر هنا -->
            <div class="empty-state" id="emptyState">
                <i class="fas fa-tasks"></i>
                <p>لا توجد مهام مضافة بعد</p>
            </div>
        </section>
    </main>

    <!-- نافذة الإعدادات المنبثقة -->
    <div id="settingsModal" class="settings-modal-overlay" style="display:none;">
      <div class="settings-modal-box">
        <button class="settings-modal-close" id="closeSettingsModal" title="إغلاق"><i class="fas fa-times"></i></button>
        <h2 class="settings-title"><i class="fas fa-cog"></i> الإعدادات</h2>
        <div class="settings-sections">
          <!-- تخصيص الألوان والثيمات -->
          <section class="settings-section" id="section-theme">
            <button class="accordion-toggle" title="إظهار/إخفاء القسم"><i class="fas fa-chevron-down"></i></button>
            <h3><i class="fas fa-palette"></i> تخصيص الألوان والثيمات</h3>
            <div class="settings-section-content">
              <!-- الثيمات الجاهزة -->
              <div class="settings-row">
                <label data-tooltip="اختر ثيم جاهز للموقع">الثيم الجاهز:</label>
                <div class="theme-presets">
                  <button class="theme-preset" data-theme="default" title="الثيم الافتراضي">
                    <div class="preset-preview default-theme"></div>
                    <span>افتراضي</span>
                  </button>
                  <button class="theme-preset" data-theme="ocean" title="ثيم المحيط">
                    <div class="preset-preview ocean-theme"></div>
                    <span>محيط</span>
                  </button>
                  <button class="theme-preset" data-theme="forest" title="ثيم الغابة">
                    <div class="preset-preview forest-theme"></div>
                    <span>غابة</span>
                  </button>
                  <button class="theme-preset" data-theme="sunset" title="ثيم الغروب">
                    <div class="preset-preview sunset-theme"></div>
                    <span>غروب</span>
                  </button>
                  <button class="theme-preset" data-theme="purple" title="ثيم البنفسج">
                    <div class="preset-preview purple-theme"></div>
                    <span>بنفسج</span>
                  </button>
                </div>
              </div>
              
              <!-- اللون الرئيسي -->
              <div class="settings-row">
                <label data-tooltip="اختر اللون الرئيسي للموقع">اللون الرئيسي:</label>
                <div class="color-picker-container">
                  <select id="primaryColorSelect">
                    <option value="#1976d2">أزرق</option>
                    <option value="#43a047">أخضر</option>
                    <option value="#f44336">أحمر</option>
                    <option value="#ff9800">برتقالي</option>
                    <option value="#9c27b0">بنفسجي</option>
                    <option value="#00bcd4">فيروزي</option>
                    <option value="#ff5722">برتقالي محمر</option>
                    <option value="#795548">بني</option>
                    <option value="#607d8b">رمادي أزرق</option>
                    <option value="#e91e63">وردي</option>
                  </select>
                  <input type="color" id="customColorPicker" value="#1976d2" title="اختر لون مخصص">
                  <div class="color-preview" id="colorPreview"></div>
            </div>
        </div>

              <!-- الوضع الليلي/النهاري -->
              <div class="settings-row">
                <label data-tooltip="تغيير مظهر الموقع بالكامل">الوضع:</label>
                <div class="theme-mode-selector">
                  <button class="mode-btn active" data-mode="auto" title="تلقائي حسب النظام">
                    <i class="fas fa-magic"></i>
                    <span>تلقائي</span>
                  </button>
                  <button class="mode-btn" data-mode="light" title="وضع النهار">
                    <i class="fas fa-sun"></i>
                    <span>نهار</span>
                  </button>
                  <button class="mode-btn" data-mode="dark" title="وضع الليل">
                    <i class="fas fa-moon"></i>
                    <span>ليل</span>
                  </button>
                </div>
        </div>
        
              <!-- تخصيص متقدم -->
              <div class="settings-row">
                <label data-tooltip="تفعيل خيارات التخصيص المتقدمة"><input type="checkbox" id="advancedColors"> تفعيل التخصيص المتقدم</label>
              </div>
              
              <div class="advanced-colors" id="advancedColorsSection" style="display: none;">
                <div class="settings-row">
                  <label>لون الخلفية:</label>
                  <input type="color" id="backgroundColorPicker" value="#ffffff">
                </div>
                <div class="settings-row">
                  <label>لون النص:</label>
                  <input type="color" id="textColorPicker" value="#333333">
                </div>
                <div class="settings-row">
                  <label>لون الحدود:</label>
                  <input type="color" id="borderColorPicker" value="#e0e0e0">
                </div>
                <div class="settings-row">
                  <label>شفافية العناصر:</label>
                  <input type="range" id="opacitySlider" min="0.1" max="1" step="0.1" value="1">
                  <span id="opacityValue">100%</span>
                </div>
    </div>

              <!-- معاينة مباشرة -->
              <div class="settings-row">
                <label>معاينة مباشرة:</label>
                <div class="live-preview" id="livePreview">
                  <div class="preview-header">رأس الصفحة</div>
                  <div class="preview-sidebar">القائمة الجانبية</div>
                  <div class="preview-content">
                    <div class="preview-task">مهمة تجريبية</div>
                    <div class="preview-button">زر تجريبي</div>
                  </div>
                </div>
              </div>
            </div>
          </section>
          <!-- إعدادات الإشعارات -->
          <section class="settings-section" id="section-notif">
            <button class="accordion-toggle" title="إظهار/إخفاء القسم"><i class="fas fa-chevron-down"></i></button>
            <h3><i class="fas fa-bell"></i> إعدادات الإشعارات</h3>
            <div class="settings-section-content">
              <div class="settings-row">
                <label data-tooltip="تشغيل صوت عند إضافة أو حذف أو تعديل مهمة"><input type="checkbox" id="notifSound"> صوت عند إضافة/حذف/تعديل مهمة</label>
              </div>
              <div class="settings-row">
                <label>مكان الإشعار:</label>
                <select id="notifPosition">
                  <option value="bottom">أسفل الشاشة</option>
                  <option value="top">أعلى الشاشة</option>
                  <option value="center">منتصف الشاشة</option>
                </select>
              </div>
              <div class="settings-row">
                <label>مدة الإشعار:</label>
                <select id="notifDuration">
                  <option value="short">قصيرة</option>
                  <option value="normal">متوسطة</option>
                  <option value="long">طويلة</option>
                </select>
              </div>
            </div>
          </section>
          <!-- إعدادات المهام -->
          <section class="settings-section" id="section-tasks">
            <button class="accordion-toggle" title="إظهار/إخفاء القسم"><i class="fas fa-chevron-down"></i></button>
            <h3><i class="fas fa-tasks"></i> إعدادات المهام</h3>
            <div class="settings-section-content">
              <div class="settings-row">
                <label data-tooltip="تأكيد قبل حذف أي مهمة"><input type="checkbox" id="confirmDelete"> تأكيد عند حذف مهمة</label>
              </div>
              <div class="settings-row">
                <label data-tooltip="إظهار زر التراجع بعد الحذف"><input type="checkbox" id="undoDelete"> تفعيل التراجع عن الحذف</label>
              </div>
              <div class="settings-row">
                <label>طريقة العرض الافتراضية:</label>
                <select id="defaultView">
                  <option value="list">قائمة</option>
                  <option value="grid">شبكة</option>
                </select>
              </div>
              <div class="settings-row">
                <label data-tooltip="إضافة المهام كمكتملة مباشرة"><input type="checkbox" id="addCompleted"> السماح بإضافة المهام كمكتملة مباشرة</label>
              </div>
              <div class="settings-row">
                <label>ترتيب المهام الافتراضي:</label>
                <select id="defaultSort">
                  <option value="newest">الأحدث أولاً</option>
                  <option value="oldest">الأقدم أولاً</option>
                  <option value="priority">حسب الأولوية</option>
                  <option value="alphabetical">أبجدياً</option>
                </select>
              </div>
              <div class="settings-row">
                <label data-tooltip="إظهار تاريخ آخر تعديل للمهام"><input type="checkbox" id="showLastModified"> إظهار تاريخ آخر تعديل</label>
              </div>
              <div class="settings-row">
                <label data-tooltip="تفعيل الحفظ التلقائي للمهام"><input type="checkbox" id="autoSave"> الحفظ التلقائي</label>
              </div>
            </div>
          </section>
          <!-- إعدادات البحث -->
          <section class="settings-section" id="section-search">
            <button class="accordion-toggle" title="إظهار/إخفاء القسم"><i class="fas fa-chevron-down"></i></button>
            <h3><i class="fas fa-search"></i> إعدادات البحث</h3>
            <div class="settings-section-content">
              <div class="settings-row">
                <label data-tooltip="تفعيل البحث الفوري أثناء الكتابة"><input type="checkbox" id="instantSearch"> البحث الفوري أثناء الكتابة</label>
              </div>
              <div class="settings-row">
                <label>نطاق البحث:</label>
                <select id="searchScope">
                  <option value="text">نص المهمة فقط</option>
                  <option value="all">النص + التصنيف + التاريخ</option>
                </select>
              </div>
            </div>
          </section>
          <!-- إعدادات الواجهة -->
          <section class="settings-section" id="section-ui">
            <button class="accordion-toggle" title="إظهار/إخفاء القسم"><i class="fas fa-chevron-down"></i></button>
            <h3><i class="fas fa-desktop"></i> إعدادات الواجهة</h3>
            <div class="settings-section-content">
              <div class="settings-row">
                <label>حجم الخط:</label>
                <select id="fontSize">
                  <option value="small">صغير</option>
                  <option value="medium">متوسط</option>
                  <option value="large">كبير</option>
                </select>
              </div>
              <div class="settings-row">
                <label data-tooltip="تفعيل الحواف الدائرية للعناصر"><input type="checkbox" id="roundedBorders"> الحواف الدائرية</label>
              </div>
              <div class="settings-row">
                <label data-tooltip="تفعيل الظلال على الصناديق والعناصر"><input type="checkbox" id="shadows"> تفعيل الظلال</label>
              </div>
              <div class="settings-row">
                <label data-tooltip="تفعيل الحركات والمؤثرات البصرية"><input type="checkbox" id="animations"> تفعيل الحركات والمؤثرات</label>
              </div>
            </div>
          </section>
          <!-- إعدادات الحساب -->
          <section class="settings-section" id="section-account">
            <button class="accordion-toggle" title="إظهار/إخفاء القسم"><i class="fas fa-chevron-down"></i></button>
            <h3><i class="fas fa-user"></i> إعدادات الحساب</h3>
            <div class="settings-section-content">
              <div class="settings-row">
                <label>اسم المستخدم: <span id="currentUsername">admin</span></label>
                <button id="changeUsernameBtn" class="btn btn-outline">تغيير الاسم</button>
              </div>
              <div class="settings-row">
                <button id="changePasswordBtn" class="btn btn-outline">تغيير كلمة المرور</button>
              </div>
              <div class="settings-row">
                <button id="logoutAllBtn" class="btn btn-danger">تسجيل الخروج من جميع الأجهزة</button>
              </div>
            </div>
          </section>
          <!-- إعدادات متقدمة -->
          <section class="settings-section" id="section-advanced">
            <button class="accordion-toggle" title="إظهار/إخفاء القسم"><i class="fas fa-chevron-down"></i></button>
            <h3><i class="fas fa-cogs"></i> إعدادات متقدمة</h3>
            <div class="settings-section-content">
              <div class="settings-row">
                <button id="exportTasksBtn" class="btn btn-outline">تصدير المهام</button>
                <button id="importTasksBtn" class="btn btn-outline">استيراد المهام</button>
              </div>
              <div class="settings-row">
                <button id="resetSettingsBtn" class="btn btn-danger">إعادة تعيين الإعدادات</button>
                <button id="deleteAllTasksBtn" class="btn btn-danger">حذف جميع المهام</button>
              </div>
            </div>
          </section>
          <!-- حول التطبيق -->
          <section class="settings-section" id="section-about">
            <button class="accordion-toggle" title="إظهار/إخفاء القسم"><i class="fas fa-chevron-down"></i></button>
            <h3><i class="fas fa-info-circle"></i> حول التطبيق</h3>
            <div class="settings-section-content">
              <div class="settings-row">
                <span>الإصدار: <b id="appVersion">1.0</b></span>
              </div>
              <div class="settings-row">
                <a href="#" id="supportLink">الدعم الفني</a> |
                <a href="#" id="privacyLink">سياسة الخصوصية</a>
              </div>
            </div>
          </section>
        </div>
        <div class="settings-modal-actions">
          <button class="btn btn-save" id="saveSettingsBtn"><i class="fas fa-check"></i> حفظ</button>
          <button class="btn btn-cancel" id="cancelSettingsBtn"><i class="fas fa-times"></i> إلغاء</button>
            </div>
        </div>
    </div>

    <!-- واجهة البحث الجديدة -->
    <div id="searchInterface" class="search-interface-overlay">
        <div class="search-interface-modal">
            <!-- رأس واجهة البحث -->
            <div class="search-interface-header">
                <div class="search-interface-title">
                    <i class="fas fa-search"></i>
                    <h3>البحث في المهام</h3>
                </div>
                <button class="search-interface-close" id="closeSearchInterfaceBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- أزرار اختيار نوع البحث -->
            <div class="search-type-selector">
                <button class="search-type-btn active" data-type="quick" id="quickSearchBtn">
                    <i class="fas fa-bolt"></i>
                    <span>البحث السريع</span>
                </button>
                <button class="search-type-btn" data-type="advanced" id="advancedSearchBtn">
                    <i class="fas fa-cogs"></i>
                    <span>البحث المتقدم</span>
                </button>
            </div>

            <!-- محتوى البحث السريع -->
            <div id="quickSearchContent" class="search-content active">
                <div class="quick-search-container">
                    <div class="quick-search-input-wrapper">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" id="quickSearchInput" placeholder="اكتب نص المهمة للبحث..." class="quick-search-input">
                        <button class="clear-quick-search-btn" id="clearQuickSearchBtn">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="quick-search-hint">
                        <i class="fas fa-info-circle"></i>
                        <span>البحث الفوري في جميع المهام</span>
                    </div>
                </div>
                
                <!-- نتائج البحث السريع -->
                <div id="quickSearchResults" class="quick-search-results">
                    <div class="quick-results-header">
                        <h4>نتائج البحث</h4>
                        <span id="quickResultsCount" class="quick-results-count">0 نتيجة</span>
                    </div>
                    <div id="quickResultsList" class="quick-results-list">
                        <!-- ستظهر النتائج هنا -->
                    </div>
                </div>
            </div>

            <!-- محتوى البحث المتقدم -->
            <div id="advancedSearchContent" class="search-content">
                <div class="advanced-search-container">
                    <!-- رأس البحث المتقدم -->
                    <div class="advanced-search-header">
                        <div class="advanced-search-title">
                            <i class="fas fa-cogs"></i>
                            <h4>البحث المتقدم والديناميكي</h4>
                        </div>
                    </div>

                    <!-- منطقة إدخال البحث -->
                    <div class="advanced-search-input-area">
                        <div class="main-search-input">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" id="advancedSearchInput" placeholder="ابحث في المهام..." class="advanced-search-input">
                            <button class="clear-advanced-search-btn" id="clearAdvancedSearchBtn">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>

                    <!-- خيارات البحث -->
                    <div class="advanced-search-options">
                        <div class="search-filters">
                            <div class="filter-group">
                                <label>البحث في:</label>
                                <div class="filter-buttons">
                                    <button class="filter-btn active" data-filter="all">
                                        <i class="fas fa-list"></i>
                                        الكل
                                    </button>
                                    <button class="filter-btn" data-filter="title">
                                        <i class="fas fa-heading"></i>
                                        العنوان
                                    </button>
                                    <button class="filter-btn" data-filter="category">
                                        <i class="fas fa-tags"></i>
                                        التصنيف
                                    </button>
                                    <button class="filter-btn" data-filter="priority">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        الأولوية
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="advanced-search-options">
                            <div class="checkbox-option">
                                <input type="checkbox" id="searchCompleted" class="search-option">
                                <label for="searchCompleted" class="checkmark"></label>
                                <span>البحث في المهام المكتملة</span>
                            </div>
                            <div class="checkbox-option">
                                <input type="checkbox" id="searchPending" class="search-option" checked>
                                <label for="searchPending" class="checkmark"></label>
                                <span>البحث في المهام المعلقة</span>
                            </div>
                            <div class="checkbox-option">
                                <input type="checkbox" id="exactMatch" class="search-option">
                                <label for="exactMatch" class="checkmark"></label>
                                <span>مطابقة دقيقة</span>
                            </div>
                        </div>
                    </div>

                    <!-- منطقة النتائج -->
                    <div class="advanced-search-results-area">
                        <div class="results-header">
                            <div class="results-count">
                                <span id="advancedResultsCount">0 نتيجة</span>
                            </div>
                            <div class="results-actions">
                                <button class="action-btn" id="exportSearchResultsBtn">
                                    <i class="fas fa-download"></i>
                                    تصدير
                                </button>
                            </div>
                        </div>
                        <div id="advancedResultsList" class="results-list">
                            <!-- ستظهر النتائج هنا -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل المهمة المحسنة -->
    <div id="editTaskModal" class="edit-task-modal-overlay" style="display:none;">
        <div class="edit-task-modal-box">
            <button class="edit-task-modal-close" id="closeEditTaskModal" title="إغلاق">
                <i class="fas fa-times"></i>
            </button>
            <h2 class="edit-task-title">
                <i class="fas fa-edit"></i> تعديل المهمة
            </h2>
            <form id="editTaskForm" class="edit-task-form">
                <div class="edit-task-field">
                    <label for="editTaskText">نص المهمة:</label>
                    <textarea id="editTaskText" placeholder="أدخل نص المهمة..." required></textarea>
                </div>

                <div class="edit-task-row">
                    <div class="edit-task-field">
                        <label for="editTaskCategory">التصنيف:</label>
                        <select id="editTaskCategory">
                            <option value="عمل">عمل</option>
                            <option value="شخصي">شخصي</option>
                            <option value="دراسة">دراسة</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>

                    <div class="edit-task-field">
                        <label for="editTaskPriority">الأولوية:</label>
                        <select id="editTaskPriority">
                            <option value="low">منخفضة</option>
                            <option value="medium">متوسطة</option>
                            <option value="high">عالية</option>
                            <option value="urgent">عاجل</option>
                        </select>
                    </div>
                </div>

                <div class="edit-task-row">
                    <div class="edit-task-field">
                        <label for="editTaskDueDate">تاريخ الاستحقاق:</label>
                        <input type="date" id="editTaskDueDate">
                    </div>

                    <div class="edit-task-field">
                        <label for="editTaskCompleted">الحالة:</label>
                        <select id="editTaskCompleted">
                            <option value="false">غير مكتملة</option>
                            <option value="true">مكتملة</option>
                        </select>
                    </div>
                </div>

                <div class="edit-task-field">
                    <label for="editTaskNotes">ملاحظات إضافية:</label>
                    <textarea id="editTaskNotes" placeholder="أضف ملاحظات إضافية (اختياري)..."></textarea>
                </div>

                <div class="edit-task-actions">
                    <button type="button" id="cancelEditTask" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button type="submit" id="saveEditTask" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script type="module" src="js/auth.js"></script>
    <script type="module" src="js/utils.js"></script>
    <script type="module" src="js/app.js"></script>
</body>
</html>
