<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TDL - مدير المهام المتقدم</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap">
    <style>
        :root {
            --primary: #6366f1;
            --secondary: #8b5cf6;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --info: #06b6d4;
            --dark: #1a1a2e;
            --darker: #0f0f23;
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.1);
            --text-primary: #e0e0e0;
            --text-secondary: #a0a0a0;
            --text-muted: #6b7280;
            --sidebar-width: 320px;
            --header-height: 80px;
        }

        /* الوضع النهاري */
        [data-theme="light"] {
            --dark: #f8fafc;
            --darker: #ffffff;
            --glass-bg: rgba(255, 255, 255, 0.8);
            --glass-border: rgba(0, 0, 0, 0.1);
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-muted: #9ca3af;
        }

        [data-theme="light"] body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
        }

        [data-theme="light"] .floating-shapes .shape {
            background: linear-gradient(45deg, rgba(99, 102, 241, 0.05), rgba(139, 92, 246, 0.05));
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--darker) 0%, var(--dark) 50%, #16213e 100%);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* الخلفية المتحركة */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .shape {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(45deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
            animation: float 20s infinite linear;
        }

        .shape:nth-child(1) { width: 80px; height: 80px; top: 20%; left: 10%; animation-delay: 0s; }
        .shape:nth-child(2) { width: 120px; height: 120px; top: 60%; right: 10%; animation-delay: -5s; }
        .shape:nth-child(3) { width: 60px; height: 60px; bottom: 20%; left: 20%; animation-delay: -10s; }
        .shape:nth-child(4) { width: 100px; height: 100px; top: 40%; right: 30%; animation-delay: -15s; }

        @keyframes float {
            0% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
            50% { transform: translateY(-100px) rotate(180deg); opacity: 0.8; }
            100% { transform: translateY(0px) rotate(360deg); opacity: 0.3; }
        }

        /* تخطيط التطبيق */
        .app-layout {
            display: grid;
            grid-template-columns: var(--sidebar-width) 1fr;
            grid-template-rows: var(--header-height) 1fr;
            grid-template-areas: 
                "sidebar header"
                "sidebar main";
            min-height: 100vh;
        }

        /* الشريط العلوي */
        .app-header {
            grid-area: header;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            border-left: 1px solid var(--glass-border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 2rem;
            position: relative;
            overflow: hidden;
        }

        .app-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, transparent 50%, rgba(139, 92, 246, 0.1) 100%);
            z-index: -1;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .header-btn {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            color: var(--text-primary);
            padding: 0.8rem;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            font-size: 1.1rem;
        }

        .header-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .header-btn.primary {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
        }

        .header-btn.primary:hover {
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.4);
        }

        /* القائمة الجانبية المنفصلة */
        .sidebar {
            grid-area: sidebar;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-right: 1px solid var(--glass-border);
            padding: 2rem;
            overflow-y: auto;
            position: fixed;
            top: var(--header-height);
            right: 0;
            width: var(--sidebar-width);
            height: calc(100vh - var(--header-height));
            z-index: 100;
            transition: all 0.3s ease;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg, rgba(99, 102, 241, 0.05) 0%, transparent 50%, rgba(139, 92, 246, 0.05) 100%);
            z-index: -1;
        }

        .sidebar-section {
            margin-bottom: 2rem;
        }

        .sidebar-title {
            color: var(--primary);
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .user-profile {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 2rem;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 1.5rem;
            margin: 0 auto 1rem;
        }

        .user-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .user-email {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        /* المحتوى الرئيسي */
        .main-content {
            grid-area: main;
            padding: 2rem;
            overflow-y: auto;
            margin-right: var(--sidebar-width);
            transition: margin-right 0.3s ease;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .quick-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn.primary {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
        }

        .btn.success {
            background: linear-gradient(135deg, var(--success), #059669);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
        }

        /* إحصائيات القائمة الجانبية */
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            background: rgba(255, 255, 255, 0.05);
            transform: translateY(-2px);
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.1rem;
        }

        .stat-icon.success {
            background: linear-gradient(135deg, var(--success), #059669);
        }

        .stat-icon.warning {
            background: linear-gradient(135deg, var(--warning), #d97706);
        }

        .stat-icon.info {
            background: linear-gradient(135deg, var(--info), #0891b2);
        }

        .stat-number {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .stat-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        /* أزرار الفلاتر */
        .filter-options {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-btn {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--glass-border);
            color: var(--text-secondary);
            padding: 0.8rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            text-align: right;
        }

        .filter-btn:hover {
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-primary);
        }

        .filter-btn.active {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            border-color: var(--primary);
        }

        /* قائمة التصنيفات */
        .category-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--glass-border);
        }

        .category-name {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-secondary);
        }

        .category-count {
            background: var(--primary);
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        /* أدوات القائمة الجانبية */
        .tools-list {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .tool-btn {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--glass-border);
            color: var(--text-secondary);
            padding: 0.8rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            text-align: right;
        }

        .tool-btn:hover {
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-primary);
        }

        .tool-btn.danger {
            border-color: var(--danger);
            color: var(--danger);
        }

        .tool-btn.danger:hover {
            background: rgba(239, 68, 68, 0.1);
        }

        /* الإعدادات */
        .settings-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .setting-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.5rem 0;
        }

        .setting-item label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .setting-item input[type="checkbox"] {
            width: 40px;
            height: 20px;
            appearance: none;
            background: var(--glass-border);
            border-radius: 10px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .setting-item input[type="checkbox"]:checked {
            background: var(--primary);
        }

        .setting-item input[type="checkbox"]::before {
            content: '';
            position: absolute;
            top: 2px;
            right: 2px;
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .setting-item input[type="checkbox"]:checked::before {
            transform: translateX(-20px);
        }

        /* شريط البحث والفلترة */
        .search-filter-bar {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-container {
            flex: 1;
            position: relative;
            min-width: 250px;
        }

        .search-container i {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
        }

        .search-container input {
            width: 100%;
            padding: 1rem 3rem 1rem 1rem;
            border: 2px solid var(--glass-border);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-primary);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .search-container input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }

        .search-clear {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-muted);
            cursor: pointer;
            padding: 0.2rem;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .search-clear:hover {
            color: var(--danger);
            background: rgba(239, 68, 68, 0.1);
        }

        .filter-controls {
            display: flex;
            gap: 1rem;
        }

        .filter-controls select {
            padding: 0.8rem 1rem;
            border: 2px solid var(--glass-border);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-controls select:focus {
            outline: none;
            border-color: var(--primary);
        }

        /* حاوي المهام */
        .tasks-container {
            min-height: 400px;
        }

        /* الحالة الفارغة */
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--text-secondary);
        }

        .empty-icon {
            font-size: 4rem;
            color: var(--text-muted);
            margin-bottom: 1rem;
        }

        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .empty-state p {
            margin-bottom: 2rem;
        }

        /* التصميم المتجاوب */
        @media (max-width: 768px) {
            .app-layout {
                grid-template-columns: 1fr;
                grid-template-rows: var(--header-height) 1fr;
                grid-template-areas:
                    "header"
                    "main";
            }

            .sidebar {
                position: fixed;
                top: var(--header-height);
                right: -100%;
                width: 280px;
                height: calc(100vh - var(--header-height));
                z-index: 1000;
                transition: right 0.3s ease;
            }

            .sidebar.open {
                right: 0;
            }

            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 999;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }

            .sidebar-overlay.active {
                opacity: 1;
                visibility: visible;
            }

            .search-filter-bar {
                flex-direction: column;
                align-items: stretch;
            }

            .filter-controls {
                justify-content: space-between;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        /* نافذة المودال */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(8px);
            z-index: 2000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            max-width: 600px;
            width: 100%;
            max-height: 90vh;
            overflow-y: auto;
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            padding: 2rem 2rem 1rem;
            border-bottom: 1px solid var(--glass-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            color: var(--text-primary);
            font-size: 1.5rem;
            font-weight: 700;
        }

        .modal-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            color: var(--danger);
            background: rgba(239, 68, 68, 0.1);
        }

        .modal-body {
            padding: 2rem;
        }

        .modal-footer {
            padding: 1rem 2rem 2rem;
            border-top: 1px solid var(--glass-border);
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }

        /* نموذج إضافة المهمة */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
            font-weight: 600;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 1rem;
            border: 2px solid var(--glass-border);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-primary);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .form-group input[type="checkbox"] {
            width: auto;
            margin-left: 0.5rem;
        }

        /* بطاقات المهام */
        .task-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .task-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--primary);
        }

        .task-card.priority-high::before {
            background: var(--danger);
        }

        .task-card.priority-medium::before {
            background: var(--warning);
        }

        .task-card.priority-low::before {
            background: var(--success);
        }

        .task-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .task-card.completed {
            opacity: 0.7;
        }

        .task-header {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .task-checkbox {
            width: 24px;
            height: 24px;
            border: 2px solid var(--primary);
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            flex-shrink: 0;
            margin-top: 0.2rem;
        }

        .task-checkbox.checked {
            background: var(--primary);
            color: white;
        }

        .task-content {
            flex: 1;
        }

        .task-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .task-title.completed {
            text-decoration: line-through;
            color: var(--text-secondary);
        }

        .task-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 1rem;
            line-height: 1.5;
        }

        .task-meta {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
            margin-bottom: 1rem;
        }

        .task-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .task-badge.priority {
            background: var(--primary);
            color: white;
        }

        .task-badge.category {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-secondary);
        }

        .task-badge.due-date {
            background: var(--info);
            color: white;
        }

        .task-badge.overdue {
            background: var(--danger);
            color: white;
        }

        .task-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: flex-end;
        }

        .task-action-btn {
            padding: 0.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .task-action-btn.edit {
            background: var(--info);
            color: white;
        }

        .task-action-btn.delete {
            background: var(--danger);
            color: white;
        }

        .task-action-btn:hover {
            transform: scale(1.1);
        }

        /* عرض الشبكة */
        .grid-view {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        /* عرض كانبان */
        .kanban-view {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .kanban-column {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 15px;
            padding: 1.5rem;
        }

        .kanban-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .kanban-count {
            background: var(--primary);
            color: white;
            padding: 0.2rem 0.6rem;
            border-radius: 12px;
            font-size: 0.8rem;
        }

        /* نوافذ التأكيد والتحذير */
        .confirmation-modal,
        .alert-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(12px);
            z-index: 3000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            animation: fadeIn 0.3s ease;
        }

        .confirmation-modal.show,
        .alert-modal.show {
            display: flex;
        }

        .confirmation-content,
        .alert-content {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            max-width: 500px;
            width: 100%;
            overflow: hidden;
            animation: modalSlideIn 0.4s ease;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        }

        .confirmation-header,
        .alert-header {
            padding: 2rem 2rem 1rem;
            text-align: center;
            position: relative;
        }

        .confirmation-icon,
        .alert-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2.5rem;
            background: linear-gradient(135deg, var(--warning), #d97706);
            color: white;
            animation: pulse 2s infinite;
        }

        .alert-icon.success {
            background: linear-gradient(135deg, var(--success), #059669);
        }

        .alert-icon.error {
            background: linear-gradient(135deg, var(--danger), #dc2626);
        }

        .alert-icon.info {
            background: linear-gradient(135deg, var(--info), #0891b2);
        }

        .confirmation-header h3,
        .alert-header h3 {
            color: var(--text-primary);
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
        }

        .alert-close {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .alert-close:hover {
            color: var(--danger);
            background: rgba(239, 68, 68, 0.1);
        }

        .confirmation-body,
        .alert-body {
            padding: 1rem 2rem;
            text-align: center;
        }

        .confirmation-body p,
        .alert-body p {
            color: var(--text-primary);
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .confirmation-details,
        .alert-details {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .confirmation-footer,
        .alert-footer {
            padding: 1rem 2rem 2rem;
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .confirmation-footer .btn,
        .alert-footer .btn {
            min-width: 120px;
            padding: 1rem 1.5rem;
            font-weight: 600;
        }

        /* تأثيرات الأنيميشن */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* تحسينات أزرار المهام */
        .task-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: flex-end;
            margin-top: 1rem;
        }

        .task-action-btn {
            padding: 0.6rem 0.8rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.3rem;
            font-weight: 500;
        }

        .task-action-btn.edit {
            background: linear-gradient(135deg, var(--info), #0891b2);
            color: white;
        }

        .task-action-btn.delete {
            background: linear-gradient(135deg, var(--danger), #dc2626);
            color: white;
        }

        .task-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .task-action-btn.edit:hover {
            box-shadow: 0 4px 15px rgba(6, 182, 212, 0.4);
        }

        .task-action-btn.delete:hover {
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
        }

        /* تحسينات إضافية للمهام */
        .task-card {
            position: relative;
            overflow: hidden;
        }

        .task-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, transparent 0%, rgba(99, 102, 241, 0.05) 100%);
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .task-card:hover::after {
            opacity: 1;
        }

        .task-title {
            position: relative;
            z-index: 1;
        }

        .task-meta {
            position: relative;
            z-index: 1;
        }

        /* تحسينات إضافية للواجهة */
        .task-action-btn span {
            display: none;
        }

        @media (min-width: 768px) {
            .task-action-btn span {
                display: inline;
                margin-right: 0.3rem;
            }
        }

        /* تأثيرات تحميل */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            z-index: 4000;
            display: none;
            align-items: center;
            justify-content: center;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid var(--glass-border);
            border-top: 4px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* تحسينات الإشعارات */
        .notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 3500;
            pointer-events: none;
        }

        .notification-item {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 1rem 1.5rem;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
            animation: slideInRight 0.3s ease;
            max-width: 350px;
            pointer-events: auto;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .notification-item:hover {
            transform: translateX(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .notification-item.success {
            border-left: 4px solid var(--success);
        }

        .notification-item.error {
            border-left: 4px solid var(--danger);
        }

        .notification-item.info {
            border-left: 4px solid var(--info);
        }

        .notification-item.warning {
            border-left: 4px solid var(--warning);
        }

        /* تحسينات الأزرار */
        .btn {
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.3s ease, height 0.3s ease;
        }

        .btn:active::before {
            width: 300px;
            height: 300px;
        }

        /* تحسينات النماذج */
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.2);
        }

        /* تحسينات القائمة الجانبية */
        .sidebar-section {
            animation: fadeInUp 0.5s ease;
        }

        .sidebar-section:nth-child(2) { animation-delay: 0.1s; }
        .sidebar-section:nth-child(3) { animation-delay: 0.2s; }
        .sidebar-section:nth-child(4) { animation-delay: 0.3s; }
        .sidebar-section:nth-child(5) { animation-delay: 0.4s; }
        .sidebar-section:nth-child(6) { animation-delay: 0.5s; }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* تحسينات المهام */
        .task-card {
            animation: fadeInUp 0.3s ease;
        }

        .task-card:nth-child(1) { animation-delay: 0.05s; }
        .task-card:nth-child(2) { animation-delay: 0.1s; }
        .task-card:nth-child(3) { animation-delay: 0.15s; }
        .task-card:nth-child(4) { animation-delay: 0.2s; }
        .task-card:nth-child(5) { animation-delay: 0.25s; }

        /* تحسينات الحالة الفارغة */
        .empty-state {
            animation: fadeIn 0.5s ease;
        }

        .empty-icon {
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        /* تحسينات القائمة الجانبية */
        .user-section {
            margin-top: auto;
            border-top: 1px solid var(--glass-border);
            padding-top: 1rem;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .user-profile:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .user-info {
            flex: 1;
        }

        .user-menu-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            padding: 0.5rem;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .user-menu-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
        }

        /* القوائم المنبثقة */
        .dropdown-container {
            position: relative;
        }

        .dropdown-trigger {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        .dropdown-arrow {
            transition: transform 0.3s ease;
            font-size: 0.8rem;
        }

        .dropdown-trigger.active .dropdown-arrow {
            transform: rotate(180deg);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 0.5rem;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .dropdown-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            width: 100%;
            padding: 0.8rem 1rem;
            background: none;
            border: none;
            color: var(--text-secondary);
            text-align: right;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .dropdown-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            transform: translateX(-3px);
        }

        .dropdown-item i {
            width: 16px;
            text-align: center;
        }

        /* رأس المحتوى المحسن */
        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--glass-border);
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .search-toggle-btn {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            color: var(--text-primary);
            padding: 0.8rem;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
        }

        .search-toggle-btn:hover {
            background: var(--primary);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.4);
        }

        .view-selector {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            color: var(--text-primary);
            padding: 0.8rem 1rem;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: left 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-left: 2.5rem;
        }

        .view-selector:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
            transform: translateY(-2px);
        }

        .view-selector:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--primary);
        }

        .view-selector option {
            background: var(--darker);
            color: var(--text-primary);
            padding: 0.5rem;
            border: none;
        }

        /* صندوق الإضافة السريع */
        .quick-add-container {
            margin-bottom: 2rem;
        }

        .quick-add-box {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 15px;
            padding: 1.5rem;
            display: flex;
            gap: 1rem;
            align-items: center;
            transition: all 0.3s ease;
        }

        .quick-add-box:hover {
            border-color: var(--primary);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.2);
        }

        .quick-add-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 1rem 1.5rem;
            color: var(--text-primary);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .quick-add-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
            background: rgba(255, 255, 255, 0.08);
        }

        .quick-add-btn,
        .advanced-add-btn {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border: none;
            color: white;
            padding: 1rem;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .advanced-add-btn {
            background: var(--glass-bg);
            color: var(--text-primary);
            border: 1px solid var(--glass-border);
        }

        .quick-add-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .advanced-add-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        /* لوحة البحث المتقدم */
        .search-panel {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            margin-bottom: 2rem;
            overflow: hidden;
            max-height: 0;
            opacity: 0;
            transition: all 0.5s ease;
        }

        .search-panel.show {
            max-height: 800px;
            opacity: 1;
        }

        .search-panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid var(--glass-border);
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
        }

        .search-panel-header h3 {
            color: var(--text-primary);
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0;
        }

        .search-panel-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .search-panel-close:hover {
            background: rgba(239, 68, 68, 0.2);
            color: var(--danger);
        }

        .search-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.03);
        }

        .search-tab {
            flex: 1;
            background: none;
            border: none;
            color: var(--text-secondary);
            padding: 1rem 2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            border-bottom: 3px solid transparent;
        }

        .search-tab.active {
            color: var(--primary);
            background: rgba(99, 102, 241, 0.1);
            border-bottom-color: var(--primary);
        }

        .search-tab:hover:not(.active) {
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-primary);
        }

        .search-content {
            padding: 2rem;
        }

        /* البحث السريع */
        .search-input-container {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .search-input-container i {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
            font-size: 1.1rem;
        }

        .search-input-container input {
            width: 100%;
            padding: 1rem 3rem 1rem 1rem;
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid var(--glass-border);
            border-radius: 12px;
            color: var(--text-primary);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .search-input-container input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
            background: rgba(255, 255, 255, 0.08);
        }

        .search-clear-btn {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-muted);
            cursor: pointer;
            padding: 0.3rem;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .search-clear-btn:hover {
            background: rgba(239, 68, 68, 0.2);
            color: var(--danger);
        }

        .quick-search-results {
            max-height: 400px;
            overflow-y: auto;
        }

        /* البحث المتقدم */
        .advanced-search-form {
            margin-bottom: 2rem;
        }

        .search-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .search-field {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .search-field label {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 0.9rem;
        }

        .search-field input,
        .search-field select {
            padding: 0.8rem 1rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 8px;
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .search-field input:focus,
        .search-field select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
        }

        .search-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 1.5rem;
        }

        /* نتائج البحث المتقدم */
        .advanced-search-results {
            border-top: 1px solid var(--glass-border);
            padding-top: 1.5rem;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 10px;
        }

        .results-count {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1.1rem;
        }

        .results-actions {
            display: flex;
            gap: 1rem;
        }

        .advanced-search-results-list {
            max-height: 500px;
            overflow-y: auto;
        }

        /* تحسينات المهام في نتائج البحث */
        .search-result-item {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 0.8rem;
            transition: all 0.3s ease;
        }

        .search-result-item:hover {
            background: rgba(255, 255, 255, 0.05);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .search-result-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.5rem;
        }

        .search-result-title {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 1rem;
        }

        .search-result-actions {
            display: flex;
            gap: 0.5rem;
        }

        .search-result-btn {
            background: none;
            border: 1px solid var(--glass-border);
            color: var(--text-secondary);
            padding: 0.4rem 0.6rem;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.8rem;
        }

        .search-result-btn:hover {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        .search-result-btn.complete {
            background: var(--success);
            color: white;
            border-color: var(--success);
        }

        .search-result-btn.delete {
            background: var(--danger);
            color: white;
            border-color: var(--danger);
        }

        /* تحسينات النموذج المتقدم */
        .form-options {
            display: flex;
            gap: 2rem;
            margin: 1.5rem 0;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 10px;
            border: 1px solid var(--glass-border);
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            cursor: pointer;
            color: var(--text-primary);
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .checkbox-label:hover {
            color: var(--primary);
        }

        .checkbox-label input[type="checkbox"] {
            display: none;
        }

        .checkmark {
            width: 20px;
            height: 20px;
            border: 2px solid var(--glass-border);
            border-radius: 4px;
            position: relative;
            transition: all 0.3s ease;
        }

        .checkbox-label input[type="checkbox"]:checked + .checkmark {
            background: var(--primary);
            border-color: var(--primary);
        }

        .checkbox-label input[type="checkbox"]:checked + .checkmark::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            font-size: 12px;
        }

        .recurring-options {
            background: rgba(99, 102, 241, 0.1);
            border: 1px solid var(--primary);
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
                padding: 0 1rem;
            }
            to {
                opacity: 1;
                max-height: 200px;
                padding: 1rem;
            }
        }

        /* تحسينات التصميم المتجاوب */
        @media (max-width: 768px) {
            .search-row {
                grid-template-columns: 1fr;
            }

            .form-options {
                flex-direction: column;
                gap: 1rem;
            }

            .header-actions {
                flex-direction: column;
                gap: 0.5rem;
            }

            .quick-add-box {
                flex-direction: column;
                gap: 1rem;
            }

            .quick-add-input {
                order: 1;
            }

            .quick-add-btn,
            .advanced-add-btn {
                order: 2;
                width: 100%;
            }

            .search-panel {
                margin: 0 -1rem;
                border-radius: 0;
            }

            .dropdown-menu {
                position: fixed;
                top: auto;
                bottom: 0;
                left: 0;
                right: 0;
                border-radius: 20px 20px 0 0;
                max-height: 50vh;
                overflow-y: auto;
            }
        }

        /* تحسينات الأداء */
        .sidebar-section,
        .task-card,
        .search-result-item {
            will-change: transform;
        }

        /* تحسينات تفاعلية متقدمة */
        .header-btn {
            position: relative;
            overflow: hidden;
        }

        .header-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.6s ease;
        }

        .header-btn:active::before {
            width: 300px;
            height: 300px;
        }

        .btn {
            position: relative;
            overflow: hidden;
            transform: perspective(1px) translateZ(0);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .task-card {
            transform: translateZ(0);
            backface-visibility: hidden;
        }

        .task-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
        }

        .sidebar-section {
            transform: translateZ(0);
        }

        .sidebar-section:hover {
            transform: translateX(-5px);
        }

        /* تأثيرات النقر */
        .clickable {
            transition: all 0.1s ease;
        }

        .clickable:active {
            transform: scale(0.95);
        }

        /* تحسينات الفوكس */
        .form-control:focus,
        .btn:focus,
        .header-btn:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
        }

        /* تأثيرات الهوفر المتقدمة */
        .filter-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(99, 102, 241, 0.4);
        }

        .category-item:hover {
            transform: translateX(-3px);
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
        }

        /* تحسينات الانتقالات */
        * {
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        }

        .smooth-transition {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* تحسينات إمكانية الوصول */
        .dropdown-trigger:focus,
        .search-toggle-btn:focus,
        .quick-add-btn:focus {
            outline: 2px solid var(--primary);
            outline-offset: 2px;
        }

        /* تأثيرات التحميل */
        .loading-skeleton {
            background: linear-gradient(90deg,
                rgba(255, 255, 255, 0.1) 25%,
                rgba(255, 255, 255, 0.2) 50%,
                rgba(255, 255, 255, 0.1) 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% {
                background-position: 200% 0;
            }
            100% {
                background-position: -200% 0;
            }
        }

        /* نظام الإشعارات الداخلية */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 5000;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            pointer-events: none;
        }

        .toast {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            padding: 1rem 1.5rem;
            color: var(--text-primary);
            min-width: 300px;
            max-width: 400px;
            pointer-events: auto;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            position: relative;
            overflow: hidden;
        }

        .toast.show {
            transform: translateX(0);
            opacity: 1;
        }

        .toast.success {
            border-left: 4px solid var(--success);
        }

        .toast.error {
            border-left: 4px solid var(--danger);
        }

        .toast.warning {
            border-left: 4px solid var(--warning);
        }

        .toast.info {
            border-left: 4px solid var(--info);
        }

        .toast::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255,255,255,0.1), transparent);
            pointer-events: none;
        }

        .toast-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .toast-icon {
            font-size: 1.5rem;
            flex-shrink: 0;
        }

        .toast-message {
            flex: 1;
        }

        .toast-title {
            font-weight: 600;
            margin-bottom: 0.2rem;
        }

        .toast-text {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .toast-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0.2rem;
            border-radius: 4px;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .toast-close:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
        }

        /* نافذة تأكيد الانتقال */
        .navigation-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(8px);
            z-index: 4000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .navigation-modal.show {
            display: flex;
        }

        .navigation-content {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            max-width: 400px;
            width: 100%;
            overflow: hidden;
            animation: modalSlideIn 0.4s ease;
        }

        .navigation-header {
            padding: 2rem 2rem 1rem;
            text-align: center;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
        }

        .navigation-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .navigation-header h3 {
            color: var(--text-primary);
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0;
        }

        .navigation-body {
            padding: 1rem 2rem;
            text-align: center;
        }

        .navigation-body p {
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .navigation-note {
            color: var(--text-secondary);
            font-size: 0.9rem;
            font-style: italic;
        }

        .navigation-footer {
            padding: 1rem 2rem 2rem;
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        /* تحسينات الطباعة */
        @media print {
            .sidebar,
            .search-panel,
            .quick-add-container,
            .header-actions,
            .toast-container,
            .navigation-modal {
                display: none !important;
            }

            .main-content {
                grid-column: 1 / -1;
                padding: 0;
                margin-right: 0;
            }

            .task-card {
                break-inside: avoid;
                border: 1px solid #000;
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- الخلفية المتحركة -->
    <div class="animated-bg">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
    </div>

    <!-- تخطيط التطبيق -->
    <div class="app-layout">
        <!-- الشريط العلوي -->
        <header class="app-header">
            <div class="header-title">
                <i class="fas fa-tasks"></i>
                <span>TDL - مدير المهام</span>
            </div>
            <div class="header-actions">
                <button class="header-btn" onclick="showNavigationModal()" title="الصفحة الرئيسية">
                    <i class="fas fa-home"></i>
                </button>
                <button class="header-btn" onclick="toggleTheme()" id="themeToggle" title="تبديل الوضع">
                    <i class="fas fa-moon"></i>
                </button>
                <button class="header-btn" id="sidebarToggle" title="القائمة الجانبية">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </header>

        <!-- القائمة الجانبية -->
        <aside class="sidebar" id="sidebar">
            <!-- الإحصائيات السريعة -->
            <div class="sidebar-section">
                <h3 class="sidebar-title">
                    <i class="fas fa-chart-bar"></i>
                    الإحصائيات
                </h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="totalTasks">0</div>
                            <div class="stat-label">إجمالي المهام</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="completedTasks">0</div>
                            <div class="stat-label">مكتملة</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon warning">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="pendingTasks">0</div>
                            <div class="stat-label">معلقة</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon info">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="completionRate">0%</div>
                            <div class="stat-label">معدل الإنجاز</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الفلاتر -->
            <div class="sidebar-section">
                <h3 class="sidebar-title">
                    <i class="fas fa-filter"></i>
                    الفلاتر
                </h3>
                <div class="filter-options">
                    <button class="filter-btn active" data-filter="all">
                        <i class="fas fa-list"></i>
                        جميع المهام
                    </button>
                    <button class="filter-btn" data-filter="pending">
                        <i class="fas fa-clock"></i>
                        المعلقة
                    </button>
                    <button class="filter-btn" data-filter="completed">
                        <i class="fas fa-check"></i>
                        المكتملة
                    </button>
                    <button class="filter-btn" data-filter="important">
                        <i class="fas fa-star"></i>
                        المهمة
                    </button>
                    <button class="filter-btn" data-filter="overdue">
                        <i class="fas fa-exclamation-triangle"></i>
                        متأخرة
                    </button>
                    <button class="filter-btn" data-filter="today">
                        <i class="fas fa-calendar-day"></i>
                        اليوم
                    </button>
                    <button class="filter-btn" data-filter="week">
                        <i class="fas fa-calendar-week"></i>
                        هذا الأسبوع
                    </button>
                    <button class="filter-btn" data-filter="recurring">
                        <i class="fas fa-redo"></i>
                        متكررة
                    </button>
                </div>
            </div>

            <!-- التصنيفات -->
            <div class="sidebar-section">
                <h3 class="sidebar-title">
                    <i class="fas fa-tags"></i>
                    التصنيفات
                </h3>
                <div class="categories-list" id="categoriesList">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>

            <!-- الأدوات -->
            <div class="sidebar-section">
                <h3 class="sidebar-title">
                    <i class="fas fa-tools"></i>
                    الأدوات
                </h3>
                <div class="tools-list">
                    <div class="dropdown-container">
                        <button class="tool-btn dropdown-trigger" data-dropdown="exportDropdown">
                            <i class="fas fa-download"></i>
                            تصدير المهام
                            <i class="fas fa-chevron-down dropdown-arrow"></i>
                        </button>
                        <div class="dropdown-menu" id="exportDropdown">
                            <button class="dropdown-item" onclick="taskManager.exportTasks('txt')">
                                <i class="fas fa-file-alt"></i>
                                تصدير بصيغة TXT
                            </button>
                            <button class="dropdown-item" onclick="taskManager.exportTasks('csv')">
                                <i class="fas fa-file-csv"></i>
                                تصدير بصيغة CSV
                            </button>
                            <button class="dropdown-item" onclick="taskManager.exportTasks('json')">
                                <i class="fas fa-file-code"></i>
                                تصدير بصيغة JSON
                            </button>
                        </div>
                    </div>
                    <div class="dropdown-container">
                        <button class="tool-btn dropdown-trigger" data-dropdown="importDropdown">
                            <i class="fas fa-upload"></i>
                            استيراد المهام
                            <i class="fas fa-chevron-down dropdown-arrow"></i>
                        </button>
                        <div class="dropdown-menu" id="importDropdown">
                            <button class="dropdown-item" onclick="taskManager.importTasks('txt')">
                                <i class="fas fa-file-alt"></i>
                                استيراد من TXT
                            </button>
                            <button class="dropdown-item" onclick="taskManager.importTasks('csv')">
                                <i class="fas fa-file-csv"></i>
                                استيراد من CSV
                            </button>
                            <button class="dropdown-item" onclick="taskManager.importTasks('json')">
                                <i class="fas fa-file-code"></i>
                                استيراد من JSON
                            </button>
                        </div>
                    </div>
                    <button class="tool-btn" onclick="clearCompleted()">
                        <i class="fas fa-broom"></i>
                        مسح المكتملة
                    </button>
                    <button class="tool-btn danger" onclick="clearAll()">
                        <i class="fas fa-trash"></i>
                        مسح الكل
                    </button>
                    <div class="dropdown-container">
                        <button class="tool-btn dropdown-trigger" data-dropdown="analyticsDropdown">
                            <i class="fas fa-chart-line"></i>
                            التحليلات
                            <i class="fas fa-chevron-down dropdown-arrow"></i>
                        </button>
                        <div class="dropdown-menu" id="analyticsDropdown">
                            <button class="dropdown-item" onclick="showProductivityReport()">
                                <i class="fas fa-chart-bar"></i>
                                تقرير الإنتاجية
                            </button>
                            <button class="dropdown-item" onclick="showTimeAnalysis()">
                                <i class="fas fa-clock"></i>
                                تحليل الوقت
                            </button>
                            <button class="dropdown-item" onclick="showCategoryStats()">
                                <i class="fas fa-pie-chart"></i>
                                إحصائيات التصنيفات
                            </button>
                        </div>
                    </div>
                    <button class="tool-btn" onclick="backupData()">
                        <i class="fas fa-shield-alt"></i>
                        نسخ احتياطي
                    </button>
                    <button class="tool-btn" onclick="syncData()">
                        <i class="fas fa-sync"></i>
                        مزامنة البيانات
                    </button>
                </div>
            </div>

            <!-- إعدادات سريعة -->
            <div class="sidebar-section">
                <h3 class="sidebar-title">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </h3>
                <div class="settings-list">
                    <div class="setting-item">
                        <label for="autoSave">حفظ تلقائي</label>
                        <input type="checkbox" id="autoSave" checked>
                    </div>
                    <div class="setting-item">
                        <label for="notifications">الإشعارات</label>
                        <input type="checkbox" id="notifications">
                    </div>
                    <div class="setting-item">
                        <label for="soundEffects">الأصوات</label>
                        <input type="checkbox" id="soundEffects">
                    </div>
                    <div class="setting-item">
                        <label for="compactMode">الوضع المضغوط</label>
                        <input type="checkbox" id="compactMode">
                    </div>
                    <div class="setting-item">
                        <label for="animationsEnabled">تفعيل الأنيميشن</label>
                        <input type="checkbox" id="animationsEnabled" checked>
                    </div>
                    <div class="setting-item">
                        <label for="autoBackup">نسخ احتياطي تلقائي</label>
                        <input type="checkbox" id="autoBackup">
                    </div>
                    <div class="setting-item">
                        <label for="smartNotifications">إشعارات ذكية</label>
                        <input type="checkbox" id="smartNotifications">
                    </div>
                </div>
            </div>

            <!-- ملف المستخدم في الأسفل -->
            <div class="sidebar-section user-section">
                <div class="user-profile">
                    <div class="user-avatar" id="userAvatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-info">
                        <div class="user-name" id="userName">مرحباً بك!</div>
                        <div class="user-email" id="userEmail">يرجى تسجيل الدخول</div>
                    </div>
                    <button class="user-menu-btn" onclick="showUserMenu()">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>

            <!-- الإحصائيات السريعة -->
            <div class="sidebar-section">
                <h3 class="sidebar-title">
                    <i class="fas fa-chart-bar"></i>
                    الإحصائيات
                </h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="totalTasks">0</div>
                            <div class="stat-label">إجمالي المهام</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="completedTasks">0</div>
                            <div class="stat-label">مكتملة</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon warning">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="pendingTasks">0</div>
                            <div class="stat-label">معلقة</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon info">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="completionRate">0%</div>
                            <div class="stat-label">معدل الإنجاز</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الفلاتر -->
            <div class="sidebar-section">
                <h3 class="sidebar-title">
                    <i class="fas fa-filter"></i>
                    الفلاتر
                </h3>
                <div class="filter-options">
                    <button class="filter-btn active" data-filter="all">
                        <i class="fas fa-list"></i>
                        جميع المهام
                    </button>
                    <button class="filter-btn" data-filter="pending">
                        <i class="fas fa-clock"></i>
                        المعلقة
                    </button>
                    <button class="filter-btn" data-filter="completed">
                        <i class="fas fa-check"></i>
                        المكتملة
                    </button>
                    <button class="filter-btn" data-filter="important">
                        <i class="fas fa-star"></i>
                        المهمة
                    </button>
                </div>
            </div>

            <!-- التصنيفات -->
            <div class="sidebar-section">
                <h3 class="sidebar-title">
                    <i class="fas fa-tags"></i>
                    التصنيفات
                </h3>
                <div class="categories-list" id="categoriesList">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>

            <!-- الأدوات -->
            <div class="sidebar-section">
                <h3 class="sidebar-title">
                    <i class="fas fa-tools"></i>
                    الأدوات
                </h3>
                <div class="tools-list">
                    <button class="tool-btn" onclick="exportTasks()">
                        <i class="fas fa-download"></i>
                        تصدير المهام
                    </button>
                    <button class="tool-btn" onclick="importTasks()">
                        <i class="fas fa-upload"></i>
                        استيراد المهام
                    </button>
                    <button class="tool-btn" onclick="clearCompleted()">
                        <i class="fas fa-broom"></i>
                        مسح المكتملة
                    </button>
                    <button class="tool-btn danger" onclick="clearAll()">
                        <i class="fas fa-trash"></i>
                        مسح الكل
                    </button>
                </div>
            </div>

            <!-- إعدادات سريعة -->
            <div class="sidebar-section">
                <h3 class="sidebar-title">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </h3>
                <div class="settings-list">
                    <div class="setting-item">
                        <label for="autoSave">حفظ تلقائي</label>
                        <input type="checkbox" id="autoSave" checked>
                    </div>
                    <div class="setting-item">
                        <label for="notifications">الإشعارات</label>
                        <input type="checkbox" id="notifications">
                    </div>
                    <div class="setting-item">
                        <label for="soundEffects">الأصوات</label>
                        <input type="checkbox" id="soundEffects">
                    </div>
                </div>
            </div>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- رأس المحتوى -->
            <div class="content-header">
                <h1 class="page-title">مهامي</h1>
                <div class="header-actions">
                    <button class="search-toggle-btn" onclick="toggleSearchPanel()" title="البحث المتقدم">
                        <i class="fas fa-search"></i>
                    </button>
                    <div class="view-controls">
                        <select id="viewMode" class="view-selector">
                            <option value="list">قائمة</option>
                            <option value="grid">شبكة</option>
                            <option value="kanban">كانبان</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- صندوق إضافة المهام السريع -->
            <div class="quick-add-container">
                <div class="quick-add-box">
                    <input type="text" id="quickAddInput" placeholder="أضف مهمة جديدة..." class="quick-add-input">
                    <button class="quick-add-btn" onclick="quickAddTask()">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="advanced-add-btn" onclick="showAddTaskModal()" title="إضافة متقدمة">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>

            <!-- لوحة البحث المتقدم -->
            <div class="search-panel" id="searchPanel">
                <div class="search-panel-header">
                    <h3>البحث في المهام</h3>
                    <button class="search-panel-close" onclick="toggleSearchPanel()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="search-tabs">
                    <button class="search-tab active" data-tab="quick">بحث سريع</button>
                    <button class="search-tab" data-tab="advanced">بحث متقدم</button>
                </div>

                <!-- البحث السريع -->
                <div class="search-content" id="quickSearch">
                    <div class="search-input-container">
                        <i class="fas fa-search"></i>
                        <input type="text" id="quickSearchInput" placeholder="ابحث في العناوين والأوصاف...">
                        <button class="search-clear-btn" onclick="clearQuickSearch()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="quick-search-results" id="quickSearchResults">
                        <!-- نتائج البحث السريع -->
                    </div>
                </div>

                <!-- البحث المتقدم -->
                <div class="search-content" id="advancedSearch" style="display: none;">
                    <div class="advanced-search-form">
                        <div class="search-row">
                            <div class="search-field">
                                <label>العنوان</label>
                                <input type="text" id="searchTitle" placeholder="ابحث في العناوين...">
                            </div>
                            <div class="search-field">
                                <label>الوصف</label>
                                <input type="text" id="searchDescription" placeholder="ابحث في الأوصاف...">
                            </div>
                        </div>
                        <div class="search-row">
                            <div class="search-field">
                                <label>الأولوية</label>
                                <select id="searchPriority">
                                    <option value="">جميع الأولويات</option>
                                    <option value="high">عالية</option>
                                    <option value="medium">متوسطة</option>
                                    <option value="low">منخفضة</option>
                                </select>
                            </div>
                            <div class="search-field">
                                <label>التصنيف</label>
                                <select id="searchCategory">
                                    <option value="">جميع التصنيفات</option>
                                    <option value="personal">شخصي</option>
                                    <option value="work">عمل</option>
                                    <option value="study">دراسة</option>
                                    <option value="health">صحة</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                        </div>
                        <div class="search-row">
                            <div class="search-field">
                                <label>الحالة</label>
                                <select id="searchStatus">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending">معلقة</option>
                                    <option value="completed">مكتملة</option>
                                </select>
                            </div>
                            <div class="search-field">
                                <label>تاريخ الاستحقاق</label>
                                <input type="date" id="searchDueDate">
                            </div>
                        </div>
                        <div class="search-actions">
                            <button class="btn primary" onclick="performAdvancedSearch()">
                                <i class="fas fa-search"></i>
                                بحث
                            </button>
                            <button class="btn secondary" onclick="clearAdvancedSearch()">
                                <i class="fas fa-eraser"></i>
                                مسح
                            </button>
                        </div>
                    </div>
                    <div class="advanced-search-results">
                        <div class="results-header">
                            <span class="results-count" id="resultsCount">0 نتيجة</span>
                            <div class="results-actions">
                                <div class="dropdown-container">
                                    <button class="btn secondary dropdown-trigger" data-dropdown="exportResultsDropdown">
                                        <i class="fas fa-download"></i>
                                        تصدير النتائج
                                        <i class="fas fa-chevron-down"></i>
                                    </button>
                                    <div class="dropdown-menu" id="exportResultsDropdown">
                                        <button class="dropdown-item" onclick="exportSearchResults('txt')">
                                            <i class="fas fa-file-alt"></i>
                                            تصدير بصيغة TXT
                                        </button>
                                        <button class="dropdown-item" onclick="exportSearchResults('csv')">
                                            <i class="fas fa-file-csv"></i>
                                            تصدير بصيغة CSV
                                        </button>
                                        <button class="dropdown-item" onclick="exportSearchResults('json')">
                                            <i class="fas fa-file-code"></i>
                                            تصدير بصيغة JSON
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="advanced-search-results-list" id="advancedSearchResults">
                            <!-- نتائج البحث المتقدم -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- منطقة المهام -->
            <div class="tasks-container" id="tasksContainer">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>

            <!-- رسالة فارغة -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <div class="empty-icon">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <h3>لا توجد مهام</h3>
                <p>ابدأ بإضافة مهمة جديدة لتنظيم يومك</p>
                <button class="btn primary" onclick="showAddTaskModal()">
                    <i class="fas fa-plus"></i>
                    إضافة مهمة
                </button>
            </div>
        </main>
    </div>

    <!-- طبقة تراكب للهواتف -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- نظام الإشعارات الداخلية -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- نافذة تأكيد الانتقال للصفحة الرئيسية -->
    <div class="navigation-modal" id="navigationModal">
        <div class="navigation-content">
            <div class="navigation-header">
                <div class="navigation-icon">
                    <i class="fas fa-home"></i>
                </div>
                <h3>الانتقال للصفحة الرئيسية</h3>
            </div>
            <div class="navigation-body">
                <p>هل تريد الانتقال إلى الصفحة الرئيسية؟</p>
                <p class="navigation-note">سيتم حفظ جميع التغييرات تلقائياً</p>
            </div>
            <div class="navigation-footer">
                <button class="btn secondary" onclick="closeNavigationModal()">إلغاء</button>
                <button class="btn primary" onclick="confirmNavigation()">انتقال</button>
            </div>
        </div>
    </div>

    <!-- نافذة التأكيد المتطورة -->
    <div class="confirmation-modal" id="confirmationModal">
        <div class="confirmation-content">
            <div class="confirmation-header">
                <div class="confirmation-icon" id="confirmationIcon">
                    <i class="fas fa-question-circle"></i>
                </div>
                <h3 id="confirmationTitle">تأكيد الإجراء</h3>
            </div>
            <div class="confirmation-body">
                <p id="confirmationMessage">هل أنت متأكد من هذا الإجراء؟</p>
                <div class="confirmation-details" id="confirmationDetails"></div>
            </div>
            <div class="confirmation-footer">
                <button class="btn secondary" id="confirmationCancel">إلغاء</button>
                <button class="btn danger" id="confirmationConfirm">تأكيد</button>
            </div>
        </div>
    </div>

    <!-- نافذة التحذير المتطورة -->
    <div class="alert-modal" id="alertModal">
        <div class="alert-content">
            <div class="alert-header">
                <div class="alert-icon" id="alertIcon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <h3 id="alertTitle">تنبيه</h3>
                <button class="alert-close" id="alertClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="alert-body">
                <p id="alertMessage">رسالة التنبيه</p>
                <div class="alert-details" id="alertDetails"></div>
            </div>
            <div class="alert-footer">
                <button class="btn primary" id="alertOk">موافق</button>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة مهمة -->
    <div class="modal" id="addTaskModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إضافة مهمة جديدة</h3>
                <button class="modal-close" onclick="closeAddTaskModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="addTaskForm">
                    <div class="form-group">
                        <label for="taskTitle">عنوان المهمة *</label>
                        <input type="text" id="taskTitle" required placeholder="أدخل عنوان المهمة">
                    </div>
                    <div class="form-group">
                        <label for="taskDescription">الوصف التفصيلي</label>
                        <textarea id="taskDescription" placeholder="وصف تفصيلي للمهمة (اختياري)" rows="3"></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="taskPriority">الأولوية</label>
                            <select id="taskPriority">
                                <option value="low">منخفضة</option>
                                <option value="medium" selected>متوسطة</option>
                                <option value="high">عالية</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="taskCategory">التصنيف</label>
                            <select id="taskCategory">
                                <option value="personal">شخصي</option>
                                <option value="work">عمل</option>
                                <option value="study">دراسة</option>
                                <option value="health">صحة</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="taskDueDate">تاريخ الاستحقاق</label>
                            <input type="date" id="taskDueDate">
                        </div>
                        <div class="form-group">
                            <label for="taskDueTime">الوقت</label>
                            <input type="time" id="taskDueTime">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="taskTags">العلامات</label>
                            <input type="text" id="taskTags" placeholder="علامات مفصولة بفواصل (مثال: عاجل, مهم)">
                        </div>
                        <div class="form-group">
                            <label for="taskEstimatedTime">الوقت المتوقع (بالدقائق)</label>
                            <input type="number" id="taskEstimatedTime" placeholder="30" min="1">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="taskLocation">الموقع</label>
                            <input type="text" id="taskLocation" placeholder="موقع تنفيذ المهمة (اختياري)">
                        </div>
                        <div class="form-group">
                            <label for="taskReminder">تذكير قبل (بالدقائق)</label>
                            <select id="taskReminder">
                                <option value="">بدون تذكير</option>
                                <option value="5">5 دقائق</option>
                                <option value="15">15 دقيقة</option>
                                <option value="30">30 دقيقة</option>
                                <option value="60">ساعة واحدة</option>
                                <option value="1440">يوم واحد</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="taskNotes">ملاحظات إضافية</label>
                        <textarea id="taskNotes" placeholder="ملاحظات أو تعليقات إضافية" rows="2"></textarea>
                    </div>
                    <div class="form-options">
                        <label class="checkbox-label">
                            <input type="checkbox" id="taskImportant">
                            <span class="checkmark"></span>
                            مهمة مهمة
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="taskRecurring">
                            <span class="checkmark"></span>
                            مهمة متكررة
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="taskPrivate">
                            <span class="checkmark"></span>
                            مهمة خاصة
                        </label>
                    </div>
                    <div class="recurring-options" id="recurringOptions" style="display: none;">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="recurringType">نوع التكرار</label>
                                <select id="recurringType">
                                    <option value="daily">يومياً</option>
                                    <option value="weekly">أسبوعياً</option>
                                    <option value="monthly">شهرياً</option>
                                    <option value="yearly">سنوياً</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="recurringInterval">كل</label>
                                <input type="number" id="recurringInterval" value="1" min="1" max="365">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn secondary" onclick="closeAddTaskModal()">إلغاء</button>
                <button type="submit" class="btn primary" onclick="addTask()">إضافة المهمة</button>
            </div>
        </div>
    </div>

    <script src="js/simple-user-manager.js"></script>
    <script>
        // نظام إدارة المهام المتقدم
        class AdvancedTaskManager {
            constructor() {
                this.tasks = this.loadTasks();
                this.currentUser = this.getCurrentUser();
                this.currentFilter = 'all';
                this.currentSort = 'created';
                this.currentView = 'list';
                this.searchQuery = '';
                this.init();
            }

            init() {
                this.updateUserInfo();
                this.renderTasks();
                this.updateStats();
                this.updateCategories();
                this.setupEventListeners();
                this.checkWelcomeMessage();
            }

            // تحميل المستخدم الحالي
            getCurrentUser() {
                try {
                    const userData = localStorage.getItem('tdl_current_user');
                    return userData ? JSON.parse(userData) : null;
                } catch (error) {
                    console.error('خطأ في تحميل بيانات المستخدم:', error);
                    return null;
                }
            }

            // تحميل المهام
            loadTasks() {
                try {
                    const tasks = localStorage.getItem('tdl_tasks');
                    return tasks ? JSON.parse(tasks) : [];
                } catch (error) {
                    console.error('خطأ في تحميل المهام:', error);
                    return [];
                }
            }

            // حفظ المهام
            saveTasks() {
                try {
                    localStorage.setItem('tdl_tasks', JSON.stringify(this.tasks));
                    this.updateUserTasks();
                } catch (error) {
                    console.error('خطأ في حفظ المهام:', error);
                }
            }

            // تحديث مهام المستخدم
            updateUserTasks() {
                if (this.currentUser) {
                    try {
                        const users = JSON.parse(localStorage.getItem('tdl_users') || '{}');
                        if (users[this.currentUser.username]) {
                            users[this.currentUser.username].tasks = this.tasks;
                            users[this.currentUser.username].stats = {
                                totalTasks: this.tasks.length,
                                completedTasks: this.tasks.filter(task => task.completed).length,
                                loginCount: users[this.currentUser.username].stats?.loginCount || 0
                            };
                            localStorage.setItem('tdl_users', JSON.stringify(users));
                        }
                    } catch (error) {
                        console.error('خطأ في تحديث بيانات المستخدم:', error);
                    }
                }
            }

            // تحديث معلومات المستخدم
            updateUserInfo() {
                const userNameEl = document.getElementById('userName');
                const userEmailEl = document.getElementById('userEmail');
                const userAvatarEl = document.getElementById('userAvatar');

                if (this.currentUser) {
                    userNameEl.textContent = `مرحباً ${this.currentUser.fullName}!`;
                    userEmailEl.textContent = this.currentUser.email;

                    // عرض الأحرف الأولى من الاسم
                    const initials = this.currentUser.fullName.split(' ').map(name => name[0]).join('').toUpperCase();
                    userAvatarEl.textContent = initials;

                    // تحميل مهام المستخدم
                    this.loadUserTasks();
                } else {
                    userNameEl.textContent = 'مرحباً بك!';
                    userEmailEl.textContent = 'يرجى تسجيل الدخول';
                    userAvatarEl.innerHTML = '<i class="fas fa-user"></i>';

                    // التوجه لصفحة التسجيل
                    setTimeout(() => {
                        window.location.href = 'standalone-auth.html?type=user';
                    }, 2000);
                }
            }

            // تحميل مهام المستخدم
            loadUserTasks() {
                if (this.currentUser) {
                    try {
                        const users = JSON.parse(localStorage.getItem('tdl_users') || '{}');
                        const user = users[this.currentUser.username];
                        if (user && user.tasks) {
                            this.tasks = user.tasks;
                        }
                    } catch (error) {
                        console.error('خطأ في تحميل مهام المستخدم:', error);
                    }
                }
            }

            // فحص رسالة الترحيب
            checkWelcomeMessage() {
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.get('welcome') === 'true') {
                    setTimeout(() => {
                        this.showNotification(`مرحباً بك ${this.currentUser?.fullName || ''}! 🎉 أهلاً بك في TDL المتقدم`, 'success');
                    }, 1500);
                }
            }

            // إعداد مستمعي الأحداث
            setupEventListeners() {
                // البحث (إذا كان موجوداً)
                const searchInput = document.getElementById('searchInput');
                if (searchInput) {
                    searchInput.addEventListener('input', (e) => {
                        this.searchQuery = e.target.value;
                        this.renderTasks();
                    });
                }

                // مسح البحث (إذا كان موجوداً)
                const searchClear = document.getElementById('searchClear');
                if (searchClear) {
                    searchClear.addEventListener('click', () => {
                        if (searchInput) {
                            searchInput.value = '';
                            this.searchQuery = '';
                            this.renderTasks();
                        }
                    });
                }

                // الفلاتر
                document.querySelectorAll('.filter-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                        e.target.classList.add('active');
                        this.currentFilter = e.target.dataset.filter;
                        this.renderTasks();
                    });
                });

                // الترتيب (إذا كان موجوداً)
                const sortBy = document.getElementById('sortBy');
                if (sortBy) {
                    sortBy.addEventListener('change', (e) => {
                        this.currentSort = e.target.value;
                        this.renderTasks();
                    });
                }

                // طريقة العرض
                const viewMode = document.getElementById('viewMode');
                if (viewMode) {
                    viewMode.addEventListener('change', (e) => {
                        this.currentView = e.target.value;
                        this.renderTasks();
                    });
                }

                // القائمة الجانبية للهواتف
                const sidebarToggle = document.getElementById('sidebarToggle');
                if (sidebarToggle) {
                    sidebarToggle.addEventListener('click', () => {
                        this.toggleSidebar();
                    });
                }

                const sidebarOverlay = document.getElementById('sidebarOverlay');
                if (sidebarOverlay) {
                    sidebarOverlay.addEventListener('click', () => {
                        this.closeSidebar();
                    });
                }

                // الإعدادات
                const autoSave = document.getElementById('autoSave');
                if (autoSave) {
                    autoSave.addEventListener('change', (e) => {
                        this.updateSetting('autoSave', e.target.checked);
                    });
                }

                const notifications = document.getElementById('notifications');
                if (notifications) {
                    notifications.addEventListener('change', (e) => {
                        this.updateSetting('notifications', e.target.checked);
                    });
                }

                const soundEffects = document.getElementById('soundEffects');
                if (soundEffects) {
                    soundEffects.addEventListener('change', (e) => {
                        this.updateSetting('soundEffects', e.target.checked);
                    });
                }
            }

            // إضافة مهمة سريعة
            quickAddTask() {
                const input = document.getElementById('quickAddInput');
                const title = input.value.trim();

                if (!title) {
                    this.showToast('يرجى إدخال عنوان المهمة', 'error');
                    return;
                }

                const newTask = {
                    id: Date.now(),
                    title,
                    description: '',
                    priority: 'medium',
                    category: 'personal',
                    dueDate: null,
                    important: false,
                    completed: false,
                    createdAt: new Date().toISOString(),
                    userId: this.currentUser?.username,
                    tags: [],
                    estimatedTime: null,
                    location: '',
                    reminder: null,
                    notes: '',
                    recurring: false,
                    private: false
                };

                this.tasks.push(newTask);
                this.saveTasks();
                this.renderTasks();
                this.updateStats();
                this.updateCategories();

                input.value = '';

                // إشعار سريع لمدة ثانية واحدة
                this.showQuickToast('تم إضافة المهمة!', 'success');
            }

            // إضافة أو تعديل مهمة متقدمة
            addTask() {
                const title = document.getElementById('taskTitle').value.trim();
                const description = document.getElementById('taskDescription').value.trim();
                const priority = document.getElementById('taskPriority').value;
                const category = document.getElementById('taskCategory').value;
                const dueDate = document.getElementById('taskDueDate').value;
                const dueTime = document.getElementById('taskDueTime').value;
                const important = document.getElementById('taskImportant').checked;
                const tags = document.getElementById('taskTags').value.trim();
                const estimatedTime = document.getElementById('taskEstimatedTime').value;
                const location = document.getElementById('taskLocation').value.trim();
                const reminder = document.getElementById('taskReminder').value;
                const notes = document.getElementById('taskNotes').value.trim();
                const recurring = document.getElementById('taskRecurring').checked;
                const isPrivate = document.getElementById('taskPrivate').checked;

                if (!title) {
                    this.showAlert({
                        title: 'خطأ في البيانات',
                        message: 'يرجى إدخال عنوان المهمة',
                        type: 'error',
                        icon: 'fas fa-exclamation-triangle'
                    });
                    return;
                }

                const taskData = {
                    title,
                    description,
                    priority,
                    category,
                    dueDate: dueDate ? `${dueDate}${dueTime ? 'T' + dueTime : ''}` : null,
                    important,
                    userId: this.currentUser?.username,
                    tags: tags ? tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [],
                    estimatedTime: estimatedTime ? parseInt(estimatedTime) : null,
                    location,
                    reminder: reminder ? parseInt(reminder) : null,
                    notes,
                    recurring,
                    private: isPrivate,
                    recurringType: recurring ? document.getElementById('recurringType').value : null,
                    recurringInterval: recurring ? parseInt(document.getElementById('recurringInterval').value) : null
                };

                if (this.editingTaskId) {
                    // تعديل مهمة موجودة
                    const taskIndex = this.tasks.findIndex(t => t.id === this.editingTaskId);
                    if (taskIndex !== -1) {
                        this.tasks[taskIndex] = {
                            ...this.tasks[taskIndex],
                            ...taskData,
                            updatedAt: new Date().toISOString()
                        };

                        this.saveTasks();
                        this.renderTasks();
                        this.updateStats();
                        this.updateCategories();
                        this.closeAddTaskModal();

                        this.showAlert({
                            title: 'تم التعديل',
                            message: 'تم تعديل المهمة بنجاح!',
                            type: 'success',
                            icon: 'fas fa-check-circle'
                        });
                    }
                } else {
                    // إضافة مهمة جديدة
                    const newTask = {
                        id: Date.now(),
                        ...taskData,
                        completed: false,
                        createdAt: new Date().toISOString()
                    };

                    this.tasks.push(newTask);
                    this.saveTasks();
                    this.renderTasks();
                    this.updateStats();
                    this.updateCategories();
                    this.closeAddTaskModal();

                    this.showAlert({
                        title: 'تم الإضافة',
                        message: 'تم إضافة المهمة بنجاح!',
                        type: 'success',
                        icon: 'fas fa-plus-circle'
                    });
                }
            }

            // تبديل حالة المهمة
            toggleTask(taskId) {
                const task = this.tasks.find(t => t.id === taskId);
                if (task) {
                    task.completed = !task.completed;
                    task.completedAt = task.completed ? new Date().toISOString() : null;
                    this.saveTasks();
                    this.renderTasks();
                    this.updateStats();

                    if (task.completed) {
                        this.showNotification('تم إكمال المهمة! 🎉', 'success');
                    }
                }
            }

            // حذف مهمة
            deleteTask(taskId) {
                const task = this.tasks.find(t => t.id === taskId);
                if (!task) return;

                this.showConfirmation({
                    title: 'حذف المهمة',
                    message: 'هل أنت متأكد من حذف هذه المهمة؟',
                    details: `المهمة: "${task.title}"<br>هذا الإجراء لا يمكن التراجع عنه.`,
                    icon: 'fas fa-trash',
                    iconClass: 'error',
                    confirmText: 'حذف',
                    cancelText: 'إلغاء',
                    onConfirm: () => {
                        this.tasks = this.tasks.filter(t => t.id !== taskId);
                        this.saveTasks();
                        this.renderTasks();
                        this.updateStats();
                        this.updateCategories();
                        this.showAlert({
                            title: 'تم الحذف',
                            message: 'تم حذف المهمة بنجاح',
                            type: 'success',
                            icon: 'fas fa-check-circle'
                        });
                    }
                });
            }

            // تعديل مهمة
            editTask(taskId) {
                const task = this.tasks.find(t => t.id === taskId);
                if (!task) return;

                // تخزين معرف المهمة للتعديل
                this.editingTaskId = taskId;

                // ملء النموذج بالبيانات الحالية
                document.getElementById('taskTitle').value = task.title;
                document.getElementById('taskDescription').value = task.description || '';
                document.getElementById('taskPriority').value = task.priority;
                document.getElementById('taskCategory').value = task.category;

                if (task.dueDate) {
                    const date = new Date(task.dueDate);
                    document.getElementById('taskDueDate').value = date.toISOString().split('T')[0];
                    document.getElementById('taskDueTime').value = date.toTimeString().slice(0, 5);
                } else {
                    document.getElementById('taskDueDate').value = '';
                    document.getElementById('taskDueTime').value = '';
                }

                document.getElementById('taskImportant').checked = task.important;

                // تغيير عنوان النافذة
                document.querySelector('#addTaskModal .modal-header h3').textContent = 'تعديل المهمة';
                document.querySelector('#addTaskModal .btn.primary').textContent = 'حفظ التعديل';

                // إظهار النموذج
                this.showAddTaskModal();

                this.showAlert({
                    title: 'وضع التعديل',
                    message: `جاري تعديل المهمة: "${task.title}"`,
                    type: 'info',
                    icon: 'fas fa-edit'
                });
            }

            // عرض المهام
            renderTasks() {
                const container = document.getElementById('tasksContainer');
                const emptyState = document.getElementById('emptyState');

                let filteredTasks = this.filterTasks();
                filteredTasks = this.sortTasks(filteredTasks);

                if (filteredTasks.length === 0) {
                    container.innerHTML = '';
                    emptyState.style.display = 'block';
                    return;
                }

                emptyState.style.display = 'none';

                if (this.currentView === 'grid') {
                    container.className = 'tasks-container grid-view';
                } else if (this.currentView === 'kanban') {
                    container.className = 'tasks-container kanban-view';
                    this.renderKanbanView(filteredTasks);
                    return;
                } else {
                    container.className = 'tasks-container list-view';
                }

                container.innerHTML = filteredTasks.map(task => this.createTaskHTML(task)).join('');
            }

            // إنشاء HTML للمهمة
            createTaskHTML(task) {
                const isOverdue = task.dueDate && new Date(task.dueDate) < new Date() && !task.completed;
                const priorityClass = `priority-${task.priority}`;

                return `
                    <div class="task-card ${task.completed ? 'completed' : ''} ${priorityClass}">
                        <div class="task-header">
                            <div class="task-checkbox ${task.completed ? 'checked' : ''}" onclick="taskManager.toggleTask(${task.id})">
                                ${task.completed ? '<i class="fas fa-check"></i>' : ''}
                            </div>
                            <div class="task-content">
                                <div class="task-title ${task.completed ? 'completed' : ''}">
                                    ${task.important ? '<i class="fas fa-star" style="color: var(--warning); margin-left: 0.5rem;"></i>' : ''}
                                    ${task.title}
                                </div>
                                ${task.description ? `<div class="task-description">${task.description}</div>` : ''}
                                <div class="task-meta">
                                    <span class="task-badge priority">${this.getPriorityText(task.priority)}</span>
                                    <span class="task-badge category">${this.getCategoryText(task.category)}</span>
                                    ${task.dueDate ? `<span class="task-badge ${isOverdue ? 'overdue' : 'due-date'}">${this.formatDate(task.dueDate)}</span>` : ''}
                                </div>
                                <div class="task-actions">
                                    <button class="task-action-btn edit" onclick="taskManager.editTask(${task.id})" title="تعديل المهمة">
                                        <i class="fas fa-edit"></i>
                                        <span>تعديل</span>
                                    </button>
                                    <button class="task-action-btn delete" onclick="taskManager.deleteTask(${task.id})" title="حذف المهمة">
                                        <i class="fas fa-trash"></i>
                                        <span>حذف</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            // عرض كانبان
            renderKanbanView(tasks) {
                const container = document.getElementById('tasksContainer');
                const pendingTasks = tasks.filter(task => !task.completed);
                const completedTasks = tasks.filter(task => task.completed);

                container.innerHTML = `
                    <div class="kanban-column">
                        <div class="kanban-header">
                            <i class="fas fa-clock"></i>
                            المهام المعلقة
                            <span class="kanban-count">${pendingTasks.length}</span>
                        </div>
                        ${pendingTasks.map(task => this.createTaskHTML(task)).join('')}
                    </div>
                    <div class="kanban-column">
                        <div class="kanban-header">
                            <i class="fas fa-check-circle"></i>
                            المهام المكتملة
                            <span class="kanban-count">${completedTasks.length}</span>
                        </div>
                        ${completedTasks.map(task => this.createTaskHTML(task)).join('')}
                    </div>
                `;
            }

            // فلترة المهام
            filterTasks() {
                let filtered = this.tasks;

                // فلترة حسب البحث
                if (this.searchQuery) {
                    filtered = filtered.filter(task =>
                        task.title.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                        (task.description && task.description.toLowerCase().includes(this.searchQuery.toLowerCase()))
                    );
                }

                // فلترة حسب النوع
                switch (this.currentFilter) {
                    case 'pending':
                        filtered = filtered.filter(task => !task.completed);
                        break;
                    case 'completed':
                        filtered = filtered.filter(task => task.completed);
                        break;
                    case 'important':
                        filtered = filtered.filter(task => task.important);
                        break;
                }

                return filtered;
            }

            // ترتيب المهام
            sortTasks(tasks) {
                return tasks.sort((a, b) => {
                    switch (this.currentSort) {
                        case 'priority':
                            const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
                            return priorityOrder[b.priority] - priorityOrder[a.priority];
                        case 'dueDate':
                            if (!a.dueDate && !b.dueDate) return 0;
                            if (!a.dueDate) return 1;
                            if (!b.dueDate) return -1;
                            return new Date(a.dueDate) - new Date(b.dueDate);
                        case 'alphabetical':
                            return a.title.localeCompare(b.title, 'ar');
                        case 'created':
                        default:
                            return new Date(b.createdAt) - new Date(a.createdAt);
                    }
                });
            }

            // تحديث الإحصائيات
            updateStats() {
                const total = this.tasks.length;
                const completed = this.tasks.filter(task => task.completed).length;
                const pending = total - completed;
                const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0;

                document.getElementById('totalTasks').textContent = total;
                document.getElementById('completedTasks').textContent = completed;
                document.getElementById('pendingTasks').textContent = pending;
                document.getElementById('completionRate').textContent = completionRate + '%';
            }

            // تحديث التصنيفات
            updateCategories() {
                const categoriesContainer = document.getElementById('categoriesList');
                const categories = {};

                this.tasks.forEach(task => {
                    if (!categories[task.category]) {
                        categories[task.category] = 0;
                    }
                    categories[task.category]++;
                });

                categoriesContainer.innerHTML = Object.entries(categories)
                    .map(([category, count]) => `
                        <div class="category-item">
                            <div class="category-name">
                                <i class="fas fa-tag"></i>
                                ${this.getCategoryText(category)}
                            </div>
                            <span class="category-count">${count}</span>
                        </div>
                    `).join('');
            }

            // دوال مساعدة
            getPriorityText(priority) {
                const priorities = {
                    'high': 'عالية',
                    'medium': 'متوسطة',
                    'low': 'منخفضة'
                };
                return priorities[priority] || priority;
            }

            getCategoryText(category) {
                const categories = {
                    'personal': 'شخصي',
                    'work': 'عمل',
                    'study': 'دراسة',
                    'health': 'صحة',
                    'other': 'أخرى'
                };
                return categories[category] || category;
            }

            formatDate(dateString) {
                const date = new Date(dateString);
                const now = new Date();
                const diffTime = date - now;
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                if (diffDays === 0) return 'اليوم';
                if (diffDays === 1) return 'غداً';
                if (diffDays === -1) return 'أمس';
                if (diffDays < 0) return `متأخر ${Math.abs(diffDays)} يوم`;
                if (diffDays <= 7) return `خلال ${diffDays} أيام`;

                return date.toLocaleDateString('ar-SA');
            }

            // إدارة القائمة الجانبية
            toggleSidebar() {
                const sidebar = document.getElementById('sidebar');
                const overlay = document.getElementById('sidebarOverlay');

                sidebar.classList.toggle('open');
                overlay.classList.toggle('active');
            }

            closeSidebar() {
                const sidebar = document.getElementById('sidebar');
                const overlay = document.getElementById('sidebarOverlay');

                sidebar.classList.remove('open');
                overlay.classList.remove('active');
            }

            // إدارة الإعدادات
            updateSetting(setting, value) {
                const settings = JSON.parse(localStorage.getItem('tdl_settings') || '{}');
                settings[setting] = value;
                localStorage.setItem('tdl_settings', JSON.stringify(settings));

                this.showNotification(`تم تحديث إعداد ${setting}`, 'info');
            }

            // مسح المهام المكتملة
            clearCompleted() {
                const completedCount = this.tasks.filter(task => task.completed).length;

                if (completedCount === 0) {
                    this.showAlert({
                        title: 'لا توجد مهام',
                        message: 'لا توجد مهام مكتملة لمسحها',
                        type: 'info',
                        icon: 'fas fa-info-circle'
                    });
                    return;
                }

                this.showConfirmation({
                    title: 'مسح المهام المكتملة',
                    message: `هل أنت متأكد من مسح ${completedCount} مهمة مكتملة؟`,
                    details: 'سيتم حذف جميع المهام المكتملة نهائياً.<br>هذا الإجراء لا يمكن التراجع عنه.',
                    icon: 'fas fa-broom',
                    iconClass: 'warning',
                    confirmText: 'مسح الكل',
                    cancelText: 'إلغاء',
                    onConfirm: () => {
                        this.tasks = this.tasks.filter(task => !task.completed);
                        this.saveTasks();
                        this.renderTasks();
                        this.updateStats();
                        this.updateCategories();
                        this.showAlert({
                            title: 'تم المسح',
                            message: `تم مسح ${completedCount} مهمة مكتملة بنجاح`,
                            type: 'success',
                            icon: 'fas fa-check-circle'
                        });
                    }
                });
            }

            // مسح جميع المهام
            clearAll() {
                if (this.tasks.length === 0) {
                    this.showAlert({
                        title: 'لا توجد مهام',
                        message: 'لا توجد مهام لمسحها',
                        type: 'info',
                        icon: 'fas fa-info-circle'
                    });
                    return;
                }

                this.showConfirmation({
                    title: 'مسح جميع المهام',
                    message: `هل أنت متأكد من مسح جميع المهام (${this.tasks.length} مهمة)؟`,
                    details: '⚠️ تحذير: سيتم حذف جميع المهام نهائياً!<br>هذا الإجراء لا يمكن التراجع عنه.<br>يُنصح بتصدير المهام أولاً كنسخة احتياطية.',
                    icon: 'fas fa-exclamation-triangle',
                    iconClass: 'error',
                    confirmText: 'مسح الكل',
                    cancelText: 'إلغاء',
                    onConfirm: () => {
                        const tasksCount = this.tasks.length;
                        this.tasks = [];
                        this.saveTasks();
                        this.renderTasks();
                        this.updateStats();
                        this.updateCategories();
                        this.showAlert({
                            title: 'تم المسح',
                            message: `تم مسح جميع المهام (${tasksCount} مهمة) بنجاح`,
                            type: 'success',
                            icon: 'fas fa-check-circle'
                        });
                    }
                });
            }

            // تصدير المهام بصيغ متعددة
            exportTasks(format = 'json') {
                const timestamp = new Date().toISOString().split('T')[0];
                let data, mimeType, extension;

                switch (format) {
                    case 'txt':
                        data = this.exportToTXT();
                        mimeType = 'text/plain';
                        extension = 'txt';
                        break;
                    case 'csv':
                        data = this.exportToCSV();
                        mimeType = 'text/csv';
                        extension = 'csv';
                        break;
                    case 'json':
                    default:
                        data = JSON.stringify(this.tasks, null, 2);
                        mimeType = 'application/json';
                        extension = 'json';
                        break;
                }

                const dataBlob = new Blob([data], {type: mimeType});
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `tdl-tasks-${timestamp}.${extension}`;
                link.click();
                URL.revokeObjectURL(url);

                this.showAlert({
                    title: 'تم التصدير',
                    message: `تم تصدير ${this.tasks.length} مهمة بصيغة ${format.toUpperCase()} بنجاح`,
                    type: 'success',
                    icon: 'fas fa-download'
                });
            }

            // تصدير إلى TXT
            exportToTXT() {
                let content = `قائمة المهام - TDL\n`;
                content += `تاريخ التصدير: ${new Date().toLocaleDateString('ar-SA')}\n`;
                content += `عدد المهام: ${this.tasks.length}\n`;
                content += `${'='.repeat(50)}\n\n`;

                this.tasks.forEach((task, index) => {
                    content += `${index + 1}. ${task.title}\n`;
                    content += `   الحالة: ${task.completed ? 'مكتملة' : 'معلقة'}\n`;
                    content += `   الأولوية: ${this.getPriorityText(task.priority)}\n`;
                    content += `   التصنيف: ${this.getCategoryText(task.category)}\n`;

                    if (task.description) {
                        content += `   الوصف: ${task.description}\n`;
                    }

                    if (task.dueDate) {
                        content += `   تاريخ الاستحقاق: ${this.formatDate(task.dueDate)}\n`;
                    }

                    if (task.tags && task.tags.length > 0) {
                        content += `   العلامات: ${task.tags.join(', ')}\n`;
                    }

                    if (task.location) {
                        content += `   الموقع: ${task.location}\n`;
                    }

                    if (task.estimatedTime) {
                        content += `   الوقت المتوقع: ${task.estimatedTime} دقيقة\n`;
                    }

                    content += `   تاريخ الإنشاء: ${new Date(task.createdAt).toLocaleDateString('ar-SA')}\n`;
                    content += `\n${'-'.repeat(30)}\n\n`;
                });

                return content;
            }

            // تصدير إلى CSV
            exportToCSV() {
                const headers = [
                    'العنوان',
                    'الوصف',
                    'الحالة',
                    'الأولوية',
                    'التصنيف',
                    'تاريخ الاستحقاق',
                    'مهمة مهمة',
                    'العلامات',
                    'الموقع',
                    'الوقت المتوقع',
                    'الملاحظات',
                    'تاريخ الإنشاء',
                    'آخر تحديث'
                ];

                let csv = headers.join(',') + '\n';

                this.tasks.forEach(task => {
                    const row = [
                        `"${task.title.replace(/"/g, '""')}"`,
                        `"${(task.description || '').replace(/"/g, '""')}"`,
                        task.completed ? 'مكتملة' : 'معلقة',
                        this.getPriorityText(task.priority),
                        this.getCategoryText(task.category),
                        task.dueDate ? new Date(task.dueDate).toLocaleDateString('ar-SA') : '',
                        task.important ? 'نعم' : 'لا',
                        task.tags ? `"${task.tags.join(', ')}"` : '',
                        `"${(task.location || '').replace(/"/g, '""')}"`,
                        task.estimatedTime || '',
                        `"${(task.notes || '').replace(/"/g, '""')}"`,
                        new Date(task.createdAt).toLocaleDateString('ar-SA'),
                        task.updatedAt ? new Date(task.updatedAt).toLocaleDateString('ar-SA') : ''
                    ];
                    csv += row.join(',') + '\n';
                });

                return csv;
            }

            // تصدير نتائج البحث
            exportSearchResults(format) {
                if (!this.currentSearchResults || this.currentSearchResults.length === 0) {
                    this.showAlert({
                        title: 'لا توجد نتائج',
                        message: 'لا توجد نتائج بحث لتصديرها',
                        type: 'info',
                        icon: 'fas fa-info-circle'
                    });
                    return;
                }

                const originalTasks = this.tasks;
                this.tasks = this.currentSearchResults;
                this.exportTasks(format);
                this.tasks = originalTasks;
            }

            // استيراد المهام
            importTasks() {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = '.json';
                input.onchange = (e) => {
                    const file = e.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            try {
                                const importedTasks = JSON.parse(e.target.result);
                                if (Array.isArray(importedTasks)) {
                                    this.tasks = [...this.tasks, ...importedTasks];
                                    this.saveTasks();
                                    this.renderTasks();
                                    this.updateStats();
                                    this.updateCategories();
                                    this.showNotification(`تم استيراد ${importedTasks.length} مهمة`, 'success');
                                } else {
                                    this.showNotification('ملف غير صالح', 'error');
                                }
                            } catch (error) {
                                this.showNotification('خطأ في قراءة الملف', 'error');
                            }
                        };
                        reader.readAsText(file);
                    }
                };
                input.click();
            }

            // عرض نافذة التأكيد
            showConfirmation(options) {
                const modal = document.getElementById('confirmationModal');
                const icon = document.getElementById('confirmationIcon');
                const title = document.getElementById('confirmationTitle');
                const message = document.getElementById('confirmationMessage');
                const details = document.getElementById('confirmationDetails');
                const confirmBtn = document.getElementById('confirmationConfirm');
                const cancelBtn = document.getElementById('confirmationCancel');

                // تعيين المحتوى
                icon.innerHTML = `<i class="${options.icon || 'fas fa-question-circle'}"></i>`;
                icon.className = `confirmation-icon ${options.iconClass || 'warning'}`;
                title.textContent = options.title || 'تأكيد الإجراء';
                message.textContent = options.message || 'هل أنت متأكد؟';

                if (options.details) {
                    details.innerHTML = options.details;
                    details.style.display = 'block';
                } else {
                    details.style.display = 'none';
                }

                confirmBtn.textContent = options.confirmText || 'تأكيد';
                cancelBtn.textContent = options.cancelText || 'إلغاء';

                // إعداد الأحداث
                const handleConfirm = () => {
                    modal.classList.remove('show');
                    if (options.onConfirm) options.onConfirm();
                    cleanup();
                };

                const handleCancel = () => {
                    modal.classList.remove('show');
                    if (options.onCancel) options.onCancel();
                    cleanup();
                };

                const cleanup = () => {
                    confirmBtn.removeEventListener('click', handleConfirm);
                    cancelBtn.removeEventListener('click', handleCancel);
                };

                confirmBtn.addEventListener('click', handleConfirm);
                cancelBtn.addEventListener('click', handleCancel);

                // إظهار النافذة
                modal.classList.add('show');
            }

            // عرض نافذة التحذير
            showAlert(options) {
                const modal = document.getElementById('alertModal');
                const icon = document.getElementById('alertIcon');
                const title = document.getElementById('alertTitle');
                const message = document.getElementById('alertMessage');
                const details = document.getElementById('alertDetails');
                const okBtn = document.getElementById('alertOk');
                const closeBtn = document.getElementById('alertClose');

                // تعيين المحتوى
                icon.innerHTML = `<i class="${options.icon || 'fas fa-info-circle'}"></i>`;
                icon.className = `alert-icon ${options.type || 'info'}`;
                title.textContent = options.title || 'تنبيه';
                message.textContent = options.message || 'رسالة التنبيه';

                if (options.details) {
                    details.innerHTML = options.details;
                    details.style.display = 'block';
                } else {
                    details.style.display = 'none';
                }

                // إعداد الأحداث
                const handleClose = () => {
                    modal.classList.remove('show');
                    if (options.onClose) options.onClose();
                    cleanup();
                };

                const cleanup = () => {
                    okBtn.removeEventListener('click', handleClose);
                    closeBtn.removeEventListener('click', handleClose);
                };

                okBtn.addEventListener('click', handleClose);
                closeBtn.addEventListener('click', handleClose);

                // إظهار النافذة
                modal.classList.add('show');
            }

            // البحث السريع
            performQuickSearch(query) {
                if (!query.trim()) {
                    document.getElementById('quickSearchResults').innerHTML = '';
                    return;
                }

                const results = this.tasks.filter(task =>
                    task.title.toLowerCase().includes(query.toLowerCase()) ||
                    (task.description && task.description.toLowerCase().includes(query.toLowerCase())) ||
                    (task.tags && task.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase())))
                );

                this.renderQuickSearchResults(results);
            }

            // عرض نتائج البحث السريع
            renderQuickSearchResults(results) {
                const container = document.getElementById('quickSearchResults');

                if (results.length === 0) {
                    container.innerHTML = '<div class="no-results">لا توجد نتائج</div>';
                    return;
                }

                container.innerHTML = results.map(task => `
                    <div class="search-result-item">
                        <div class="search-result-header">
                            <div class="search-result-title">${task.title}</div>
                            <div class="search-result-actions">
                                <button class="search-result-btn" onclick="taskManager.editTask(${task.id})" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="search-result-btn ${task.completed ? '' : 'complete'}"
                                        onclick="taskManager.toggleTask(${task.id})"
                                        title="${task.completed ? 'إلغاء الإكمال' : 'إكمال'}">
                                    <i class="fas fa-${task.completed ? 'undo' : 'check'}"></i>
                                </button>
                                <button class="search-result-btn delete" onclick="taskManager.deleteTask(${task.id})" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        ${task.description ? `<div class="search-result-description">${task.description}</div>` : ''}
                        <div class="search-result-meta">
                            <span class="task-badge priority">${this.getPriorityText(task.priority)}</span>
                            <span class="task-badge category">${this.getCategoryText(task.category)}</span>
                            ${task.completed ? '<span class="task-badge completed">مكتملة</span>' : '<span class="task-badge pending">معلقة</span>'}
                        </div>
                    </div>
                `).join('');
            }

            // البحث المتقدم
            performAdvancedSearch() {
                const criteria = {
                    title: document.getElementById('searchTitle').value.trim(),
                    description: document.getElementById('searchDescription').value.trim(),
                    priority: document.getElementById('searchPriority').value,
                    category: document.getElementById('searchCategory').value,
                    status: document.getElementById('searchStatus').value,
                    dueDate: document.getElementById('searchDueDate').value
                };

                let results = this.tasks.filter(task => {
                    // البحث في العنوان
                    if (criteria.title && !task.title.toLowerCase().includes(criteria.title.toLowerCase())) {
                        return false;
                    }

                    // البحث في الوصف
                    if (criteria.description && (!task.description || !task.description.toLowerCase().includes(criteria.description.toLowerCase()))) {
                        return false;
                    }

                    // فلترة الأولوية
                    if (criteria.priority && task.priority !== criteria.priority) {
                        return false;
                    }

                    // فلترة التصنيف
                    if (criteria.category && task.category !== criteria.category) {
                        return false;
                    }

                    // فلترة الحالة
                    if (criteria.status) {
                        if (criteria.status === 'completed' && !task.completed) return false;
                        if (criteria.status === 'pending' && task.completed) return false;
                    }

                    // فلترة تاريخ الاستحقاق
                    if (criteria.dueDate && task.dueDate) {
                        const taskDate = new Date(task.dueDate).toDateString();
                        const searchDate = new Date(criteria.dueDate).toDateString();
                        if (taskDate !== searchDate) return false;
                    }

                    return true;
                });

                this.currentSearchResults = results;
                this.renderAdvancedSearchResults(results);
                document.getElementById('resultsCount').textContent = `${results.length} نتيجة`;
            }

            // عرض نتائج البحث المتقدم
            renderAdvancedSearchResults(results) {
                const container = document.getElementById('advancedSearchResults');

                if (results.length === 0) {
                    container.innerHTML = '<div class="no-results">لا توجد نتائج تطابق معايير البحث</div>';
                    return;
                }

                container.innerHTML = results.map(task => `
                    <div class="search-result-item">
                        <div class="search-result-header">
                            <div class="search-result-title">
                                ${task.important ? '<i class="fas fa-star" style="color: var(--warning);"></i> ' : ''}
                                ${task.title}
                            </div>
                            <div class="search-result-actions">
                                <button class="search-result-btn" onclick="taskManager.editTask(${task.id})" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="search-result-btn ${task.completed ? '' : 'complete'}"
                                        onclick="taskManager.toggleTask(${task.id})"
                                        title="${task.completed ? 'إلغاء الإكمال' : 'إكمال'}">
                                    <i class="fas fa-${task.completed ? 'undo' : 'check'}"></i>
                                </button>
                                <button class="search-result-btn delete" onclick="taskManager.deleteTask(${task.id})" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        ${task.description ? `<div class="search-result-description">${task.description}</div>` : ''}
                        <div class="search-result-details">
                            <div class="detail-row">
                                <span class="detail-label">الأولوية:</span>
                                <span class="task-badge priority">${this.getPriorityText(task.priority)}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">التصنيف:</span>
                                <span class="task-badge category">${this.getCategoryText(task.category)}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">الحالة:</span>
                                ${task.completed ? '<span class="task-badge completed">مكتملة</span>' : '<span class="task-badge pending">معلقة</span>'}
                            </div>
                            ${task.dueDate ? `
                                <div class="detail-row">
                                    <span class="detail-label">تاريخ الاستحقاق:</span>
                                    <span class="task-badge due-date">${this.formatDate(task.dueDate)}</span>
                                </div>
                            ` : ''}
                            ${task.tags && task.tags.length > 0 ? `
                                <div class="detail-row">
                                    <span class="detail-label">العلامات:</span>
                                    <div class="tags-container">
                                        ${task.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                                    </div>
                                </div>
                            ` : ''}
                            ${task.estimatedTime ? `
                                <div class="detail-row">
                                    <span class="detail-label">الوقت المتوقع:</span>
                                    <span class="time-badge">${task.estimatedTime} دقيقة</span>
                                </div>
                            ` : ''}
                            ${task.location ? `
                                <div class="detail-row">
                                    <span class="detail-label">الموقع:</span>
                                    <span class="location-badge">${task.location}</span>
                                </div>
                            ` : ''}
                        </div>
                        <div class="search-result-meta">
                            <span class="creation-date">تم الإنشاء: ${new Date(task.createdAt).toLocaleDateString('ar-SA')}</span>
                            ${task.updatedAt ? `<span class="update-date">آخر تحديث: ${new Date(task.updatedAt).toLocaleDateString('ar-SA')}</span>` : ''}
                        </div>
                    </div>
                `).join('');
            }

            // مسح البحث السريع
            clearQuickSearch() {
                document.getElementById('quickSearchInput').value = '';
                document.getElementById('quickSearchResults').innerHTML = '';
            }

            // مسح البحث المتقدم
            clearAdvancedSearch() {
                document.getElementById('searchTitle').value = '';
                document.getElementById('searchDescription').value = '';
                document.getElementById('searchPriority').value = '';
                document.getElementById('searchCategory').value = '';
                document.getElementById('searchStatus').value = '';
                document.getElementById('searchDueDate').value = '';
                document.getElementById('advancedSearchResults').innerHTML = '';
                document.getElementById('resultsCount').textContent = '0 نتيجة';
                this.currentSearchResults = [];
            }

            // نظام الإشعارات الداخلية الجديد
            showToast(message, type = 'info', duration = 3000) {
                const toastContainer = document.getElementById('toastContainer');
                if (!toastContainer) return;

                const toast = document.createElement('div');
                toast.className = `toast ${type}`;

                const icons = {
                    success: 'fas fa-check-circle',
                    error: 'fas fa-exclamation-circle',
                    warning: 'fas fa-exclamation-triangle',
                    info: 'fas fa-info-circle'
                };

                toast.innerHTML = `
                    <div class="toast-content">
                        <div class="toast-icon">
                            <i class="${icons[type] || icons.info}"></i>
                        </div>
                        <div class="toast-message">
                            <div class="toast-text">${message}</div>
                        </div>
                        <button class="toast-close" onclick="this.parentElement.parentElement.remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;

                toastContainer.appendChild(toast);

                // إظهار الإشعار
                setTimeout(() => {
                    toast.classList.add('show');
                }, 100);

                // إخفاء الإشعار تلقائياً
                if (duration > 0) {
                    setTimeout(() => {
                        toast.classList.remove('show');
                        setTimeout(() => {
                            if (toast.parentNode) {
                                toast.remove();
                            }
                        }, 400);
                    }, duration);
                }
            }

            // إشعار سريع للإضافة (ثانية واحدة)
            showQuickToast(message, type = 'success') {
                this.showToast(message, type, 1000);
            }

            // عرض إشعار (للتوافق مع الكود القديم)
            showNotification(message, type = 'info') {
                this.showToast(message, type);
            }
        }

        // متغير عام للتطبيق
        let taskManager;

        // دوال عامة للواجهة
        function showAddTaskModal() {
            document.getElementById('addTaskModal').classList.add('show');
            document.getElementById('taskTitle').focus();
        }

        function quickAddTask() {
            if (taskManager) {
                taskManager.quickAddTask();
            }
        }

        // إدارة لوحة البحث
        function toggleSearchPanel() {
            const panel = document.getElementById('searchPanel');
            panel.classList.toggle('show');

            if (panel.classList.contains('show')) {
                document.getElementById('quickSearchInput').focus();
            }
        }

        // إدارة تبويبات البحث
        function setupSearchTabs() {
            const searchTabs = document.querySelectorAll('.search-tab');
            if (searchTabs.length === 0) {
                console.warn('لم يتم العثور على تبويبات البحث');
                return;
            }

            searchTabs.forEach(tab => {
                tab.addEventListener('click', (e) => {
                    // إزالة النشط من جميع التبويبات
                    document.querySelectorAll('.search-tab').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.search-content').forEach(c => c.style.display = 'none');

                    // تفعيل التبويب المحدد
                    e.target.classList.add('active');
                    const tabType = e.target.dataset.tab;
                    const targetContent = document.getElementById(tabType + 'Search');
                    if (targetContent) {
                        targetContent.style.display = 'block';
                    }
                });
            });
        }

        // إدارة القوائم المنبثقة
        function setupDropdowns() {
            document.querySelectorAll('.dropdown-trigger').forEach(trigger => {
                trigger.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const dropdownId = trigger.dataset.dropdown;
                    const dropdown = document.getElementById(dropdownId);

                    // إغلاق جميع القوائم الأخرى
                    document.querySelectorAll('.dropdown-menu').forEach(menu => {
                        if (menu.id !== dropdownId) {
                            menu.classList.remove('show');
                        }
                    });
                    document.querySelectorAll('.dropdown-trigger').forEach(t => {
                        if (t !== trigger) {
                            t.classList.remove('active');
                        }
                    });

                    // تبديل القائمة الحالية
                    dropdown.classList.toggle('show');
                    trigger.classList.toggle('active');
                });
            });

            // إغلاق القوائم عند النقر خارجها
            document.addEventListener('click', () => {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.remove('show');
                });
                document.querySelectorAll('.dropdown-trigger').forEach(trigger => {
                    trigger.classList.remove('active');
                });
            });
        }

        // إدارة البحث السريع
        function setupQuickSearch() {
            const input = document.getElementById('quickSearchInput');
            if (!input) {
                console.warn('لم يتم العثور على حقل البحث السريع');
                return;
            }

            let searchTimeout;

            input.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (taskManager) {
                        taskManager.performQuickSearch(e.target.value);
                    }
                }, 300);
            });
        }

        // إدارة الإضافة السريعة
        function setupQuickAdd() {
            const input = document.getElementById('quickAddInput');
            if (!input) {
                console.warn('لم يتم العثور على حقل الإضافة السريعة');
                return;
            }

            // دعم Enter للإضافة
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    if (taskManager) {
                        taskManager.quickAddTask();
                    }
                }
            });

            // تأثيرات بصرية
            input.addEventListener('focus', () => {
                input.parentElement.style.borderColor = 'var(--primary)';
                input.parentElement.style.boxShadow = '0 0 0 3px rgba(99, 102, 241, 0.2)';
            });

            input.addEventListener('blur', () => {
                input.parentElement.style.borderColor = 'var(--glass-border)';
                input.parentElement.style.boxShadow = 'none';
            });
        }

        // إدارة النموذج المتقدم
        function setupAdvancedForm() {
            const recurringCheckbox = document.getElementById('taskRecurring');
            const recurringOptions = document.getElementById('recurringOptions');

            if (recurringCheckbox && recurringOptions) {
                recurringCheckbox.addEventListener('change', (e) => {
                    if (e.target.checked) {
                        recurringOptions.style.display = 'block';
                    } else {
                        recurringOptions.style.display = 'none';
                    }
                });
            } else {
                console.warn('لم يتم العثور على عناصر النموذج المتقدم');
            }
        }

        // دوال البحث المتقدم
        function performAdvancedSearch() {
            if (taskManager) {
                taskManager.performAdvancedSearch();
            }
        }

        function clearAdvancedSearch() {
            if (taskManager) {
                taskManager.clearAdvancedSearch();
            }
        }

        function clearQuickSearch() {
            if (taskManager) {
                taskManager.clearQuickSearch();
            }
        }

        // دوال التصدير
        function exportSearchResults(format) {
            if (taskManager) {
                taskManager.exportSearchResults(format);
            }
        }

        // دالة عرض قائمة المستخدم
        function showUserMenu() {
            // يمكن إضافة قائمة منبثقة للمستخدم هنا
            console.log('عرض قائمة المستخدم');
        }

        // دوال تأكيد الانتقال للصفحة الرئيسية
        function showNavigationModal() {
            document.getElementById('navigationModal').classList.add('show');
        }

        function closeNavigationModal() {
            document.getElementById('navigationModal').classList.remove('show');
        }

        function confirmNavigation() {
            // حفظ البيانات قبل الانتقال
            if (taskManager) {
                taskManager.saveTasks();
            }

            // إظهار إشعار الحفظ
            if (taskManager) {
                taskManager.showQuickToast('تم حفظ جميع التغييرات', 'success');
            }

            // الانتقال بعد ثانية
            setTimeout(() => {
                window.location.href = 'landing.html';
            }, 1000);
        }

        // دالة تبديل الوضع الليلي/النهاري
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';

            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            // تحديث أيقونة الزر
            const themeToggle = document.getElementById('themeToggle');
            if (themeToggle) {
                const icon = themeToggle.querySelector('i');
                if (newTheme === 'light') {
                    icon.className = 'fas fa-sun';
                } else {
                    icon.className = 'fas fa-moon';
                }
            }

            // إشعار التغيير
            if (taskManager) {
                taskManager.showQuickToast(`تم التبديل للوضع ${newTheme === 'light' ? 'النهاري' : 'الليلي'}`, 'info');
            }
        }

        // دوال التحليلات والتقارير
        function showProductivityReport() {
            if (taskManager) {
                taskManager.showToast('تقرير الإنتاجية قيد التطوير', 'info');
            }
        }

        function showTimeAnalysis() {
            if (taskManager) {
                taskManager.showToast('تحليل الوقت قيد التطوير', 'info');
            }
        }

        function showCategoryStats() {
            if (taskManager) {
                taskManager.showToast('إحصائيات التصنيفات قيد التطوير', 'info');
            }
        }

        // دوال النسخ الاحتياطي والمزامنة
        function backupData() {
            if (taskManager) {
                try {
                    const backupData = {
                        tasks: taskManager.tasks,
                        settings: taskManager.settings,
                        timestamp: new Date().toISOString(),
                        version: '1.0'
                    };

                    const dataStr = JSON.stringify(backupData, null, 2);
                    const dataBlob = new Blob([dataStr], {type: 'application/json'});
                    const url = URL.createObjectURL(dataBlob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `tdl-backup-${new Date().toISOString().split('T')[0]}.json`;
                    link.click();
                    URL.revokeObjectURL(url);

                    taskManager.showToast('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
                } catch (error) {
                    taskManager.showToast('فشل في إنشاء النسخة الاحتياطية', 'error');
                }
            }
        }

        function syncData() {
            if (taskManager) {
                taskManager.showToast('المزامنة قيد التطوير', 'info');
            }
        }

        // دوال الفلاتر المتقدمة
        function filterToday() {
            if (taskManager) {
                const today = new Date().toDateString();
                taskManager.currentFilter = 'today';
                taskManager.renderTasks();
            }
        }

        function filterWeek() {
            if (taskManager) {
                taskManager.currentFilter = 'week';
                taskManager.renderTasks();
            }
        }

        function filterRecurring() {
            if (taskManager) {
                taskManager.currentFilter = 'recurring';
                taskManager.renderTasks();
            }
        }

        function closeAddTaskModal() {
            document.getElementById('addTaskModal').classList.remove('show');
            document.getElementById('addTaskForm').reset();

            // إعادة تعيين وضع التعديل
            if (taskManager) {
                taskManager.editingTaskId = null;

                // إعادة تعيين عنوان النافذة
                document.querySelector('#addTaskModal .modal-header h3').textContent = 'إضافة مهمة جديدة';
                document.querySelector('#addTaskModal .btn.primary').textContent = 'إضافة المهمة';
            }
        }

        function addTask() {
            taskManager.addTask();
        }

        function showBulkActions() {
            // يمكن إضافة نافذة للإجراءات المتعددة هنا
            taskManager.showNotification('ميزة الإجراءات المتعددة قيد التطوير', 'info');
        }

        function exportTasks() {
            taskManager.exportTasks();
        }

        function importTasks() {
            taskManager.importTasks();
        }

        function clearCompleted() {
            if (taskManager) {
                taskManager.clearCompleted();
            }
        }

        function clearAll() {
            if (taskManager) {
                taskManager.clearAll();
            }
        }

        // تهيئة التطبيق
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 تحميل TDL المتقدم والمحسن...');

            try {
                // تهيئة مدير المهام
                taskManager = new AdvancedTaskManager();

                // تهيئة المكونات التفاعلية (مع معالجة الأخطاء)
                try {
                    setupSearchTabs();
                } catch (e) {
                    console.warn('تحذير: فشل في تهيئة تبويبات البحث:', e);
                }

                try {
                    setupDropdowns();
                } catch (e) {
                    console.warn('تحذير: فشل في تهيئة القوائم المنبثقة:', e);
                }

                try {
                    setupQuickSearch();
                } catch (e) {
                    console.warn('تحذير: فشل في تهيئة البحث السريع:', e);
                }

                try {
                    setupQuickAdd();
                } catch (e) {
                    console.warn('تحذير: فشل في تهيئة الإضافة السريعة:', e);
                }

                try {
                    setupAdvancedForm();
                } catch (e) {
                    console.warn('تحذير: فشل في تهيئة النموذج المتقدم:', e);
                }

                // تهيئة الوضع الليلي/النهاري
                try {
                    const savedTheme = localStorage.getItem('theme') || 'dark';
                    document.documentElement.setAttribute('data-theme', savedTheme);

                    const themeToggle = document.getElementById('themeToggle');
                    if (themeToggle) {
                        const icon = themeToggle.querySelector('i');
                        if (savedTheme === 'light') {
                            icon.className = 'fas fa-sun';
                        } else {
                            icon.className = 'fas fa-moon';
                        }
                    }
                } catch (e) {
                    console.warn('تحذير: فشل في تهيئة الوضع الليلي/النهاري:', e);
                }

                console.log('✅ تم تحميل التطبيق بنجاح');

                // إضافة أنيميشن CSS للإشعارات والتحسينات
                const style = document.createElement('style');
                style.textContent = `
                    @keyframes slideInRight {
                        from {
                            opacity: 0;
                            transform: translateX(100%);
                        }
                        to {
                            opacity: 1;
                            transform: translateX(0);
                        }
                    }

                    @keyframes slideOutRight {
                        from {
                            opacity: 1;
                            transform: translateX(0);
                        }
                        to {
                            opacity: 0;
                            transform: translateX(100%);
                        }
                    }

                    .notification.success {
                        border-left: 4px solid var(--success);
                    }

                    .notification.error {
                        border-left: 4px solid var(--danger);
                    }

                    .notification.info {
                        border-left: 4px solid var(--info);
                    }

                    .no-results {
                        text-align: center;
                        padding: 2rem;
                        color: var(--text-secondary);
                        font-style: italic;
                    }

                    .search-result-description {
                        color: var(--text-secondary);
                        margin: 0.5rem 0;
                        font-size: 0.9rem;
                        line-height: 1.4;
                    }

                    .search-result-details {
                        margin: 1rem 0;
                    }

                    .detail-row {
                        display: flex;
                        align-items: center;
                        gap: 0.5rem;
                        margin-bottom: 0.3rem;
                    }

                    .detail-label {
                        font-weight: 600;
                        color: var(--text-primary);
                        min-width: 80px;
                    }

                    .tags-container {
                        display: flex;
                        gap: 0.3rem;
                        flex-wrap: wrap;
                    }

                    .tag {
                        background: var(--primary);
                        color: white;
                        padding: 0.2rem 0.5rem;
                        border-radius: 12px;
                        font-size: 0.7rem;
                        font-weight: 500;
                    }

                    .time-badge,
                    .location-badge {
                        background: var(--info);
                        color: white;
                        padding: 0.2rem 0.5rem;
                        border-radius: 6px;
                        font-size: 0.8rem;
                    }

                    .search-result-meta {
                        display: flex;
                        justify-content: space-between;
                        margin-top: 1rem;
                        padding-top: 0.5rem;
                        border-top: 1px solid var(--glass-border);
                        font-size: 0.8rem;
                        color: var(--text-muted);
                    }

                    .creation-date,
                    .update-date {
                        font-style: italic;
                    }
                `;
                document.head.appendChild(style);

                // رسالة ترحيب تختفي بعد 3 ثوان
                setTimeout(() => {
                    if (taskManager && taskManager.currentUser) {
                        taskManager.showToast(
                            `مرحباً بك في TDL المتطور، ${taskManager.currentUser.fullName}! 🚀`,
                            'success',
                            3000
                        );
                    }
                }, 1000);

            } catch (error) {
                console.error('❌ خطأ في تحميل التطبيق:', error);

                // عرض رسالة خطأ مفصلة
                const errorMessage = `
                    حدث خطأ في تحميل التطبيق:
                    ${error.message}

                    يرجى:
                    1. إعادة تحميل الصفحة
                    2. التأكد من اتصال الإنترنت
                    3. مسح ذاكرة التخزين المؤقت للمتصفح
                `;

                // إنشاء عنصر خطأ في الصفحة
                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: #dc2626;
                    color: white;
                    padding: 2rem;
                    border-radius: 10px;
                    z-index: 9999;
                    max-width: 400px;
                    text-align: center;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
                `;
                errorDiv.innerHTML = `
                    <h3>خطأ في التطبيق</h3>
                    <p style="margin: 1rem 0;">${error.message}</p>
                    <button onclick="location.reload()" style="
                        background: white;
                        color: #dc2626;
                        border: none;
                        padding: 0.5rem 1rem;
                        border-radius: 5px;
                        cursor: pointer;
                        font-weight: bold;
                    ">إعادة تحميل</button>
                `;
                document.body.appendChild(errorDiv);
            }
        });

        // معالج أخطاء عام
        window.addEventListener('error', (e) => {
            console.error('خطأ JavaScript:', e.error);

            // تجنب عرض أخطاء متكررة
            if (window.lastErrorTime && Date.now() - window.lastErrorTime < 1000) {
                return;
            }
            window.lastErrorTime = Date.now();

            // عرض رسالة خطأ بسيطة
            const errorNotification = document.createElement('div');
            errorNotification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #dc2626;
                color: white;
                padding: 1rem;
                border-radius: 8px;
                z-index: 9999;
                max-width: 300px;
                animation: slideInRight 0.3s ease;
            `;
            errorNotification.innerHTML = `
                <strong>خطأ في التطبيق</strong><br>
                <small>${e.error?.message || 'خطأ غير معروف'}</small>
            `;
            document.body.appendChild(errorNotification);

            // إزالة الإشعار بعد 5 ثوان
            setTimeout(() => {
                if (errorNotification.parentNode) {
                    errorNotification.style.animation = 'slideOutRight 0.3s ease';
                    setTimeout(() => {
                        if (errorNotification.parentNode) {
                            document.body.removeChild(errorNotification);
                        }
                    }, 300);
                }
            }, 5000);
        });

        // إغلاق المودال عند الضغط على Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                // إغلاق النوافذ بالترتيب
                const confirmationModal = document.getElementById('confirmationModal');
                const alertModal = document.getElementById('alertModal');
                const addTaskModal = document.getElementById('addTaskModal');

                if (confirmationModal.classList.contains('show')) {
                    confirmationModal.classList.remove('show');
                } else if (alertModal.classList.contains('show')) {
                    alertModal.classList.remove('show');
                } else if (addTaskModal.classList.contains('show')) {
                    closeAddTaskModal();
                } else if (taskManager) {
                    taskManager.closeSidebar();
                }
            }

            // اختصارات لوحة المفاتيح
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'n':
                        e.preventDefault();
                        showAddTaskModal();
                        break;
                    case 's':
                        e.preventDefault();
                        if (taskManager) {
                            taskManager.exportTasks();
                        }
                        break;
                }
            }
        });

        // إغلاق المودال عند الضغط خارجه
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                closeAddTaskModal();
            }
        });
    </script>
</body>
</html>
