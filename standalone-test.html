<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام TDL - مستقل</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            color: #e0e0e0;
            padding: 2rem;
            line-height: 1.6;
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            padding: 2rem;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 3rem;
        }
        .header h1 {
            font-size: 3rem;
            background: linear-gradient(135deg, #6366f1, #8b5cf6, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }
        .section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .section h2 {
            color: #6366f1;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #a0a0a0;
        }
        .form-group input {
            width: 100%;
            padding: 1rem;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            color: white;
            font-size: 1rem;
            box-sizing: border-box;
            transition: all 0.3s ease;
        }
        .form-group input:focus {
            outline: none;
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }
        .btn {
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 10px;
            cursor: pointer;
            margin: 0.5rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
        }
        .btn.danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }
        .result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            min-height: 50px;
        }
        .success { 
            background: rgba(16, 185, 129, 0.2); 
            border: 1px solid rgba(16, 185, 129, 0.3);
        }
        .error { 
            background: rgba(239, 68, 68, 0.2); 
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
        .info { 
            background: rgba(59, 130, 246, 0.2); 
            border: 1px solid rgba(59, 130, 246, 0.3);
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار نظام TDL</h1>
            <p>نظام إدارة المستخدمين المدمج</p>
        </div>

        <div class="section">
            <h2>📊 حالة النظام</h2>
            <button class="btn" onclick="checkSystem()">فحص النظام</button>
            <div id="system-status" class="result info">انقر "فحص النظام" للبدء...</div>
        </div>

        <div class="grid">
            <div class="section">
                <h2>📝 إنشاء حساب</h2>
                <div class="form-group">
                    <label>الاسم الكامل:</label>
                    <input type="text" id="regFullName" value="مستخدم تجريبي">
                </div>
                <div class="form-group">
                    <label>اسم المستخدم:</label>
                    <input type="text" id="regUsername" value="">
                </div>
                <div class="form-group">
                    <label>البريد الإلكتروني:</label>
                    <input type="email" id="regEmail" value="">
                </div>
                <div class="form-group">
                    <label>كلمة المرور:</label>
                    <input type="password" id="regPassword" value="123456">
                </div>
                <button class="btn" onclick="testRegister()">إنشاء حساب</button>
                <div id="register-result" class="result"></div>
            </div>

            <div class="section">
                <h2>🔐 تسجيل الدخول</h2>
                <div class="form-group">
                    <label>اسم المستخدم:</label>
                    <input type="text" id="loginUsername" value="">
                </div>
                <div class="form-group">
                    <label>كلمة المرور:</label>
                    <input type="password" id="loginPassword" value="123456">
                </div>
                <button class="btn" onclick="testLogin()">تسجيل الدخول</button>
                <div id="login-result" class="result"></div>
            </div>
        </div>

        <div class="section">
            <h2>🗂️ إدارة البيانات</h2>
            <button class="btn" onclick="showUsers()">عرض المستخدمين</button>
            <button class="btn" onclick="showSession()">عرض الجلسة</button>
            <button class="btn danger" onclick="clearData()">مسح البيانات</button>
            <div id="data-result" class="result"></div>
        </div>
    </div>

    <script>
        // نظام إدارة المستخدمين المدمج
        class EmbeddedUserManager {
            constructor() {
                this.currentUser = null;
                this.sessionTimeout = 24 * 60 * 60 * 1000; // 24 ساعة
                console.log('✅ تم إنشاء EmbeddedUserManager');
            }

            // تحميل المستخدمين
            loadUsers() {
                try {
                    const users = localStorage.getItem('tdl_users');
                    return users ? JSON.parse(users) : {};
                } catch (error) {
                    console.error('خطأ في تحميل المستخدمين:', error);
                    return {};
                }
            }

            // حفظ المستخدمين
            saveUsers(users) {
                try {
                    localStorage.setItem('tdl_users', JSON.stringify(users));
                    return true;
                } catch (error) {
                    console.error('خطأ في حفظ المستخدمين:', error);
                    return false;
                }
            }

            // تشفير كلمة المرور
            hashPassword(password) {
                let hash = 0;
                for (let i = 0; i < password.length; i++) {
                    const char = password.charCodeAt(i);
                    hash = ((hash << 5) - hash) + char;
                    hash = hash & hash;
                }
                return hash.toString();
            }

            // التحقق من البريد الإلكتروني
            isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            // إنشاء حساب
            async register(userData) {
                const { username, email, password, fullName } = userData;

                // التحقق من البيانات
                if (!username || !email || !password || !fullName) {
                    throw new Error('جميع الحقول مطلوبة');
                }

                if (username.length < 3) {
                    throw new Error('اسم المستخدم يجب أن يكون 3 أحرف على الأقل');
                }

                if (password.length < 6) {
                    throw new Error('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                }

                if (!this.isValidEmail(email)) {
                    throw new Error('البريد الإلكتروني غير صالح');
                }

                const users = this.loadUsers();

                // التحقق من عدم وجود المستخدم
                if (users[username]) {
                    throw new Error('اسم المستخدم موجود بالفعل');
                }

                if (Object.values(users).some(user => user.email === email)) {
                    throw new Error('البريد الإلكتروني مستخدم بالفعل');
                }

                // إنشاء المستخدم
                const newUser = {
                    username,
                    email,
                    password: this.hashPassword(password),
                    fullName,
                    createdAt: new Date().toISOString(),
                    lastLogin: null,
                    stats: { totalTasks: 0, completedTasks: 0, loginCount: 0 }
                };

                users[username] = newUser;
                const saved = this.saveUsers(users);

                if (saved) {
                    return { success: true, message: 'تم إنشاء الحساب بنجاح' };
                } else {
                    throw new Error('فشل في حفظ بيانات المستخدم');
                }
            }

            // تسجيل الدخول
            async login(username, password) {
                if (!username || !password) {
                    throw new Error('اسم المستخدم وكلمة المرور مطلوبان');
                }

                const users = this.loadUsers();
                const user = users[username];

                if (!user) {
                    throw new Error('اسم المستخدم غير موجود');
                }

                if (user.password !== this.hashPassword(password)) {
                    throw new Error('كلمة المرور غير صحيحة');
                }

                // تحديث بيانات الدخول
                user.lastLogin = new Date().toISOString();
                user.stats.loginCount++;
                users[username] = user;
                this.saveUsers(users);

                // إنشاء جلسة
                this.currentUser = user;
                this.createSession(user);

                return { success: true, user: this.getSafeUserData(user) };
            }

            // إنشاء جلسة
            createSession(user) {
                const session = {
                    username: user.username,
                    loginTime: Date.now(),
                    expiresAt: Date.now() + this.sessionTimeout
                };

                localStorage.setItem('tdl_session', JSON.stringify(session));
                localStorage.setItem('tdl_current_user', JSON.stringify(this.getSafeUserData(user)));
            }

            // فحص الجلسة
            checkSession() {
                try {
                    const sessionData = localStorage.getItem('tdl_session');
                    const currentUserData = localStorage.getItem('tdl_current_user');

                    if (!sessionData || !currentUserData) {
                        return false;
                    }

                    const session = JSON.parse(sessionData);
                    const currentUser = JSON.parse(currentUserData);

                    if (session && currentUser && Date.now() < session.expiresAt) {
                        const users = this.loadUsers();
                        const user = users[session.username];
                        if (user) {
                            this.currentUser = user;
                            return true;
                        }
                    }
                } catch (error) {
                    console.error('خطأ في فحص الجلسة:', error);
                }

                this.logout();
                return false;
            }

            // تسجيل الخروج
            logout() {
                this.currentUser = null;
                localStorage.removeItem('tdl_session');
                localStorage.removeItem('tdl_current_user');
            }

            // بيانات آمنة
            getSafeUserData(user) {
                const { password, ...safeData } = user;
                return safeData;
            }

            // المستخدم الحالي
            getCurrentUser() {
                return this.currentUser ? this.getSafeUserData(this.currentUser) : null;
            }
        }

        // إنشاء مدير المستخدمين
        const userManager = new EmbeddedUserManager();

        // إنشاء أسماء عشوائية
        const randomId = Date.now();
        document.getElementById('regUsername').value = 'user_' + randomId;
        document.getElementById('regEmail').value = 'test_' + randomId + '@example.com';
        document.getElementById('loginUsername').value = 'user_' + randomId;

        // فحص النظام
        function checkSystem() {
            const result = document.getElementById('system-status');
            try {
                const users = userManager.loadUsers();
                const userCount = Object.keys(users).length;
                const hasSession = userManager.checkSession();
                const currentUser = userManager.getCurrentUser();

                const info = `✅ النظام يعمل بشكل مثالي!

📊 معلومات النظام:
• حالة userManager: ✅ محمل ويعمل
• التخزين المحلي: ✅ متاح
• عدد المستخدمين: ${userCount}
• الجلسة الحالية: ${hasSession ? '✅ نشطة' : '❌ غير نشطة'}
• المستخدم الحالي: ${currentUser ? currentUser.fullName : 'لا يوجد'}
• المتصفح: ${navigator.userAgent.split(' ')[0]}
• الوقت: ${new Date().toLocaleString('ar')}`;

                result.className = 'result success';
                result.textContent = info;
            } catch (error) {
                result.className = 'result error';
                result.textContent = '❌ خطأ في فحص النظام: ' + error.message;
            }
        }

        // اختبار التسجيل
        async function testRegister() {
            const result = document.getElementById('register-result');
            try {
                const userData = {
                    fullName: document.getElementById('regFullName').value,
                    username: document.getElementById('regUsername').value,
                    email: document.getElementById('regEmail').value,
                    password: document.getElementById('regPassword').value
                };

                const registerResult = await userManager.register(userData);

                if (registerResult.success) {
                    result.className = 'result success';
                    result.textContent = `✅ تم إنشاء الحساب بنجاح!

👤 بيانات الحساب:
• الاسم: ${userData.fullName}
• اسم المستخدم: ${userData.username}
• البريد: ${userData.email}
• تاريخ الإنشاء: ${new Date().toLocaleString('ar')}

🎉 يمكنك الآن تسجيل الدخول!`;

                    // تحديث حقول تسجيل الدخول
                    document.getElementById('loginUsername').value = userData.username;
                } else {
                    result.className = 'result error';
                    result.textContent = '❌ فشل في إنشاء الحساب: ' + registerResult.message;
                }
            } catch (error) {
                result.className = 'result error';
                result.textContent = '❌ خطأ في التسجيل: ' + error.message;
            }
        }

        // اختبار تسجيل الدخول
        async function testLogin() {
            const result = document.getElementById('login-result');
            try {
                const username = document.getElementById('loginUsername').value;
                const password = document.getElementById('loginPassword').value;

                const loginResult = await userManager.login(username, password);

                if (loginResult.success) {
                    result.className = 'result success';
                    result.textContent = `✅ تم تسجيل الدخول بنجاح!

👤 مرحباً ${loginResult.user.fullName}!
• اسم المستخدم: ${loginResult.user.username}
• البريد: ${loginResult.user.email}
• آخر دخول: ${new Date(loginResult.user.lastLogin).toLocaleString('ar')}
• عدد مرات الدخول: ${loginResult.user.stats.loginCount}

🎯 الجلسة نشطة الآن!`;
                } else {
                    result.className = 'result error';
                    result.textContent = '❌ فشل في تسجيل الدخول: ' + loginResult.message;
                }
            } catch (error) {
                result.className = 'result error';
                result.textContent = '❌ خطأ في تسجيل الدخول: ' + error.message;
            }
        }

        // عرض المستخدمين
        function showUsers() {
            const result = document.getElementById('data-result');
            try {
                const users = userManager.loadUsers();
                const userList = Object.keys(users).map(username => {
                    const user = users[username];
                    return `• ${user.fullName} (@${username}) - ${user.email}`;
                }).join('\n');

                result.className = 'result info';
                result.textContent = `📋 المستخدمين المحفوظين (${Object.keys(users).length}):

${userList || 'لا يوجد مستخدمين مسجلين'}`;
            } catch (error) {
                result.className = 'result error';
                result.textContent = '❌ خطأ في عرض المستخدمين: ' + error.message;
            }
        }

        // عرض الجلسة
        function showSession() {
            const result = document.getElementById('data-result');
            try {
                const sessionData = localStorage.getItem('tdl_session');
                const currentUserData = localStorage.getItem('tdl_current_user');

                if (sessionData && currentUserData) {
                    const session = JSON.parse(sessionData);
                    const user = JSON.parse(currentUserData);
                    const timeLeft = session.expiresAt - Date.now();
                    const hoursLeft = Math.floor(timeLeft / (1000 * 60 * 60));

                    result.className = 'result info';
                    result.textContent = `🎫 معلومات الجلسة:

👤 المستخدم: ${user.fullName}
🔐 اسم المستخدم: ${user.username}
⏰ وقت الدخول: ${new Date(session.loginTime).toLocaleString('ar')}
⏳ تنتهي في: ${hoursLeft} ساعة
📧 البريد: ${user.email}`;
                } else {
                    result.className = 'result info';
                    result.textContent = '❌ لا توجد جلسة نشطة';
                }
            } catch (error) {
                result.className = 'result error';
                result.textContent = '❌ خطأ في عرض الجلسة: ' + error.message;
            }
        }

        // مسح البيانات
        function clearData() {
            const result = document.getElementById('data-result');
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟\nهذا سيحذف جميع المستخدمين والجلسات!')) {
                try {
                    localStorage.clear();
                    userManager.currentUser = null;
                    result.className = 'result success';
                    result.textContent = '✅ تم مسح جميع البيانات بنجاح!\n\nيمكنك الآن إنشاء حسابات جديدة.';
                } catch (error) {
                    result.className = 'result error';
                    result.textContent = '❌ خطأ في مسح البيانات: ' + error.message;
                }
            }
        }

        // فحص النظام عند التحميل
        setTimeout(() => {
            checkSystem();
        }, 500);
    </script>
</body>
</html>
