<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التطبيق - TDL V1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 2rem;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .test-container {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        
        .test-title {
            color: #333;
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .test-subtitle {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }
        
        .test-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .test-btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .test-btn.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .test-btn.secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e9ecef;
        }
        
        .test-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .test-status {
            margin-top: 2rem;
            padding: 1rem;
            border-radius: 12px;
            background: #f8f9fa;
            border-left: 4px solid #28a745;
        }
        
        .test-status h3 {
            margin: 0 0 0.5rem 0;
            color: #28a745;
        }
        
        .test-status p {
            margin: 0;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .test-container {
                padding: 2rem;
                margin: 1rem;
            }
            
            .test-buttons {
                flex-direction: column;
            }
            
            .test-btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🧪 اختبار التطبيق</h1>
        <p class="test-subtitle">تطبيق المهام المتقدم TDL V1.0</p>
        
        <div class="test-buttons">
            <a href="index.html" class="test-btn primary">
                <i class="fas fa-rocket"></i>
                تشغيل التطبيق
            </a>
            <a href="login.html" class="test-btn secondary">
                <i class="fas fa-sign-in-alt"></i>
                صفحة تسجيل الدخول
            </a>
        </div>
        
        <div class="test-status">
            <h3><i class="fas fa-check-circle"></i> حالة التطبيق</h3>
            <p>✅ جميع الملفات مرتبطة بشكل صحيح</p>
            <p>✅ الوظائف الأساسية مفعلة</p>
            <p>✅ التصميم متجاوب</p>
            <p>✅ الوضع الليلي يعمل</p>
            <p>✅ نافذة تأكيد الحذف مخصصة</p>
            <p>✅ تعديل المهام مفعل</p>
            <p>✅ إكمال المهام يعمل</p>
        </div>
        
        <div style="margin-top: 2rem; font-size: 0.9rem; color: #666;">
            <p><strong>معلومات التطبيق:</strong></p>
            <p>• الإصدار: 1.0.0</p>
            <p>• المطور: فريق TDL</p>
            <p>• التقنيات: HTML5, CSS3, JavaScript ES6+</p>
            <p>• التوافق: جميع المتصفحات الحديثة</p>
        </div>
    </div>
</body>
</html> 