// TDL V1.0 - Service Worker Registration

// التحقق من دعم Service Worker في المتصفح
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('../sw.js')
      .then((registration) => {
        console.log('Service Worker تم تسجيله بنجاح:', registration.scope);
        
        // طلب إذن الإشعارات
        requestNotificationPermission();
        
        // التحقق من وجود تحديثات للـ Service Worker
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              showUpdateNotification();
            }
          });
        });
      })
      .catch((error) => {
        console.error('فشل تسجيل Service Worker:', error);
      });
      
    // التعامل مع Service Workers الموجودة مسبقاً
    if (navigator.serviceWorker.controller) {
      console.log('يتم استخدام Service Worker نشط حالياً');
    }
  });
  
  // إظهار إشعار بوجود تحديث للتطبيق
  function showUpdateNotification() {
    // استخدام دالة showNotification من utils.js إذا كانت متاحة
    if (typeof showNotification === 'function') {
      showNotification('تحديث متاح', 'يوجد إصدار جديد من التطبيق. قم بإعادة تحميل الصفحة للتحديث.', 'info', true, 0, () => {
        // تحديث الصفحة عند النقر على الإشعار
        window.location.reload();
      });
    } else {
      // استخدام إشعار بسيط إذا لم تكن الدالة متاحة
      const updateNotification = document.createElement('div');
      updateNotification.className = 'update-notification';
      updateNotification.innerHTML = `
        <div class="update-notification-content">
          <i class="fas fa-sync-alt"></i>
          <span>يوجد إصدار جديد من التطبيق.</span>
          <div class="update-actions">
            <button class="btn btn-primary update-now-btn">تحديث الآن</button>
            <button class="btn btn-outline update-later-btn">لاحقاً</button>
          </div>
          <button class="close-btn"><i class="fas fa-times"></i></button>
        </div>
      `;
      
      document.body.appendChild(updateNotification);
      
      // إظهار الإشعار بتأثير بصري
      setTimeout(() => {
        updateNotification.classList.add('show');
      }, 100);
      
      // تحديث الصفحة عند النقر على زر التحديث
      updateNotification.querySelector('.update-now-btn').addEventListener('click', () => {
        window.location.reload();
      });
      
      // إخفاء الإشعار عند النقر على زر لاحقاً
      updateNotification.querySelector('.update-later-btn').addEventListener('click', () => {
        updateNotification.classList.remove('show');
        setTimeout(() => {
          updateNotification.remove();
        }, 300);
      });
      
      // إغلاق الإشعار عند النقر على زر الإغلاق
      updateNotification.querySelector('.close-btn').addEventListener('click', () => {
        updateNotification.classList.remove('show');
        setTimeout(() => {
          updateNotification.remove();
        }, 300);
      });
    }
  }
  
  // طلب إذن الإشعارات
  function requestNotificationPermission() {
    if ('Notification' in window && Notification.permission !== 'denied') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          console.log('تم منح إذن الإشعارات');
          // يمكن تسجيل اشتراك الإشعارات هنا إذا كان مطلوباً
          subscribeToPushNotifications();
        }
      });
    }
  }
  
  // الاشتراك في إشعارات Push
  function subscribeToPushNotifications() {
    if ('PushManager' in window) {
      navigator.serviceWorker.ready.then(registration => {
        // التحقق من وجود اشتراك سابق
        registration.pushManager.getSubscription().then(subscription => {
          if (subscription) {
            console.log('المستخدم مشترك بالفعل في الإشعارات');
            return subscription;
          }
          
          // إنشاء اشتراك جديد (في بيئة الإنتاج، يجب استخدام مفتاح VAPID حقيقي)
          // هذا مجرد مثال، وفي التطبيق الحقيقي يجب الحصول على المفتاح من الخادم
          const vapidPublicKey = 'BEl62iUYgUivxIkv69yViEuiBIa-Ib9-SkvMeAtA3LFgDzkrxZJjSgSnfckjBJuBkr3qBUYIHBQFLXYp5Nksh8U';
          const convertedVapidKey = urlBase64ToUint8Array(vapidPublicKey);
          
          return registration.pushManager.subscribe({
            userVisibleOnly: true,
            applicationServerKey: convertedVapidKey
          }).then(newSubscription => {
            console.log('تم الاشتراك في الإشعارات بنجاح');
            // في التطبيق الحقيقي، يجب إرسال بيانات الاشتراك إلى الخادم
            // sendSubscriptionToServer(newSubscription);
            return newSubscription;
          }).catch(error => {
            console.error('فشل الاشتراك في الإشعارات:', error);
          });
        });
      });
    }
  }
  
  // تحويل مفتاح VAPID من Base64 إلى Uint8Array
  function urlBase64ToUint8Array(base64String) {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/\-/g, '+')
      .replace(/_/g, '/');
    
    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);
    
    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    
    return outputArray;
  }
  
  // إرسال رسالة للـ Service Worker لتخطي الانتظار وتطبيق التحديث
  function updateServiceWorker() {
    navigator.serviceWorker.ready.then((registration) => {
      registration.waiting.postMessage({ type: 'SKIP_WAITING' });
    });
  }
  
  // الاستماع لحدث تحديث Service Worker
  let refreshing = false;
  navigator.serviceWorker.addEventListener('controllerchange', () => {
    if (!refreshing) {
      refreshing = true;
      window.location.reload();
    }
  });
  
  // الاستماع للرسائل من Service Worker
  navigator.serviceWorker.addEventListener('message', event => {
    if (event.data) {
      // التعامل مع رسائل مختلفة من Service Worker
      switch (event.data.type) {
        case 'UPDATE_READY':
          showUpdateNotification();
          break;
        case 'SYNC_COMPLETE':
          if (typeof showNotification === 'function') {
            showNotification('مزامنة المهام', event.data.message, 'success', true, 3000);
          }
          break;
        case 'SW_VERSION':
          console.log('إصدار Service Worker:', event.data.version);
          // تحديث إصدار التطبيق في واجهة المستخدم إذا كان ذلك مطلوباً
          const appVersionElement = document.getElementById('appVersion');
          if (appVersionElement) {
            appVersionElement.textContent = event.data.version;
          }
          break;
      }
    }
  });
}

// التحقق من حالة الاتصال بالإنترنت
window.addEventListener('online', updateOnlineStatus);
window.addEventListener('offline', updateOnlineStatus);

function updateOnlineStatus() {
  const isOnline = navigator.onLine;
  document.body.classList.toggle('offline-mode', !isOnline);
  
  // إظهار إشعار بحالة الاتصال
  const statusElement = document.getElementById('connectionStatus') || createConnectionStatusElement();
  
  if (isOnline) {
    statusElement.innerHTML = '<i class="fas fa-wifi"></i> متصل بالإنترنت';
    statusElement.className = 'connection-status online show';
    
    // إخفاء الإشعار بعد فترة
    setTimeout(() => {
      statusElement.classList.remove('show');
    }, 3000);
    
    // مزامنة المهام المعلقة إذا كانت هناك مهام في انتظار المزامنة
    if (typeof syncPendingTasks === 'function') {
      syncPendingTasks();
    }
  } else {
    statusElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i> غير متصل بالإنترنت - الوضع المحلي نشط';
    statusElement.className = 'connection-status offline show';
  }
}

// إنشاء عنصر لعرض حالة الاتصال
function createConnectionStatusElement() {
  const statusElement = document.createElement('div');
  statusElement.id = 'connectionStatus';
  statusElement.className = 'connection-status';
  document.body.appendChild(statusElement);
  return statusElement;
}

// تحديث حالة الاتصال عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', updateOnlineStatus);