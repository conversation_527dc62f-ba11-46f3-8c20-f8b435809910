// TDL V1.0 - Authentication Interface
// واجهة تسجيل الدخول والتسجيل

import userManager from './user-manager.js';

class AuthInterface {
  constructor() {
    this.currentForm = 'login';
    this.selectedAvatar = null;
    this.init();
  }

  // تهيئة الواجهة
  init() {
    this.bindEvents();
    this.setupPasswordToggles();
    this.setupAvatarSelection();
    this.checkExistingSession();
  }

  // ربط الأحداث
  bindEvents() {
    // تبديل النماذج
    document.getElementById('showRegister')?.addEventListener('click', (e) => {
      e.preventDefault();
      this.showForm('register');
    });

    document.getElementById('showLogin')?.addEventListener('click', (e) => {
      e.preventDefault();
      this.showForm('login');
    });

    // إرسال النماذج
    document.getElementById('loginFormElement')?.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleLogin();
    });

    document.getElementById('registerFormElement')?.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleRegister();
    });

    // التحقق من كلمة المرور في الوقت الفعلي
    document.getElementById('confirmPassword')?.addEventListener('input', () => {
      this.validatePasswordMatch();
    });

    // التحقق من اسم المستخدم
    document.getElementById('registerUsername')?.addEventListener('blur', () => {
      this.validateUsername();
    });
  }

  // إعداد أزرار إظهار/إخفاء كلمة المرور
  setupPasswordToggles() {
    document.querySelectorAll('.toggle-password').forEach(btn => {
      btn.addEventListener('click', () => {
        const targetId = btn.dataset.target;
        const input = document.getElementById(targetId);
        const icon = btn.querySelector('i');

        if (input.type === 'password') {
          input.type = 'text';
          icon.className = 'fas fa-eye-slash';
        } else {
          input.type = 'password';
          icon.className = 'fas fa-eye';
        }
      });
    });
  }

  // إعداد اختيار الصورة الرمزية
  setupAvatarSelection() {
    const uploadBtn = document.querySelector('[data-type="upload"]');
    const generateBtn = document.querySelector('[data-type="generate"]');
    const fileInput = document.getElementById('avatarUpload');

    uploadBtn?.addEventListener('click', () => {
      fileInput.click();
    });

    generateBtn?.addEventListener('click', () => {
      this.generateRandomAvatar();
    });

    fileInput?.addEventListener('change', (e) => {
      this.handleAvatarUpload(e.target.files[0]);
    });
  }

  // فحص الجلسة الموجودة
  checkExistingSession() {
    if (userManager.checkSession()) {
      window.location.href = 'index.html';
    }
  }

  // عرض النموذج المحدد
  showForm(formType) {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');

    if (formType === 'login') {
      loginForm.classList.remove('hidden');
      registerForm.classList.add('hidden');
      this.currentForm = 'login';
    } else {
      loginForm.classList.add('hidden');
      registerForm.classList.remove('hidden');
      this.currentForm = 'register';
    }

    // تأثير انتقال سلس
    setTimeout(() => {
      const activeForm = document.querySelector('.auth-form-container:not(.hidden)');
      if (activeForm) {
        activeForm.style.transform = 'scale(0.95)';
        setTimeout(() => {
          activeForm.style.transform = 'scale(1)';
        }, 100);
      }
    }, 50);
  }

  // معالجة تسجيل الدخول
  async handleLogin() {
    const form = document.getElementById('loginFormElement');
    const formData = new FormData(form);
    const username = formData.get('username').trim();
    const password = formData.get('password');

    if (!this.validateLoginForm(username, password)) {
      return;
    }

    this.showLoader(true);

    try {
      const result = await userManager.login(username, password);
      
      if (result.success) {
        this.showNotification('تم تسجيل الدخول بنجاح!', 'success');
        
        // انتظار قصير لإظهار الرسالة ثم التوجه للتطبيق
        setTimeout(() => {
          window.location.href = 'index.html';
        }, 1500);
      }
    } catch (error) {
      this.showNotification(error.message, 'error');
    } finally {
      this.showLoader(false);
    }
  }

  // معالجة التسجيل
  async handleRegister() {
    const form = document.getElementById('registerFormElement');
    const formData = new FormData(form);
    
    const userData = {
      fullName: formData.get('fullName').trim(),
      username: formData.get('username').trim(),
      email: formData.get('email').trim(),
      password: formData.get('password'),
      avatar: this.selectedAvatar
    };

    const confirmPassword = formData.get('confirmPassword');

    if (!this.validateRegisterForm(userData, confirmPassword)) {
      return;
    }

    this.showLoader(true);

    try {
      const result = await userManager.register(userData);
      
      if (result.success) {
        this.showNotification('تم إنشاء الحساب بنجاح!', 'success');
        
        // تسجيل دخول تلقائي
        setTimeout(async () => {
          try {
            await userManager.login(userData.username, userData.password);
            window.location.href = 'index.html';
          } catch (error) {
            this.showForm('login');
            this.showNotification('تم إنشاء الحساب، يرجى تسجيل الدخول', 'success');
          }
        }, 1500);
      }
    } catch (error) {
      this.showNotification(error.message, 'error');
    } finally {
      this.showLoader(false);
    }
  }

  // التحقق من صحة نموذج تسجيل الدخول
  validateLoginForm(username, password) {
    if (!username) {
      this.showNotification('يرجى إدخال اسم المستخدم', 'error');
      return false;
    }

    if (!password) {
      this.showNotification('يرجى إدخال كلمة المرور', 'error');
      return false;
    }

    return true;
  }

  // التحقق من صحة نموذج التسجيل
  validateRegisterForm(userData, confirmPassword) {
    const { fullName, username, email, password } = userData;

    if (!fullName || fullName.length < 2) {
      this.showNotification('يرجى إدخال الاسم الكامل (حرفين على الأقل)', 'error');
      return false;
    }

    if (!username || username.length < 3) {
      this.showNotification('اسم المستخدم يجب أن يكون 3 أحرف على الأقل', 'error');
      return false;
    }

    if (!email || !this.isValidEmail(email)) {
      this.showNotification('يرجى إدخال بريد إلكتروني صالح', 'error');
      return false;
    }

    if (!password || password.length < 6) {
      this.showNotification('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
      return false;
    }

    if (password !== confirmPassword) {
      this.showNotification('كلمات المرور غير متطابقة', 'error');
      return false;
    }

    if (!document.getElementById('agreeTerms').checked) {
      this.showNotification('يجب الموافقة على الشروط والأحكام', 'error');
      return false;
    }

    return true;
  }

  // التحقق من تطابق كلمة المرور
  validatePasswordMatch() {
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const confirmInput = document.getElementById('confirmPassword');

    if (confirmPassword && password !== confirmPassword) {
      confirmInput.style.borderColor = '#f44336';
      confirmInput.style.boxShadow = '0 0 0 3px rgba(244, 67, 54, 0.1)';
    } else if (confirmPassword) {
      confirmInput.style.borderColor = '#4caf50';
      confirmInput.style.boxShadow = '0 0 0 3px rgba(76, 175, 80, 0.1)';
    } else {
      confirmInput.style.borderColor = '';
      confirmInput.style.boxShadow = '';
    }
  }

  // التحقق من اسم المستخدم
  validateUsername() {
    const username = document.getElementById('registerUsername').value.trim();
    const input = document.getElementById('registerUsername');

    if (username.length >= 3) {
      // فحص توفر اسم المستخدم
      const users = userManager.loadUsers();
      if (users[username]) {
        input.style.borderColor = '#f44336';
        input.style.boxShadow = '0 0 0 3px rgba(244, 67, 54, 0.1)';
        this.showNotification('اسم المستخدم غير متاح', 'error');
      } else {
        input.style.borderColor = '#4caf50';
        input.style.boxShadow = '0 0 0 3px rgba(76, 175, 80, 0.1)';
      }
    }
  }

  // التحقق من صحة البريد الإلكتروني
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // معالجة رفع الصورة الرمزية
  handleAvatarUpload(file) {
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      this.showNotification('يرجى اختيار ملف صورة صالح', 'error');
      return;
    }

    if (file.size > 2 * 1024 * 1024) { // 2MB
      this.showNotification('حجم الصورة يجب أن يكون أقل من 2 ميجابايت', 'error');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      this.selectedAvatar = {
        type: 'image',
        data: e.target.result
      };
      this.updateAvatarPreview(e.target.result);
    };
    reader.readAsDataURL(file);
  }

  // إنشاء صورة رمزية عشوائية
  generateRandomAvatar() {
    const fullName = document.getElementById('registerFullName').value.trim();
    if (!fullName) {
      this.showNotification('يرجى إدخال الاسم الكامل أولاً', 'error');
      return;
    }

    this.selectedAvatar = userManager.generateAvatar(fullName);
    this.updateAvatarPreview();
  }

  // تحديث معاينة الصورة الرمزية
  updateAvatarPreview(imageData = null) {
    const preview = document.getElementById('avatarPreview');
    
    if (imageData) {
      preview.innerHTML = `<img src="${imageData}" alt="Avatar">`;
    } else if (this.selectedAvatar && this.selectedAvatar.type === 'initials') {
      preview.style.backgroundColor = this.selectedAvatar.backgroundColor;
      preview.innerHTML = this.selectedAvatar.initials;
    }
  }

  // عرض الإشعارات
  showNotification(message, type = 'info') {
    const notification = document.getElementById('authNotification');
    const icon = notification.querySelector('.notification-icon');
    const messageEl = notification.querySelector('.notification-message');

    // تحديد الأيقونة حسب النوع
    if (type === 'success') {
      icon.className = 'notification-icon fas fa-check-circle';
    } else if (type === 'error') {
      icon.className = 'notification-icon fas fa-exclamation-circle';
    } else {
      icon.className = 'notification-icon fas fa-info-circle';
    }

    messageEl.textContent = message;
    notification.className = `auth-notification ${type}`;

    // إخفاء الإشعار بعد 4 ثوان
    setTimeout(() => {
      notification.classList.add('hidden');
    }, 4000);
  }

  // عرض/إخفاء مؤشر التحميل
  showLoader(show) {
    const loader = document.getElementById('authLoader');
    if (show) {
      loader.classList.remove('hidden');
    } else {
      loader.classList.add('hidden');
    }
  }
}

// تهيئة واجهة التسجيل عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  new AuthInterface();
});
