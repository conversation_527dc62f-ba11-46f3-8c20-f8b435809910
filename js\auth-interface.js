// TDL V1.0 - Authentication Interface
// واجهة تسجيل الدخول والتسجيل

import userManager from './simple-user-manager.js';

console.log('🔄 تحميل auth-interface.js...');
console.log('📦 userManager:', userManager);

// التحقق من تحميل userManager
if (!userManager) {
  console.error('❌ فشل في تحميل userManager');
  alert('خطأ في تحميل النظام، يرجى إعادة تحميل الصفحة');
}

class AuthInterface {
  constructor() {
    this.currentForm = 'login';
    this.selectedAvatar = null;

    // التحقق من وجود العناصر الأساسية
    this.checkRequiredElements();
    this.init();
  }

  // التحقق من وجود العناصر المطلوبة
  checkRequiredElements() {
    const requiredElements = [
      'loginFormElement',
      'registerFormElement',
      'showRegister',
      'showLogin',
      'authNotification',
      'authLoader'
    ];

    const missingElements = requiredElements.filter(id => !document.getElementById(id));

    if (missingElements.length > 0) {
      console.error('❌ عناصر مفقودة في DOM:', missingElements);
      alert('خطأ في تحميل الصفحة، يرجى إعادة تحميل الصفحة');
      return false;
    }

    console.log('✅ جميع العناصر المطلوبة موجودة');
    return true;
  }

  // تهيئة الواجهة
  init() {
    this.bindEvents();
    this.setupPasswordToggles();
    this.setupAvatarSelection();
    this.setupMouseEffects();
    this.setupRippleEffects();
    this.checkExistingSession();
  }

  // ربط الأحداث
  bindEvents() {
    // تبديل النماذج
    document.getElementById('showRegister')?.addEventListener('click', (e) => {
      e.preventDefault();
      this.showForm('register');
    });

    document.getElementById('showLogin')?.addEventListener('click', (e) => {
      e.preventDefault();
      this.showForm('login');
    });

    // إرسال النماذج
    document.getElementById('loginFormElement')?.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleLogin();
    });

    document.getElementById('registerFormElement')?.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleRegister();
    });

    // التحقق من كلمة المرور في الوقت الفعلي
    document.getElementById('confirmPassword')?.addEventListener('input', () => {
      this.validatePasswordMatch();
    });

    // التحقق من اسم المستخدم
    document.getElementById('registerUsername')?.addEventListener('blur', () => {
      this.validateUsername();
    });
  }

  // إعداد أزرار إظهار/إخفاء كلمة المرور
  setupPasswordToggles() {
    document.querySelectorAll('.toggle-password').forEach(btn => {
      btn.addEventListener('click', () => {
        const targetId = btn.dataset.target;
        const input = document.getElementById(targetId);
        const icon = btn.querySelector('i');

        if (input.type === 'password') {
          input.type = 'text';
          icon.className = 'fas fa-eye-slash';
        } else {
          input.type = 'password';
          icon.className = 'fas fa-eye';
        }
      });
    });
  }

  // إعداد اختيار الصورة الرمزية
  setupAvatarSelection() {
    const uploadBtn = document.querySelector('[data-type="upload"]');
    const generateBtn = document.querySelector('[data-type="generate"]');
    const fileInput = document.getElementById('avatarUpload');

    uploadBtn?.addEventListener('click', () => {
      fileInput.click();
    });

    generateBtn?.addEventListener('click', () => {
      this.generateRandomAvatar();
    });

    fileInput?.addEventListener('change', (e) => {
      this.handleAvatarUpload(e.target.files[0]);
    });
  }

  // فحص الجلسة الموجودة
  checkExistingSession() {
    if (userManager.checkSession()) {
      window.location.href = 'index.html';
    }
  }

  // عرض النموذج المحدد
  showForm(formType) {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');

    if (formType === 'login') {
      loginForm.classList.remove('hidden');
      registerForm.classList.add('hidden');
      this.currentForm = 'login';
    } else {
      loginForm.classList.add('hidden');
      registerForm.classList.remove('hidden');
      this.currentForm = 'register';
    }

    // تأثير انتقال سلس
    setTimeout(() => {
      const activeForm = document.querySelector('.auth-form-container:not(.hidden)');
      if (activeForm) {
        activeForm.style.transform = 'scale(0.95)';
        setTimeout(() => {
          activeForm.style.transform = 'scale(1)';
        }, 100);
      }
    }, 50);
  }

  // معالجة تسجيل الدخول
  async handleLogin() {
    const form = document.getElementById('loginFormElement');
    const formData = new FormData(form);
    const username = formData.get('username').trim();
    const password = formData.get('password');

    if (!this.validateLoginForm(username, password)) {
      return;
    }

    this.showLoader(true);

    try {
      const result = await userManager.login(username, password);
      
      if (result.success) {
        this.showNotification('تم تسجيل الدخول بنجاح!', 'success');
        
        // انتظار قصير لإظهار الرسالة ثم التوجه للتطبيق
        setTimeout(() => {
          window.location.href = 'index.html';
        }, 1500);
      }
    } catch (error) {
      this.showNotification(error.message, 'error');
    } finally {
      this.showLoader(false);
    }
  }

  // معالجة التسجيل
  async handleRegister() {
    console.log('🚀 بدء عملية إنشاء الحساب...');

    const form = document.getElementById('registerFormElement');
    if (!form) {
      console.error('❌ لم يتم العثور على نموذج التسجيل');
      this.showNotification('خطأ في النموذج', 'error');
      return;
    }

    const formData = new FormData(form);

    const userData = {
      fullName: formData.get('fullName')?.trim() || '',
      username: formData.get('username')?.trim() || '',
      email: formData.get('email')?.trim() || '',
      password: formData.get('password') || '',
      avatar: this.selectedAvatar
    };

    const confirmPassword = formData.get('confirmPassword') || '';

    console.log('📝 بيانات المستخدم:', {
      fullName: userData.fullName,
      username: userData.username,
      email: userData.email,
      passwordLength: userData.password.length,
      confirmPasswordLength: confirmPassword.length,
      hasAvatar: !!userData.avatar
    });

    if (!this.validateRegisterForm(userData, confirmPassword)) {
      console.log('❌ فشل في التحقق من صحة البيانات');
      return;
    }

    console.log('✅ تم التحقق من صحة البيانات، بدء الحفظ...');
    this.showLoader(true);

    try {
      console.log('📤 إرسال البيانات إلى userManager...');
      const result = await userManager.register(userData);
      console.log('📥 استجابة userManager:', result);

      if (result.success) {
        console.log('🎉 تم إنشاء الحساب بنجاح!');
        this.showNotification('تم إنشاء الحساب بنجاح! 🎉', 'success');

        // إظهار رسالة تأكيد مع خيارات
        this.showAccountCreatedDialog(userData);
      } else {
        console.log('⚠️ فشل في إنشاء الحساب:', result);
        this.showNotification('فشل في إنشاء الحساب', 'error');
      }
    } catch (error) {
      console.error('💥 خطأ في إنشاء الحساب:', error);
      this.showNotification(error.message || 'حدث خطأ غير متوقع', 'error');
    } finally {
      this.showLoader(false);
      console.log('🏁 انتهت عملية إنشاء الحساب');
    }
  }

  // التحقق من صحة نموذج تسجيل الدخول
  validateLoginForm(username, password) {
    if (!username) {
      this.showNotification('يرجى إدخال اسم المستخدم', 'error');
      return false;
    }

    if (!password) {
      this.showNotification('يرجى إدخال كلمة المرور', 'error');
      return false;
    }

    return true;
  }

  // التحقق من صحة نموذج التسجيل
  validateRegisterForm(userData, confirmPassword) {
    console.log('🔍 بدء التحقق من صحة البيانات...');

    const { fullName, username, email, password } = userData;

    // التحقق من الاسم الكامل
    if (!fullName || fullName.trim().length < 2) {
      console.log('❌ الاسم الكامل غير صالح:', fullName);
      this.showNotification('يرجى إدخال الاسم الكامل (حرفين على الأقل)', 'error');
      return false;
    }

    // التحقق من اسم المستخدم
    if (!username || username.trim().length < 3) {
      console.log('❌ اسم المستخدم غير صالح:', username);
      this.showNotification('اسم المستخدم يجب أن يكون 3 أحرف على الأقل', 'error');
      return false;
    }

    // التحقق من البريد الإلكتروني
    if (!email || !this.isValidEmail(email)) {
      console.log('❌ البريد الإلكتروني غير صالح:', email);
      this.showNotification('يرجى إدخال بريد إلكتروني صالح', 'error');
      return false;
    }

    // التحقق من كلمة المرور
    if (!password || password.length < 6) {
      console.log('❌ كلمة المرور غير صالحة، الطول:', password?.length || 0);
      this.showNotification('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
      return false;
    }

    // التحقق من تطابق كلمة المرور
    if (password !== confirmPassword) {
      console.log('❌ كلمات المرور غير متطابقة');
      this.showNotification('كلمات المرور غير متطابقة', 'error');
      return false;
    }

    // التحقق من الموافقة على الشروط
    const agreeTermsElement = document.getElementById('agreeTerms');
    if (!agreeTermsElement) {
      console.error('❌ عنصر الموافقة على الشروط غير موجود');
      this.showNotification('خطأ في النموذج', 'error');
      return false;
    }

    if (!agreeTermsElement.checked) {
      console.log('❌ لم يتم الموافقة على الشروط');
      this.showNotification('يجب الموافقة على الشروط والأحكام', 'error');
      return false;
    }

    console.log('✅ جميع البيانات صحيحة');
    return true;
  }

  // التحقق من تطابق كلمة المرور
  validatePasswordMatch() {
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const confirmInput = document.getElementById('confirmPassword');

    if (confirmPassword && password !== confirmPassword) {
      confirmInput.style.borderColor = '#f44336';
      confirmInput.style.boxShadow = '0 0 0 3px rgba(244, 67, 54, 0.1)';
    } else if (confirmPassword) {
      confirmInput.style.borderColor = '#4caf50';
      confirmInput.style.boxShadow = '0 0 0 3px rgba(76, 175, 80, 0.1)';
    } else {
      confirmInput.style.borderColor = '';
      confirmInput.style.boxShadow = '';
    }
  }

  // التحقق من اسم المستخدم
  validateUsername() {
    const username = document.getElementById('registerUsername').value.trim();
    const input = document.getElementById('registerUsername');

    if (username.length >= 3) {
      // فحص توفر اسم المستخدم
      const users = userManager.loadUsers();
      if (users[username]) {
        input.style.borderColor = '#f44336';
        input.style.boxShadow = '0 0 0 3px rgba(244, 67, 54, 0.1)';
        this.showNotification('اسم المستخدم غير متاح', 'error');
      } else {
        input.style.borderColor = '#4caf50';
        input.style.boxShadow = '0 0 0 3px rgba(76, 175, 80, 0.1)';
      }
    }
  }

  // التحقق من صحة البريد الإلكتروني
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // معالجة رفع الصورة الرمزية
  handleAvatarUpload(file) {
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      this.showNotification('يرجى اختيار ملف صورة صالح', 'error');
      return;
    }

    if (file.size > 2 * 1024 * 1024) { // 2MB
      this.showNotification('حجم الصورة يجب أن يكون أقل من 2 ميجابايت', 'error');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      this.selectedAvatar = {
        type: 'image',
        data: e.target.result
      };
      this.updateAvatarPreview(e.target.result);
    };
    reader.readAsDataURL(file);
  }

  // إنشاء صورة رمزية عشوائية
  generateRandomAvatar() {
    const fullName = document.getElementById('registerFullName').value.trim();
    if (!fullName) {
      this.showNotification('يرجى إدخال الاسم الكامل أولاً', 'error');
      return;
    }

    this.selectedAvatar = userManager.generateAvatar(fullName);
    this.updateAvatarPreview();
  }

  // تحديث معاينة الصورة الرمزية
  updateAvatarPreview(imageData = null) {
    const preview = document.getElementById('avatarPreview');
    
    if (imageData) {
      preview.innerHTML = `<img src="${imageData}" alt="Avatar">`;
    } else if (this.selectedAvatar && this.selectedAvatar.type === 'initials') {
      preview.style.backgroundColor = this.selectedAvatar.backgroundColor;
      preview.innerHTML = this.selectedAvatar.initials;
    }
  }

  // عرض الإشعارات
  showNotification(message, type = 'info') {
    const notification = document.getElementById('authNotification');
    const icon = notification.querySelector('.notification-icon');
    const messageEl = notification.querySelector('.notification-message');

    // تحديد الأيقونة حسب النوع
    if (type === 'success') {
      icon.className = 'notification-icon fas fa-check-circle';
    } else if (type === 'error') {
      icon.className = 'notification-icon fas fa-exclamation-circle';
    } else {
      icon.className = 'notification-icon fas fa-info-circle';
    }

    messageEl.textContent = message;
    notification.className = `auth-notification ${type}`;

    // إخفاء الإشعار بعد 4 ثوان
    setTimeout(() => {
      notification.classList.add('hidden');
    }, 4000);
  }

  // عرض/إخفاء مؤشر التحميل
  showLoader(show) {
    const loader = document.getElementById('authLoader');
    if (show) {
      loader.classList.remove('hidden');
    } else {
      loader.classList.add('hidden');
    }
  }

  // إظهار حوار تأكيد إنشاء الحساب
  showAccountCreatedDialog(userData) {
    const dialog = document.createElement('div');
    dialog.className = 'account-created-dialog';
    dialog.innerHTML = `
      <div class="dialog-overlay"></div>
      <div class="dialog-content">
        <div class="dialog-header">
          <div class="success-icon">
            <i class="fas fa-check-circle"></i>
          </div>
          <h3>تم إنشاء حسابك بنجاح! 🎉</h3>
          <p>مرحباً بك في TDL V1.0، ${userData.fullName}</p>
        </div>

        <div class="dialog-body">
          <div class="account-summary">
            <div class="summary-item">
              <i class="fas fa-user"></i>
              <span>اسم المستخدم: ${userData.username}</span>
            </div>
            <div class="summary-item">
              <i class="fas fa-envelope"></i>
              <span>البريد الإلكتروني: ${userData.email}</span>
            </div>
          </div>
        </div>

        <div class="dialog-actions">
          <button class="dialog-btn primary" id="continueToApp">
            <i class="fas fa-arrow-right"></i>
            الانتقال للتطبيق
          </button>
          <button class="dialog-btn secondary" id="stayOnPage">
            <i class="fas fa-edit"></i>
            البقاء هنا
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(dialog);

    // تأثير ظهور
    setTimeout(() => {
      dialog.classList.add('show');
    }, 100);

    // ربط الأحداث
    const continueBtn = dialog.querySelector('#continueToApp');
    const stayBtn = dialog.querySelector('#stayOnPage');

    continueBtn.addEventListener('click', async () => {
      this.showLoader(true);
      try {
        await userManager.login(userData.username, userData.password);
        this.showNotification('جاري تسجيل الدخول...', 'info');
        setTimeout(() => {
          window.location.href = 'index.html';
        }, 1000);
      } catch (error) {
        this.showLoader(false);
        this.removeDialog(dialog);
        this.showForm('login');
        this.showNotification('تم إنشاء الحساب، يرجى تسجيل الدخول يدوياً', 'info');
      }
    });

    stayBtn.addEventListener('click', () => {
      this.removeDialog(dialog);
      this.showNotification('يمكنك تسجيل الدخول في أي وقت', 'info');
    });

    // إغلاق عند النقر خارج الحوار
    dialog.querySelector('.dialog-overlay').addEventListener('click', () => {
      this.removeDialog(dialog);
    });
  }

  // إزالة الحوار
  removeDialog(dialog) {
    dialog.classList.add('hide');
    setTimeout(() => {
      if (dialog.parentNode) {
        dialog.parentNode.removeChild(dialog);
      }
    }, 300);
  }

  // إعداد تأثيرات الماوس
  setupMouseEffects() {
    const authContainer = document.querySelector('.auth-form-container');
    const trails = [];

    // إنشاء مسارات الماوس
    for (let i = 0; i < 10; i++) {
      const trail = document.createElement('div');
      trail.className = 'mouse-trail';
      document.body.appendChild(trail);
      trails.push({
        element: trail,
        x: 0,
        y: 0,
        delay: i * 50
      });
    }

    // تتبع حركة الماوس
    let mouseX = 0, mouseY = 0;

    document.addEventListener('mousemove', (e) => {
      mouseX = e.clientX;
      mouseY = e.clientY;

      // تحديث متغيرات CSS للتأثير الضوئي
      if (authContainer) {
        const rect = authContainer.getBoundingClientRect();
        const x = ((e.clientX - rect.left) / rect.width) * 100;
        const y = ((e.clientY - rect.top) / rect.height) * 100;
        authContainer.style.setProperty('--mouse-x', x + '%');
        authContainer.style.setProperty('--mouse-y', y + '%');
      }

      // تحديث مسارات الماوس
      trails.forEach((trail, index) => {
        setTimeout(() => {
          trail.element.style.left = mouseX + 'px';
          trail.element.style.top = mouseY + 'px';
          trail.element.style.opacity = (10 - index) / 10;
          trail.element.style.transform = `scale(${(10 - index) / 10})`;
        }, trail.delay);
      });
    });

    // إخفاء المسارات عند مغادرة النافذة
    document.addEventListener('mouseleave', () => {
      trails.forEach(trail => {
        trail.element.style.opacity = '0';
      });
    });
  }

  // إعداد تأثيرات الموجة
  setupRippleEffects() {
    const buttons = document.querySelectorAll('.auth-btn');

    buttons.forEach(button => {
      button.classList.add('ripple-effect');

      button.addEventListener('click', (e) => {
        // إزالة التأثير السابق
        const existingRipple = button.querySelector('.ripple');
        if (existingRipple) {
          existingRipple.remove();
        }

        // إنشاء تأثير موجة جديد
        const ripple = document.createElement('span');
        ripple.className = 'ripple';

        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';

        button.appendChild(ripple);

        // إزالة التأثير بعد انتهاء الانيميشن
        setTimeout(() => {
          ripple.remove();
        }, 600);
      });
    });
  }

  // تأثير الكتابة المتحركة
  setupTypingEffect() {
    const titles = document.querySelectorAll('.auth-form h2');

    titles.forEach(title => {
      const text = title.textContent;
      title.textContent = '';

      let i = 0;
      const typeInterval = setInterval(() => {
        if (i < text.length) {
          title.textContent += text.charAt(i);
          i++;
        } else {
          clearInterval(typeInterval);
        }
      }, 100);
    });
  }

  // تأثير الظهور المتدرج للعناصر
  setupFadeInAnimation() {
    const elements = document.querySelectorAll('.form-group, .auth-btn, .auth-footer');

    elements.forEach((element, index) => {
      element.style.opacity = '0';
      element.style.transform = 'translateY(20px)';

      setTimeout(() => {
        element.style.transition = 'all 0.6s ease';
        element.style.opacity = '1';
        element.style.transform = 'translateY(0)';
      }, index * 100);
    });
  }
}

// تهيئة واجهة التسجيل عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  console.log('🚀 بدء تحميل واجهة التسجيل...');

  try {
    const authInterface = new AuthInterface();
    console.log('✅ تم تحميل واجهة التسجيل بنجاح');

    // تأثيرات إضافية عند التحميل
    setTimeout(() => {
      authInterface.setupFadeInAnimation();
    }, 500);

    // إضافة معالج للأخطاء العامة
    window.addEventListener('error', (event) => {
      console.error('💥 خطأ عام في الصفحة:', event.error);
    });

    window.addEventListener('unhandledrejection', (event) => {
      console.error('💥 خطأ في Promise غير معالج:', event.reason);
    });

  } catch (error) {
    console.error('❌ فشل في تحميل واجهة التسجيل:', error);
    alert('حدث خطأ في تحميل الصفحة، يرجى إعادة تحميل الصفحة');
  }
});
