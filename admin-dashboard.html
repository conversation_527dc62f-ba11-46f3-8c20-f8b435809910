<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المدير - TDL</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap');

        :root {
            --primary: #6366f1;
            --secondary: #8b5cf6;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --info: #06b6d4;
            --dark: #1a1a2e;
            --darker: #0f0f23;
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.1);
            --text-primary: #e0e0e0;
            --text-secondary: #a0a0a0;
            --text-muted: #6b7280;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--darker) 0%, var(--dark) 50%, #16213e 100%);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .admin-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .admin-header {
            text-align: center;
            margin-bottom: 3rem;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 2rem;
        }

        .admin-header h1 {
            font-size: 3rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary), var(--info));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .admin-header .subtitle {
            color: var(--text-secondary);
            font-size: 1.2rem;
        }

        .login-section {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .login-form {
            max-width: 400px;
            margin: 0 auto;
        }

        .form-group {
            margin-bottom: 1.5rem;
            text-align: right;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
            font-weight: 600;
        }

        .form-group input {
            width: 100%;
            padding: 1rem;
            border: 2px solid var(--glass-border);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-primary);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }

        .btn {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
        }

        .btn.danger {
            background: linear-gradient(135deg, var(--danger), #dc2626);
        }

        .btn.success {
            background: linear-gradient(135deg, var(--success), #059669);
        }

        .dashboard {
            display: none;
        }

        .dashboard.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .stat-card .icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-card .value {
            font-size: 2rem;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .stat-card .label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .section {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .section h2 {
            color: var(--primary);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid var(--glass-border);
        }

        .data-table th {
            background: rgba(99, 102, 241, 0.1);
            color: var(--primary);
            font-weight: 600;
        }

        .data-table tr:hover {
            background: rgba(255, 255, 255, 0.02);
        }

        .badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .badge.success {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success);
        }

        .badge.danger {
            background: rgba(239, 68, 68, 0.2);
            color: var(--danger);
        }

        .badge.warning {
            background: rgba(245, 158, 11, 0.2);
            color: var(--warning);
        }

        .code-block {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            overflow-x: auto;
            margin: 1rem 0;
        }

        .tabs {
            display: flex;
            margin-bottom: 1rem;
            border-bottom: 1px solid var(--glass-border);
        }

        .tab {
            padding: 1rem 1.5rem;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .tab.active {
            border-bottom-color: var(--primary);
            color: var(--primary);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .alert {
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            border-left: 4px solid;
        }

        .alert.success {
            background: rgba(16, 185, 129, 0.1);
            border-color: var(--success);
            color: var(--success);
        }

        .alert.error {
            background: rgba(239, 68, 68, 0.1);
            border-color: var(--danger);
            color: var(--danger);
        }

        .alert.info {
            background: rgba(6, 182, 212, 0.1);
            border-color: var(--info);
            color: var(--info);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin: 0.5rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary), var(--secondary));
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .admin-container {
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .admin-header h1 {
                font-size: 2rem;
            }

            .data-table {
                font-size: 0.8rem;
            }

            .tabs {
                flex-wrap: wrap;
            }

            .tab {
                flex: 1;
                min-width: 120px;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- زر العودة للصفحة الرئيسية -->
        <div style="position: absolute; top: 2rem; right: 2rem; z-index: 1000;">
            <button onclick="window.location.href='landing.html'"
                    style="background: var(--glass-bg); border: 1px solid var(--glass-border); color: var(--text-primary); padding: 0.8rem 1.2rem; border-radius: 10px; cursor: pointer; backdrop-filter: blur(10px); transition: all 0.3s ease; font-family: 'Cairo', sans-serif;"
                    onmouseover="this.style.background='rgba(255,255,255,0.1)'; this.style.transform='translateY(-2px)'"
                    onmouseout="this.style.background='var(--glass-bg)'; this.style.transform='translateY(0)'">
                <i class="fas fa-home"></i> الصفحة الرئيسية
            </button>
        </div>

        <!-- رأس الصفحة -->
        <div class="admin-header">
            <h1><i class="fas fa-shield-alt"></i> لوحة تحكم المدير</h1>
            <p class="subtitle">نظام إدارة TDL - مراقبة وتحليل شامل</p>
        </div>

        <!-- قسم تسجيل الدخول -->
        <div id="loginSection" class="login-section">
            <h2><i class="fas fa-lock"></i> تسجيل دخول المدير</h2>
            <div class="login-form">
                <div class="form-group">
                    <label for="adminUsername">اسم المستخدم:</label>
                    <input type="text" id="adminUsername" placeholder="أدخل اسم المستخدم">
                </div>
                <div class="form-group">
                    <label for="adminPassword">كلمة المرور:</label>
                    <input type="password" id="adminPassword" placeholder="أدخل كلمة المرور">
                </div>
                <button class="btn" onclick="adminLogin()">
                    <i class="fas fa-sign-in-alt"></i> دخول
                </button>
            </div>
            <div id="loginMessage" class="alert" style="display: none;"></div>
        </div>

        <!-- لوحة التحكم -->
        <div id="dashboard" class="dashboard">
            <!-- إحصائيات النظام -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="icon"><i class="fas fa-users"></i></div>
                    <div class="value" id="totalUsers">0</div>
                    <div class="label">إجمالي المستخدمين</div>
                </div>
                <div class="stat-card">
                    <div class="icon"><i class="fas fa-user-check"></i></div>
                    <div class="value" id="activeUsers">0</div>
                    <div class="label">المستخدمين النشطين</div>
                </div>
                <div class="stat-card">
                    <div class="icon"><i class="fas fa-tasks"></i></div>
                    <div class="value" id="totalTasks">0</div>
                    <div class="label">إجمالي المهام</div>
                </div>
                <div class="stat-card">
                    <div class="icon"><i class="fas fa-check-circle"></i></div>
                    <div class="value" id="completedTasks">0</div>
                    <div class="label">المهام المكتملة</div>
                </div>
                <div class="stat-card">
                    <div class="icon"><i class="fas fa-database"></i></div>
                    <div class="value" id="storageUsed">0</div>
                    <div class="label">استخدام التخزين (KB)</div>
                </div>
                <div class="stat-card">
                    <div class="icon"><i class="fas fa-clock"></i></div>
                    <div class="value" id="systemUptime">0</div>
                    <div class="label">وقت تشغيل النظام</div>
                </div>
            </div>

            <!-- التبويبات -->
            <div class="section">
                <div class="tabs">
                    <div class="tab active" onclick="showTab('users')">
                        <i class="fas fa-users"></i> المستخدمين
                    </div>
                    <div class="tab" onclick="showTab('system')">
                        <i class="fas fa-cogs"></i> النظام
                    </div>
                    <div class="tab" onclick="showTab('storage')">
                        <i class="fas fa-database"></i> التخزين
                    </div>
                    <div class="tab" onclick="showTab('logs')">
                        <i class="fas fa-file-alt"></i> السجلات
                    </div>
                    <div class="tab" onclick="showTab('security')">
                        <i class="fas fa-shield-alt"></i> الأمان
                    </div>
                </div>

                <!-- تبويب المستخدمين -->
                <div id="usersTab" class="tab-content active">
                    <h2><i class="fas fa-users"></i> إدارة المستخدمين المتقدمة</h2>
                    <div class="alert info">
                        <i class="fas fa-info-circle"></i>
                        عرض تفصيلي ومتقدم لجميع المستخدمين مع إحصائيات شاملة
                    </div>

                    <!-- فلاتر البحث -->
                    <div style="margin-bottom: 2rem; display: flex; gap: 1rem; flex-wrap: wrap;">
                        <input type="text" id="userSearch" placeholder="البحث في المستخدمين..."
                               style="flex: 1; min-width: 200px; padding: 0.8rem; border: 1px solid var(--glass-border); border-radius: 8px; background: rgba(255,255,255,0.05); color: var(--text-primary);">
                        <select id="userStatusFilter" style="padding: 0.8rem; border: 1px solid var(--glass-border); border-radius: 8px; background: rgba(255,255,255,0.05); color: var(--text-primary);">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                        <button class="btn primary" onclick="adminManager.refreshUserData()">
                            <i class="fas fa-sync"></i> تحديث
                        </button>
                    </div>

                    <table class="data-table" id="usersTable">
                        <thead>
                            <tr>
                                <th>الإجراءات</th>
                                <th>إحصائيات المهام</th>
                                <th>معدل النشاط</th>
                                <th>الحالة</th>
                                <th>آخر دخول</th>
                                <th>تاريخ التسجيل</th>
                                <th>البريد الإلكتروني</th>
                                <th>الاسم الكامل</th>
                                <th>اسم المستخدم</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </tbody>
                    </table>
                </div>

                <!-- تبويب النظام -->
                <div id="systemTab" class="tab-content">
                    <h2><i class="fas fa-cogs"></i> معلومات النظام</h2>
                    <div id="systemInfo"></div>
                </div>

                <!-- تبويب التخزين -->
                <div id="storageTab" class="tab-content">
                    <h2><i class="fas fa-database"></i> إدارة التخزين</h2>
                    <div id="storageInfo"></div>
                </div>

                <!-- تبويب السجلات -->
                <div id="logsTab" class="tab-content">
                    <h2><i class="fas fa-file-alt"></i> سجلات النظام</h2>
                    <div id="systemLogs"></div>
                </div>

                <!-- تبويب الأمان -->
                <div id="securityTab" class="tab-content">
                    <h2><i class="fas fa-shield-alt"></i> الأمان والحماية</h2>
                    <div id="securityInfo"></div>
                </div>
            </div>

            <!-- أدوات الإدارة -->
            <div class="section">
                <h2><i class="fas fa-tools"></i> أدوات الإدارة</h2>
                <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                    <button class="btn" onclick="refreshData()">
                        <i class="fas fa-sync"></i> تحديث البيانات
                    </button>
                    <button class="btn success" onclick="exportData()">
                        <i class="fas fa-download"></i> تصدير البيانات
                    </button>
                    <button class="btn danger" onclick="clearAllData()">
                        <i class="fas fa-trash"></i> مسح جميع البيانات
                    </button>
                    <button class="btn" onclick="generateReport()">
                        <i class="fas fa-chart-bar"></i> تقرير شامل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // نظام إدارة المستخدمين المدمج
        class AdminUserManager {
            constructor() {
                this.adminCredentials = {
                    username: 'kha',
                    password: 'kha/admin'
                };
                this.isLoggedIn = false;
                this.systemStartTime = Date.now();
                this.logs = [];
                this.init();
            }

            init() {
                this.loadSystemData();
                this.startSystemMonitoring();
                this.log('النظام', 'تم تشغيل لوحة تحكم المدير');
            }

            // تسجيل الأحداث
            log(category, message, type = 'info') {
                const logEntry = {
                    timestamp: new Date().toISOString(),
                    category,
                    message,
                    type,
                    id: Date.now() + Math.random()
                };
                this.logs.unshift(logEntry);

                // الاحتفاظ بآخر 1000 سجل فقط
                if (this.logs.length > 1000) {
                    this.logs = this.logs.slice(0, 1000);
                }

                console.log(`[${category}] ${message}`);
            }

            // تحميل بيانات النظام
            loadSystemData() {
                try {
                    this.users = this.loadUsers();
                    this.sessions = this.loadSessions();
                    this.log('البيانات', 'تم تحميل بيانات النظام بنجاح');
                } catch (error) {
                    this.log('البيانات', 'خطأ في تحميل بيانات النظام: ' + error.message, 'error');
                }
            }

            // تحميل المستخدمين
            loadUsers() {
                try {
                    const users = localStorage.getItem('tdl_users');
                    return users ? JSON.parse(users) : {};
                } catch (error) {
                    this.log('المستخدمين', 'خطأ في تحميل المستخدمين: ' + error.message, 'error');
                    return {};
                }
            }

            // تحميل الجلسات
            loadSessions() {
                try {
                    const session = localStorage.getItem('tdl_session');
                    const currentUser = localStorage.getItem('tdl_current_user');
                    return {
                        session: session ? JSON.parse(session) : null,
                        currentUser: currentUser ? JSON.parse(currentUser) : null
                    };
                } catch (error) {
                    this.log('الجلسات', 'خطأ في تحميل الجلسات: ' + error.message, 'error');
                    return { session: null, currentUser: null };
                }
            }

            // تسجيل دخول المدير
            adminLogin(username, password) {
                if (username === this.adminCredentials.username && password === this.adminCredentials.password) {
                    this.isLoggedIn = true;
                    this.log('الأمان', 'تم تسجيل دخول المدير بنجاح', 'success');

                    // فحص رسالة الترحيب
                    this.checkWelcomeMessage();

                    return true;
                } else {
                    this.log('الأمان', 'محاولة دخول فاشلة للمدير', 'warning');
                    return false;
                }
            }

            // فحص رسالة الترحيب
            checkWelcomeMessage() {
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.get('welcome') === 'true') {
                    setTimeout(() => {
                        const alert = document.createElement('div');
                        alert.className = 'alert success';
                        alert.innerHTML = '<i class="fas fa-crown"></i> مرحباً بك في لوحة تحكم المدير! 👑 تم تسجيل الدخول بنجاح';
                        alert.style.position = 'fixed';
                        alert.style.top = '20px';
                        alert.style.right = '20px';
                        alert.style.zIndex = '9999';
                        alert.style.minWidth = '350px';
                        document.body.appendChild(alert);

                        setTimeout(() => {
                            if (alert.parentNode) {
                                document.body.removeChild(alert);
                            }
                        }, 4000);
                    }, 1000);
                }
            }

            // إحصائيات النظام
            getSystemStats() {
                const users = this.users;
                const userCount = Object.keys(users).length;
                let totalTasks = 0;
                let completedTasks = 0;
                let activeUsers = 0;

                // حساب الإحصائيات
                Object.values(users).forEach(user => {
                    if (user.tasks) {
                        totalTasks += user.tasks.length;
                        completedTasks += user.tasks.filter(task => task.completed).length;
                    }

                    // المستخدم نشط إذا سجل دخول في آخر 7 أيام
                    if (user.lastLogin) {
                        const lastLogin = new Date(user.lastLogin);
                        const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
                        if (lastLogin > weekAgo) {
                            activeUsers++;
                        }
                    }
                });

                // حساب استخدام التخزين
                const storageUsed = this.calculateStorageUsage();

                // وقت تشغيل النظام
                const uptime = this.formatUptime(Date.now() - this.systemStartTime);

                return {
                    totalUsers: userCount,
                    activeUsers,
                    totalTasks,
                    completedTasks,
                    storageUsed,
                    uptime
                };
            }

            // حساب استخدام التخزين
            calculateStorageUsage() {
                let totalSize = 0;
                for (let key in localStorage) {
                    if (localStorage.hasOwnProperty(key)) {
                        totalSize += localStorage[key].length;
                    }
                }
                return Math.round(totalSize / 1024 * 100) / 100; // KB
            }

            // تنسيق وقت التشغيل
            formatUptime(milliseconds) {
                const seconds = Math.floor(milliseconds / 1000);
                const minutes = Math.floor(seconds / 60);
                const hours = Math.floor(minutes / 60);
                const days = Math.floor(hours / 24);

                if (days > 0) return `${days} يوم`;
                if (hours > 0) return `${hours} ساعة`;
                if (minutes > 0) return `${minutes} دقيقة`;
                return `${seconds} ثانية`;
            }

            // مراقبة النظام
            startSystemMonitoring() {
                setInterval(() => {
                    this.updateDashboard();
                }, 5000); // تحديث كل 5 ثوان
            }

            // تحديث لوحة التحكم
            updateDashboard() {
                if (this.isLoggedIn) {
                    const stats = this.getSystemStats();

                    document.getElementById('totalUsers').textContent = stats.totalUsers;
                    document.getElementById('activeUsers').textContent = stats.activeUsers;
                    document.getElementById('totalTasks').textContent = stats.totalTasks;
                    document.getElementById('completedTasks').textContent = stats.completedTasks;
                    document.getElementById('storageUsed').textContent = stats.storageUsed;
                    document.getElementById('systemUptime').textContent = stats.uptime;
                }
            }

            // تصدير البيانات
            exportData() {
                const data = {
                    users: this.users,
                    sessions: this.sessions,
                    logs: this.logs,
                    stats: this.getSystemStats(),
                    exportDate: new Date().toISOString(),
                    systemInfo: this.getSystemInfo()
                };

                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `tdl-system-export-${new Date().toISOString().split('T')[0]}.json`;
                a.click();
                URL.revokeObjectURL(url);

                this.log('التصدير', 'تم تصدير بيانات النظام بنجاح', 'success');
            }

            // معلومات النظام
            getSystemInfo() {
                return {
                    userAgent: navigator.userAgent,
                    language: navigator.language,
                    platform: navigator.platform,
                    cookieEnabled: navigator.cookieEnabled,
                    onLine: navigator.onLine,
                    screenResolution: `${screen.width}x${screen.height}`,
                    colorDepth: screen.colorDepth,
                    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                    localStorage: {
                        available: typeof(Storage) !== "undefined",
                        used: this.calculateStorageUsage(),
                        quota: this.getStorageQuota()
                    },
                    performance: {
                        memory: performance.memory ? {
                            used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                            total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                            limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
                        } : 'غير متاح'
                    }
                };
            }

            // حصة التخزين
            getStorageQuota() {
                if ('storage' in navigator && 'estimate' in navigator.storage) {
                    navigator.storage.estimate().then(estimate => {
                        return {
                            quota: Math.round(estimate.quota / 1024 / 1024),
                            usage: Math.round(estimate.usage / 1024 / 1024)
                        };
                    });
                }
                return 'غير متاح';
            }

            // مسح جميع البيانات
            clearAllData() {
                if (confirm('هل أنت متأكد من مسح جميع بيانات النظام؟\nهذا الإجراء لا يمكن التراجع عنه!')) {
                    localStorage.clear();
                    this.users = {};
                    this.sessions = { session: null, currentUser: null };
                    this.log('الإدارة', 'تم مسح جميع بيانات النظام', 'warning');
                    this.updateDashboard();
                    alert('تم مسح جميع البيانات بنجاح');
                }
            }

            // حذف مستخدم
            deleteUser(username) {
                if (confirm(`هل أنت متأكد من حذف المستخدم "${username}"؟`)) {
                    delete this.users[username];
                    localStorage.setItem('tdl_users', JSON.stringify(this.users));
                    this.log('المستخدمين', `تم حذف المستخدم: ${username}`, 'warning');
                    this.updateUsersTable();
                    this.updateDashboard();
                }
            }

            // تحديث جدول المستخدمين المتقدم
            updateUsersTable() {
                const tbody = document.getElementById('usersTableBody');
                const searchTerm = document.getElementById('userSearch')?.value.toLowerCase() || '';
                const statusFilter = document.getElementById('userStatusFilter')?.value || '';

                tbody.innerHTML = '';

                Object.values(this.users).forEach(user => {
                    // تطبيق الفلاتر
                    const matchesSearch = !searchTerm ||
                        user.fullName.toLowerCase().includes(searchTerm) ||
                        user.username.toLowerCase().includes(searchTerm) ||
                        user.email.toLowerCase().includes(searchTerm);

                    const isActive = user.lastLogin &&
                        new Date(user.lastLogin) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

                    const matchesStatus = !statusFilter ||
                        (statusFilter === 'active' && isActive) ||
                        (statusFilter === 'inactive' && !isActive);

                    if (!matchesSearch || !matchesStatus) return;

                    const row = document.createElement('tr');

                    // حساب إحصائيات المهام
                    const totalTasks = user.tasks ? user.tasks.length : 0;
                    const completedTasks = user.tasks ? user.tasks.filter(task => task.completed).length : 0;
                    const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

                    // حساب معدل النشاط
                    const daysSinceRegistration = Math.floor((Date.now() - new Date(user.createdAt)) / (1000 * 60 * 60 * 24));
                    const activityRate = daysSinceRegistration > 0 ? Math.round((totalTasks / daysSinceRegistration) * 10) / 10 : 0;

                    const statusBadge = isActive ?
                        '<span class="badge success">نشط</span>' :
                        '<span class="badge danger">غير نشط</span>';

                    const taskStats = `
                        <div style="font-size: 0.9rem;">
                            <div><strong>${totalTasks}</strong> إجمالي</div>
                            <div style="color: var(--success);"><strong>${completedTasks}</strong> مكتملة</div>
                            <div style="color: var(--info);">${completionRate}% معدل الإنجاز</div>
                        </div>
                    `;

                    const activityBadge = activityRate > 1 ?
                        `<span class="badge success">${activityRate} مهمة/يوم</span>` :
                        activityRate > 0.5 ?
                        `<span class="badge warning">${activityRate} مهمة/يوم</span>` :
                        `<span class="badge danger">${activityRate} مهمة/يوم</span>`;

                    row.innerHTML = `
                        <td>
                            <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                <button class="btn primary" onclick="adminManager.viewUserDetails('${user.username}')" style="padding: 0.3rem 0.6rem; font-size: 0.8rem;">
                                    <i class="fas fa-eye"></i> عرض
                                </button>
                                <button class="btn danger" onclick="adminManager.deleteUser('${user.username}')" style="padding: 0.3rem 0.6rem; font-size: 0.8rem;">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </div>
                        </td>
                        <td>${taskStats}</td>
                        <td>${activityBadge}</td>
                        <td>${statusBadge}</td>
                        <td>${user.lastLogin ? new Date(user.lastLogin).toLocaleString('ar') : '<span style="color: var(--text-muted);">لم يسجل دخول</span>'}</td>
                        <td>${new Date(user.createdAt).toLocaleString('ar')}</td>
                        <td><a href="mailto:${user.email}" style="color: var(--info);">${user.email}</a></td>
                        <td><strong>${user.fullName}</strong></td>
                        <td><code style="background: rgba(99,102,241,0.2); padding: 0.2rem 0.5rem; border-radius: 4px;">${user.username}</code></td>
                    `;

                    tbody.appendChild(row);
                });

                // إضافة مستمعي الأحداث للفلاتر
                this.setupTableFilters();
            }

            // إعداد فلاتر الجدول
            setupTableFilters() {
                const searchInput = document.getElementById('userSearch');
                const statusFilter = document.getElementById('userStatusFilter');

                if (searchInput) {
                    searchInput.addEventListener('input', () => {
                        this.updateUsersTable();
                    });
                }

                if (statusFilter) {
                    statusFilter.addEventListener('change', () => {
                        this.updateUsersTable();
                    });
                }
            }

            // عرض تفاصيل المستخدم
            viewUserDetails(username) {
                const user = this.users[username];
                if (!user) return;

                const totalTasks = user.tasks ? user.tasks.length : 0;
                const completedTasks = user.tasks ? user.tasks.filter(task => task.completed).length : 0;
                const pendingTasks = totalTasks - completedTasks;

                const details = `
=== تفاصيل المستخدم: ${user.fullName} ===

📋 المعلومات الأساسية:
• اسم المستخدم: ${user.username}
• الاسم الكامل: ${user.fullName}
• البريد الإلكتروني: ${user.email}
• تاريخ التسجيل: ${new Date(user.createdAt).toLocaleString('ar')}
• آخر دخول: ${user.lastLogin ? new Date(user.lastLogin).toLocaleString('ar') : 'لم يسجل دخول'}

📊 إحصائيات المهام:
• إجمالي المهام: ${totalTasks}
• المهام المكتملة: ${completedTasks}
• المهام المعلقة: ${pendingTasks}
• معدل الإنجاز: ${totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0}%

🎯 النشاط:
• عدد مرات تسجيل الدخول: ${user.stats?.loginCount || 0}
• متوسط المهام اليومية: ${this.calculateDailyAverage(user)}

⚙️ الإعدادات:
• الثيم: ${user.settings?.theme || 'افتراضي'}
• اللغة: ${user.settings?.language || 'العربية'}
• الإشعارات: ${user.settings?.notifications ? 'مفعلة' : 'معطلة'}
                `;

                alert(details);
            }

            // حساب متوسط المهام اليومية
            calculateDailyAverage(user) {
                const daysSinceRegistration = Math.floor((Date.now() - new Date(user.createdAt)) / (1000 * 60 * 60 * 24));
                const totalTasks = user.tasks ? user.tasks.length : 0;
                return daysSinceRegistration > 0 ? (totalTasks / daysSinceRegistration).toFixed(1) : '0';
            }

            // تحديث بيانات المستخدمين
            refreshUserData() {
                this.loadSystemData();
                this.updateUsersTable();
                this.updateDashboard();

                // إظهار رسالة تأكيد
                const alert = document.createElement('div');
                alert.className = 'alert success';
                alert.innerHTML = '<i class="fas fa-check-circle"></i> تم تحديث بيانات المستخدمين بنجاح';
                alert.style.position = 'fixed';
                alert.style.top = '20px';
                alert.style.right = '20px';
                alert.style.zIndex = '9999';
                document.body.appendChild(alert);

                setTimeout(() => {
                    if (alert.parentNode) {
                        document.body.removeChild(alert);
                    }
                }, 3000);
            }
        }

        // إنشاء مدير النظام
        const adminManager = new AdminUserManager();

        // تسجيل دخول المدير
        function adminLogin() {
            const username = document.getElementById('adminUsername').value;
            const password = document.getElementById('adminPassword').value;
            const messageDiv = document.getElementById('loginMessage');

            if (adminManager.adminLogin(username, password)) {
                document.getElementById('loginSection').style.display = 'none';
                document.getElementById('dashboard').classList.add('active');

                // تحديث البيانات
                adminManager.updateDashboard();
                adminManager.updateUsersTable();
                showSystemInfo();
                showStorageInfo();
                showSystemLogs();
                showSecurityInfo();

                messageDiv.style.display = 'none';
            } else {
                messageDiv.className = 'alert error';
                messageDiv.style.display = 'block';
                messageDiv.innerHTML = '<i class="fas fa-exclamation-triangle"></i> اسم المستخدم أو كلمة المرور غير صحيحة';
            }
        }

        // عرض التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // إزالة التفعيل من جميع الأزرار
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // تفعيل التبويب المحدد
            document.getElementById(tabName + 'Tab').classList.add('active');
            event.target.classList.add('active');
        }

        // عرض معلومات النظام
        function showSystemInfo() {
            const systemInfo = adminManager.getSystemInfo();
            const container = document.getElementById('systemInfo');

            container.innerHTML = `
                <div class="alert info">
                    <i class="fas fa-info-circle"></i>
                    معلومات تفصيلية عن بيئة تشغيل النظام والأداء
                </div>

                <h3><i class="fas fa-desktop"></i> معلومات المتصفح</h3>
                <div class="code-block">المتصفح: ${systemInfo.userAgent}
اللغة: ${systemInfo.language}
المنصة: ${systemInfo.platform}
الكوكيز مفعلة: ${systemInfo.cookieEnabled ? 'نعم' : 'لا'}
متصل بالإنترنت: ${systemInfo.onLine ? 'نعم' : 'لا'}</div>

                <h3><i class="fas fa-tv"></i> معلومات الشاشة</h3>
                <div class="code-block">دقة الشاشة: ${systemInfo.screenResolution}
عمق الألوان: ${systemInfo.colorDepth} بت
المنطقة الزمنية: ${systemInfo.timezone}</div>

                <h3><i class="fas fa-memory"></i> الذاكرة والأداء</h3>
                <div class="code-block">${typeof systemInfo.performance.memory === 'object' ?
                    `الذاكرة المستخدمة: ${systemInfo.performance.memory.used} MB
إجمالي الذاكرة: ${systemInfo.performance.memory.total} MB
حد الذاكرة: ${systemInfo.performance.memory.limit} MB` :
                    'معلومات الذاكرة غير متاحة'}</div>

                <h3><i class="fas fa-database"></i> التخزين المحلي</h3>
                <div class="code-block">متاح: ${systemInfo.localStorage.available ? 'نعم' : 'لا'}
المستخدم: ${systemInfo.localStorage.used} KB</div>
            `;
        }

        // عرض معلومات التخزين
        function showStorageInfo() {
            const container = document.getElementById('storageInfo');
            let storageDetails = '';

            // تفاصيل كل عنصر في التخزين
            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    const size = (localStorage[key].length / 1024).toFixed(2);
                    storageDetails += `${key}: ${size} KB\n`;
                }
            }

            const totalSize = adminManager.calculateStorageUsage();
            const usagePercentage = (totalSize / 5120 * 100).toFixed(1); // افتراض حد أقصى 5MB

            container.innerHTML = `
                <div class="alert info">
                    <i class="fas fa-info-circle"></i>
                    تفاصيل استخدام التخزين المحلي للنظام
                </div>

                <h3><i class="fas fa-chart-pie"></i> إحصائيات التخزين</h3>
                <div class="stat-card">
                    <div class="value">${totalSize} KB</div>
                    <div class="label">إجمالي المساحة المستخدمة</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${Math.min(usagePercentage, 100)}%"></div>
                    </div>
                    <small>${usagePercentage}% من المساحة المتاحة</small>
                </div>

                <h3><i class="fas fa-list"></i> تفاصيل التخزين</h3>
                <div class="code-block">${storageDetails || 'لا توجد بيانات مخزنة'}</div>

                <button class="btn danger" onclick="adminManager.clearAllData()">
                    <i class="fas fa-trash"></i> مسح جميع البيانات
                </button>
            `;
        }

        // عرض سجلات النظام
        function showSystemLogs() {
            const container = document.getElementById('systemLogs');
            const logs = adminManager.logs.slice(0, 50); // آخر 50 سجل

            let logsHtml = `
                <div class="alert info">
                    <i class="fas fa-info-circle"></i>
                    آخر ${logs.length} سجل من أنشطة النظام
                </div>
            `;

            logs.forEach(log => {
                const time = new Date(log.timestamp).toLocaleString('ar');
                const typeClass = log.type === 'error' ? 'danger' :
                                 log.type === 'warning' ? 'warning' :
                                 log.type === 'success' ? 'success' : 'info';

                logsHtml += `
                    <div class="alert ${typeClass}" style="margin: 0.5rem 0; padding: 0.8rem;">
                        <strong>[${log.category}]</strong> ${log.message}
                        <br><small>${time}</small>
                    </div>
                `;
            });

            container.innerHTML = logsHtml;
        }

        // عرض معلومات الأمان
        function showSecurityInfo() {
            const container = document.getElementById('securityInfo');
            const users = adminManager.users;
            const sessions = adminManager.sessions;

            // إحصائيات الأمان
            let weakPasswords = 0;
            let recentLogins = 0;
            const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

            Object.values(users).forEach(user => {
                // فحص كلمات المرور الضعيفة (أقل من 8 أحرف)
                if (user.password && user.password.length < 8) {
                    weakPasswords++;
                }

                // فحص تسجيلات الدخول الحديثة
                if (user.lastLogin && new Date(user.lastLogin) > weekAgo) {
                    recentLogins++;
                }
            });

            container.innerHTML = `
                <div class="alert info">
                    <i class="fas fa-shield-alt"></i>
                    تقييم أمان النظام والمستخدمين
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="icon"><i class="fas fa-key"></i></div>
                        <div class="value">${weakPasswords}</div>
                        <div class="label">كلمات مرور ضعيفة</div>
                    </div>
                    <div class="stat-card">
                        <div class="icon"><i class="fas fa-sign-in-alt"></i></div>
                        <div class="value">${recentLogins}</div>
                        <div class="label">تسجيلات دخول حديثة</div>
                    </div>
                    <div class="stat-card">
                        <div class="icon"><i class="fas fa-user-shield"></i></div>
                        <div class="value">${sessions.session ? 'نشطة' : 'غير نشطة'}</div>
                        <div class="label">الجلسة الحالية</div>
                    </div>
                </div>

                <h3><i class="fas fa-exclamation-triangle"></i> تحذيرات الأمان</h3>
                ${weakPasswords > 0 ?
                    `<div class="alert error">
                        <i class="fas fa-exclamation-triangle"></i>
                        يوجد ${weakPasswords} مستخدم بكلمات مرور ضعيفة
                    </div>` :
                    `<div class="alert success">
                        <i class="fas fa-check-circle"></i>
                        جميع كلمات المرور قوية
                    </div>`
                }

                <h3><i class="fas fa-user-clock"></i> الجلسة النشطة</h3>
                ${sessions.session ?
                    `<div class="code-block">المستخدم: ${sessions.currentUser?.fullName || 'غير محدد'}
اسم المستخدم: ${sessions.session.username}
وقت الدخول: ${new Date(sessions.session.loginTime).toLocaleString('ar')}
تنتهي في: ${new Date(sessions.session.expiresAt).toLocaleString('ar')}</div>` :
                    `<div class="alert info">لا توجد جلسة نشطة حالياً</div>`
                }
            `;
        }

        // تحديث البيانات
        function refreshData() {
            adminManager.loadSystemData();
            adminManager.updateDashboard();
            adminManager.updateUsersTable();
            showSystemInfo();
            showStorageInfo();
            showSystemLogs();
            showSecurityInfo();

            // إظهار رسالة تأكيد
            const alert = document.createElement('div');
            alert.className = 'alert success';
            alert.innerHTML = '<i class="fas fa-check-circle"></i> تم تحديث البيانات بنجاح';
            alert.style.position = 'fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            document.body.appendChild(alert);

            setTimeout(() => {
                document.body.removeChild(alert);
            }, 3000);
        }

        // تصدير البيانات
        function exportData() {
            adminManager.exportData();
        }

        // مسح جميع البيانات
        function clearAllData() {
            adminManager.clearAllData();
        }

        // تقرير شامل
        function generateReport() {
            const stats = adminManager.getSystemStats();
            const systemInfo = adminManager.getSystemInfo();
            const users = adminManager.users;

            const report = `
# تقرير نظام TDL الشامل
تاريخ التقرير: ${new Date().toLocaleString('ar')}

## إحصائيات عامة
- إجمالي المستخدمين: ${stats.totalUsers}
- المستخدمين النشطين: ${stats.activeUsers}
- إجمالي المهام: ${stats.totalTasks}
- المهام المكتملة: ${stats.completedTasks}
- استخدام التخزين: ${stats.storageUsed} KB
- وقت تشغيل النظام: ${stats.uptime}

## تفاصيل المستخدمين
${Object.values(users).map(user => `
- ${user.fullName} (@${user.username})
  - البريد: ${user.email}
  - تاريخ التسجيل: ${new Date(user.createdAt).toLocaleString('ar')}
  - آخر دخول: ${user.lastLogin ? new Date(user.lastLogin).toLocaleString('ar') : 'لم يسجل دخول'}
  - عدد المهام: ${user.tasks ? user.tasks.length : 0}
`).join('')}

## معلومات النظام
- المتصفح: ${systemInfo.userAgent}
- المنصة: ${systemInfo.platform}
- اللغة: ${systemInfo.language}
- دقة الشاشة: ${systemInfo.screenResolution}

## الأمان
- كلمات المرور الضعيفة: ${Object.values(users).filter(u => u.password && u.password.length < 8).length}
- الجلسات النشطة: ${adminManager.sessions.session ? 1 : 0}

---
تم إنشاء هذا التقرير بواسطة نظام TDL الإداري
            `;

            const blob = new Blob([report], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `tdl-report-${new Date().toISOString().split('T')[0]}.txt`;
            a.click();
            URL.revokeObjectURL(url);

            adminManager.log('التقارير', 'تم إنشاء تقرير شامل للنظام', 'success');
        }

        // معالج Enter للدخول
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !adminManager.isLoggedIn) {
                adminLogin();
            }
        });

        // تحديث دوري للبيانات
        setInterval(() => {
            if (adminManager.isLoggedIn) {
                refreshData();
            }
        }, 30000); // كل 30 ثانية
    </script>
</body>
</html>
