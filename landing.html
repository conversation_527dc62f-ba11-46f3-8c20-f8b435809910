<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TDL - مدير المهام المتقدم</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap');

        :root {
            --primary: #6366f1;
            --secondary: #8b5cf6;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --info: #06b6d4;
            --dark: #1a1a2e;
            --darker: #0f0f23;
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.1);
            --text-primary: #e0e0e0;
            --text-secondary: #a0a0a0;
            --text-muted: #6b7280;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--darker) 0%, var(--dark) 50%, #16213e 100%);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* الخلفية المتحركة */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .shape {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(45deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
            animation: float 20s infinite linear;
        }

        .shape:nth-child(1) { width: 80px; height: 80px; top: 20%; left: 10%; animation-delay: 0s; }
        .shape:nth-child(2) { width: 120px; height: 120px; top: 60%; right: 10%; animation-delay: -5s; }
        .shape:nth-child(3) { width: 60px; height: 60px; bottom: 20%; left: 20%; animation-delay: -10s; }
        .shape:nth-child(4) { width: 100px; height: 100px; top: 40%; right: 30%; animation-delay: -15s; }

        @keyframes float {
            0% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
            50% { transform: translateY(-100px) rotate(180deg); opacity: 0.8; }
            100% { transform: translateY(0px) rotate(360deg); opacity: 0.3; }
        }

        /* الحاوي الرئيسي */
        .landing-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* الهيدر */
        .header {
            text-align: center;
            margin-bottom: 4rem;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 3rem;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, transparent 50%, rgba(139, 92, 246, 0.1) 100%);
            z-index: -1;
        }

        .logo {
            font-size: 5rem;
            font-weight: 900;
            background: linear-gradient(135deg, var(--primary), var(--secondary), var(--info));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            animation: logoGlow 3s ease-in-out infinite;
        }

        @keyframes logoGlow {
            0%, 100% { filter: drop-shadow(0 0 20px rgba(99, 102, 241, 0.5)); }
            50% { filter: drop-shadow(0 0 30px rgba(139, 92, 246, 0.8)); }
        }

        .tagline {
            font-size: 1.5rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
            font-weight: 300;
        }

        .description {
            font-size: 1.1rem;
            color: var(--text-muted);
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.8;
        }

        /* الإحصائيات */
        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-bottom: 4rem;
        }

        .stat-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, transparent 50%);
            z-index: -1;
        }

        .stat-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border-color: var(--primary);
        }

        .stat-icon {
            font-size: 3rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            counter-reset: number;
            animation: countUp 2s ease-out;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 1rem;
            font-weight: 500;
        }

        /* الميزات */
        .features-section {
            margin-bottom: 4rem;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 3rem;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 2px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 15px;
            padding: 2rem;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            border-color: var(--primary);
        }

        .feature-icon {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 1rem;
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .feature-description {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        /* أزرار الدخول */
        .auth-section {
            text-align: center;
            margin-bottom: 4rem;
        }

        .auth-buttons {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
            margin-top: 2rem;
        }

        .auth-btn {
            padding: 1.2rem 2.5rem;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            text-decoration: none;
            min-width: 200px;
            justify-content: center;
        }

        .auth-btn.primary {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
        }

        .auth-btn.secondary {
            background: linear-gradient(135deg, var(--success), #059669);
            color: white;
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
        }

        .auth-btn.admin {
            background: linear-gradient(135deg, var(--warning), #d97706);
            color: white;
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
        }

        .auth-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(99, 102, 241, 0.4);
        }

        /* التقييم */
        .rating-section {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            margin-bottom: 4rem;
        }

        .rating-stars {
            font-size: 2rem;
            color: #fbbf24;
            margin-bottom: 1rem;
        }

        .rating-text {
            font-size: 1.2rem;
            color: var(--text-secondary);
            margin-bottom: 1rem;
        }

        .rating-count {
            color: var(--text-muted);
        }

        /* الفوتر */
        .footer {
            text-align: center;
            padding: 2rem;
            border-top: 1px solid var(--glass-border);
            color: var(--text-muted);
        }

        /* التصميم المتجاوب */
        @media (max-width: 768px) {
            .landing-container {
                padding: 1rem;
            }

            .header {
                padding: 2rem;
            }

            .logo {
                font-size: 3rem;
            }

            .tagline {
                font-size: 1.2rem;
            }

            .auth-buttons {
                flex-direction: column;
                align-items: center;
            }

            .auth-btn {
                width: 100%;
                max-width: 300px;
            }

            .stats-section {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 1rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- الخلفية المتحركة -->
    <div class="animated-bg">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
    </div>

    <div class="landing-container">
        <!-- الهيدر الرئيسي -->
        <header class="header">
            <h1 class="logo">TDL</h1>
            <p class="tagline">مدير المهام المتقدم</p>
            <p class="description">
                نظام إدارة المهام الأكثر تطوراً وفعالية. يساعدك على تنظيم مهامك، تتبع تقدمك، وزيادة إنتاجيتك بطريقة ذكية ومبتكرة.
            </p>
        </header>

        <!-- الإحصائيات -->
        <section class="stats-section">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number" id="totalUsers">0</div>
                <div class="stat-label">مستخدم نشط</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-tasks"></i>
                </div>
                <div class="stat-number" id="totalTasks">0</div>
                <div class="stat-label">مهمة مكتملة</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-number">99.9%</div>
                <div class="stat-label">معدل الأداء</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-number">24/7</div>
                <div class="stat-label">متاح دائماً</div>
            </div>
        </section>

        <!-- الميزات -->
        <section class="features-section">
            <h2 class="section-title">الميزات المتقدمة</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3 class="feature-title">بحث ذكي متقدم</h3>
                    <p class="feature-description">
                        ابحث في مهامك بسرعة البرق مع فلاتر متقدمة وتصنيفات ذكية لتجد ما تحتاجه فوراً.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h3 class="feature-title">تحليلات وإحصائيات</h3>
                    <p class="feature-description">
                        راقب تقدمك مع تقارير مفصلة ورسوم بيانية تفاعلية تساعدك على تحسين إنتاجيتك.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="feature-title">متجاوب ومتوافق</h3>
                    <p class="feature-description">
                        يعمل بسلاسة على جميع الأجهزة - الكمبيوتر، التابلت، والهاتف المحمول.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title">أمان وحماية</h3>
                    <p class="feature-description">
                        بياناتك محمية بأحدث تقنيات الأمان مع نظام مصادقة متقدم وتشفير شامل.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3 class="feature-title">تخصيص كامل</h3>
                    <p class="feature-description">
                        خصص واجهتك بالألوان والثيمات التي تناسبك مع إعدادات متقدمة للتحكم الكامل.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <h3 class="feature-title">تصدير واستيراد</h3>
                    <p class="feature-description">
                        صدّر بياناتك بصيغ متعددة واستورد من تطبيقات أخرى بسهولة تامة.
                    </p>
                </div>
            </div>
        </section>

        <!-- التقييم -->
        <section class="rating-section">
            <div class="rating-stars">
                <i class="fas fa-star"></i>
                <i class="fas fa-star"></i>
                <i class="fas fa-star"></i>
                <i class="fas fa-star"></i>
                <i class="fas fa-star"></i>
            </div>
            <p class="rating-text">تقييم ممتاز من المستخدمين</p>
            <p class="rating-count">بناءً على آراء أكثر من 1000 مستخدم</p>
        </section>

        <!-- أزرار الدخول -->
        <section class="auth-section">
            <h2 class="section-title">ابدأ رحلتك الآن</h2>
            <p style="color: var(--text-secondary); margin-bottom: 2rem; font-size: 1.1rem;">
                اختر نوع حسابك وابدأ في تنظيم مهامك بطريقة احترافية
            </p>
            <div class="auth-buttons">
                <a href="standalone-auth.html?type=user" class="auth-btn primary">
                    <i class="fas fa-user"></i>
                    دخول كمستخدم
                </a>
                <a href="standalone-auth.html?type=admin" class="auth-btn admin">
                    <i class="fas fa-user-shield"></i>
                    دخول كمدير
                </a>
                <a href="admin-dashboard.html" class="auth-btn secondary">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة تحكم المدير
                </a>
            </div>
        </section>

        <!-- الفوتر -->
        <footer class="footer">
            <p>&copy; 2024 TDL - مدير المهام المتقدم. جميع الحقوق محفوظة.</p>
            <p>تم التطوير بـ <i class="fas fa-heart" style="color: #ef4444;"></i> لتحسين إنتاجيتك</p>
        </footer>
    </div>

    <script>
        // نظام إحصائيات الصفحة الرئيسية
        class LandingPageManager {
            constructor() {
                this.init();
            }

            init() {
                this.loadStats();
                this.animateCounters();
                this.setupEventListeners();
            }

            // تحميل الإحصائيات من localStorage
            loadStats() {
                try {
                    const users = JSON.parse(localStorage.getItem('tdl_users') || '{}');
                    const userCount = Object.keys(users).length;

                    let totalTasks = 0;
                    Object.values(users).forEach(user => {
                        if (user.tasks) {
                            totalTasks += user.tasks.filter(task => task.completed).length;
                        }
                    });

                    // تحديث الإحصائيات
                    this.updateStat('totalUsers', userCount);
                    this.updateStat('totalTasks', totalTasks);
                } catch (error) {
                    console.error('خطأ في تحميل الإحصائيات:', error);
                }
            }

            // تحديث إحصائية معينة
            updateStat(elementId, value) {
                const element = document.getElementById(elementId);
                if (element) {
                    element.textContent = value;
                }
            }

            // تحريك العدادات
            animateCounters() {
                const counters = document.querySelectorAll('.stat-number');

                counters.forEach(counter => {
                    const target = parseInt(counter.textContent) || 0;
                    const increment = target / 50;
                    let current = 0;

                    const timer = setInterval(() => {
                        current += increment;
                        if (current >= target) {
                            counter.textContent = target;
                            clearInterval(timer);
                        } else {
                            counter.textContent = Math.floor(current);
                        }
                    }, 50);
                });
            }

            // إعداد مستمعي الأحداث
            setupEventListeners() {
                // تحديث الإحصائيات كل 30 ثانية
                setInterval(() => {
                    this.loadStats();
                }, 30000);

                // تأثيرات التمرير
                this.setupScrollEffects();
            }

            // تأثيرات التمرير
            setupScrollEffects() {
                const observerOptions = {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                };

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }
                    });
                }, observerOptions);

                // مراقبة العناصر
                document.querySelectorAll('.stat-card, .feature-card').forEach(el => {
                    el.style.opacity = '0';
                    el.style.transform = 'translateY(30px)';
                    el.style.transition = 'all 0.6s ease';
                    observer.observe(el);
                });
            }
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 تحميل الصفحة الرئيسية...');

            try {
                new LandingPageManager();
                console.log('✅ تم تحميل الصفحة الرئيسية بنجاح');
            } catch (error) {
                console.error('❌ خطأ في تحميل الصفحة الرئيسية:', error);
            }
        });

        // تحسين الأداء
        window.addEventListener('load', () => {
            // إخفاء شاشة التحميل إذا وجدت
            const loader = document.querySelector('.loader');
            if (loader) {
                loader.style.opacity = '0';
                setTimeout(() => loader.remove(), 300);
            }
        });
    </script>
</body>
</html>
