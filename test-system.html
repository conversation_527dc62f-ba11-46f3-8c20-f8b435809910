<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام - TDL</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #1a1a2e;
            color: #e0e0e0;
            padding: 2rem;
            line-height: 1.6;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            padding: 2rem;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        .test-btn {
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }
        .result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
        }
        .success { background: rgba(16, 185, 129, 0.2); border-left: 4px solid #10b981; }
        .error { background: rgba(239, 68, 68, 0.2); border-left: 4px solid #ef4444; }
        .info { background: rgba(59, 130, 246, 0.2); border-left: 4px solid #3b82f6; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار نظام TDL</h1>
        
        <div class="test-section">
            <h2>📋 اختبار التخزين المحلي</h2>
            <button class="test-btn" onclick="testLocalStorage()">اختبار التخزين المحلي</button>
            <div id="localStorage-result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>👤 اختبار إدارة المستخدمين</h2>
            <button class="test-btn" onclick="testUserManager()">اختبار UserManager</button>
            <button class="test-btn" onclick="testUserRegistration()">اختبار التسجيل</button>
            <button class="test-btn" onclick="testUserLogin()">اختبار تسجيل الدخول</button>
            <div id="user-result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>🔗 اختبار الروابط</h2>
            <button class="test-btn" onclick="testAuthPage()">اختبار صفحة التسجيل</button>
            <button class="test-btn" onclick="testMainApp()">اختبار التطبيق الرئيسي</button>
            <div id="links-result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>📊 معلومات النظام</h2>
            <button class="test-btn" onclick="showSystemInfo()">عرض معلومات النظام</button>
            <div id="system-result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>🧹 تنظيف البيانات</h2>
            <button class="test-btn" onclick="clearAllData()" style="background: linear-gradient(135deg, #ef4444, #dc2626);">مسح جميع البيانات</button>
            <div id="clear-result" class="result"></div>
        </div>
    </div>

    <script type="module">
        import userManager from './js/user-manager.js';
        
        window.userManager = userManager;
        
        // اختبار التخزين المحلي
        window.testLocalStorage = function() {
            const result = document.getElementById('localStorage-result');
            try {
                // اختبار الكتابة
                localStorage.setItem('test-key', 'test-value');
                const value = localStorage.getItem('test-key');
                localStorage.removeItem('test-key');
                
                if (value === 'test-value') {
                    result.className = 'result success';
                    result.innerHTML = '✅ التخزين المحلي يعمل بشكل صحيح';
                } else {
                    throw new Error('فشل في قراءة البيانات');
                }
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `❌ خطأ في التخزين المحلي: ${error.message}`;
            }
        };

        // اختبار UserManager
        window.testUserManager = function() {
            const result = document.getElementById('user-result');
            try {
                if (typeof userManager === 'undefined') {
                    throw new Error('userManager غير محمل');
                }
                
                if (typeof userManager.register !== 'function') {
                    throw new Error('دالة register غير موجودة');
                }
                
                if (typeof userManager.login !== 'function') {
                    throw new Error('دالة login غير موجودة');
                }
                
                result.className = 'result success';
                result.innerHTML = '✅ UserManager محمل ويعمل بشكل صحيح';
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `❌ خطأ في UserManager: ${error.message}`;
            }
        };

        // اختبار التسجيل
        window.testUserRegistration = async function() {
            const result = document.getElementById('user-result');
            try {
                const testUser = {
                    fullName: 'مستخدم تجريبي',
                    username: 'test_user_' + Date.now(),
                    email: '<EMAIL>',
                    password: 'password123'
                };
                
                const registerResult = await userManager.register(testUser);
                
                if (registerResult.success) {
                    result.className = 'result success';
                    result.innerHTML = `✅ تم إنشاء المستخدم التجريبي بنجاح: ${testUser.username}`;
                } else {
                    throw new Error('فشل في إنشاء المستخدم');
                }
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `❌ خطأ في التسجيل: ${error.message}`;
            }
        };

        // اختبار تسجيل الدخول
        window.testUserLogin = async function() {
            const result = document.getElementById('user-result');
            try {
                // إنشاء مستخدم تجريبي أولاً
                const testUser = {
                    fullName: 'مستخدم تجريبي للدخول',
                    username: 'login_test_' + Date.now(),
                    email: '<EMAIL>',
                    password: 'password123'
                };
                
                await userManager.register(testUser);
                
                // محاولة تسجيل الدخول
                const loginResult = await userManager.login(testUser.username, testUser.password);
                
                if (loginResult.success) {
                    result.className = 'result success';
                    result.innerHTML = `✅ تم تسجيل الدخول بنجاح للمستخدم: ${testUser.username}`;
                } else {
                    throw new Error('فشل في تسجيل الدخول');
                }
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `❌ خطأ في تسجيل الدخول: ${error.message}`;
            }
        };

        // اختبار صفحة التسجيل
        window.testAuthPage = function() {
            const result = document.getElementById('links-result');
            try {
                window.open('auth.html', '_blank');
                result.className = 'result info';
                result.innerHTML = '🔗 تم فتح صفحة التسجيل في تبويب جديد';
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `❌ خطأ في فتح صفحة التسجيل: ${error.message}`;
            }
        };

        // اختبار التطبيق الرئيسي
        window.testMainApp = function() {
            const result = document.getElementById('links-result');
            try {
                window.open('index.html', '_blank');
                result.className = 'result info';
                result.innerHTML = '🔗 تم فتح التطبيق الرئيسي في تبويب جديد';
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `❌ خطأ في فتح التطبيق الرئيسي: ${error.message}`;
            }
        };

        // عرض معلومات النظام
        window.showSystemInfo = function() {
            const result = document.getElementById('system-result');
            try {
                const users = userManager.loadUsers();
                const userCount = Object.keys(users).length;
                const currentUser = userManager.getCurrentUser();
                
                const info = `
                    📊 معلومات النظام:
                    • عدد المستخدمين المسجلين: ${userCount}
                    • المستخدم الحالي: ${currentUser ? currentUser.fullName : 'لا يوجد'}
                    • حالة الجلسة: ${userManager.checkSession() ? 'نشطة' : 'غير نشطة'}
                    • متصفح: ${navigator.userAgent.split(' ')[0]}
                    • اللغة: ${navigator.language}
                `;
                
                result.className = 'result info';
                result.innerHTML = info.replace(/\n/g, '<br>');
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `❌ خطأ في عرض معلومات النظام: ${error.message}`;
            }
        };

        // مسح جميع البيانات
        window.clearAllData = function() {
            const result = document.getElementById('clear-result');
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                try {
                    localStorage.clear();
                    result.className = 'result success';
                    result.innerHTML = '✅ تم مسح جميع البيانات بنجاح';
                } catch (error) {
                    result.className = 'result error';
                    result.innerHTML = `❌ خطأ في مسح البيانات: ${error.message}`;
                }
            }
        };
    </script>
</body>
</html>
