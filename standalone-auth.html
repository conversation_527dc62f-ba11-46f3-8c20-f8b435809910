<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TDL - تسجيل الدخول</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* استيراد الخط العربي */
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap');

        /* المتغيرات العامة */
        :root {
            --auth-bg: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.1);
            --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            --gradient-primary: linear-gradient(135deg, #6366f1, #8b5cf6, #06b6d4);
            --gradient-secondary: linear-gradient(135deg, #10b981, #059669);
            --auth-text: #e0e0e0;
            --auth-text-light: #a0a0a0;
            --auth-text-muted: #6b7280;
            --auth-primary: #6366f1;
            --auth-border: rgba(255, 255, 255, 0.2);
            --auth-radius: 20px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--auth-bg);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow-x: hidden;
            position: relative;
        }

        /* الخلفية المتحركة */
        .auth-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .shape {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(45deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
            animation: float 20s infinite linear;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: -5s;
        }

        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: -10s;
        }

        @keyframes float {
            0% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.3;
            }
            50% {
                transform: translateY(-100px) rotate(180deg);
                opacity: 0.8;
            }
            100% {
                transform: translateY(0px) rotate(360deg);
                opacity: 0.3;
            }
        }

        /* حاوي التطبيق */
        .auth-container {
            width: 100%;
            max-width: 520px;
            margin: 2rem;
            z-index: 1;
            position: relative;
        }

        /* رأس التطبيق */
        .auth-header {
            text-align: center;
            margin-bottom: 3rem;
            position: relative;
        }

        .app-logo {
            color: var(--auth-text);
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
            position: relative;
        }

        .app-logo i {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            display: block;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: logoFloat 3s ease-in-out infinite;
            filter: drop-shadow(0 4px 8px rgba(99, 102, 241, 0.3));
        }

        .app-logo h1 {
            font-size: 4rem;
            font-weight: 900;
            margin: 1rem 0;
            letter-spacing: 8px;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            animation: titleGlow 4s ease-in-out infinite;
            text-align: center;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .app-logo h1::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: var(--gradient-primary);
            border-radius: 2px;
            animation: underlineExpand 2s ease-in-out infinite;
        }

        .app-logo p {
            font-size: 1.2rem;
            opacity: 0.8;
            margin: 0;
            color: var(--auth-text-light);
            font-weight: 300;
            letter-spacing: 1px;
        }

        @keyframes logoFloat {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                filter: drop-shadow(0 4px 8px rgba(99, 102, 241, 0.3));
            }
            50% {
                transform: translateY(-10px) rotate(5deg);
                filter: drop-shadow(0 8px 16px rgba(99, 102, 241, 0.5));
            }
        }

        @keyframes titleGlow {
            0%, 100% {
                text-shadow: 0 0 20px rgba(99, 102, 241, 0.5);
            }
            50% {
                text-shadow: 0 0 30px rgba(139, 92, 246, 0.8), 0 0 40px rgba(6, 182, 212, 0.3);
            }
        }

        @keyframes underlineExpand {
            0%, 100% {
                width: 60px;
                opacity: 0.7;
            }
            50% {
                width: 120px;
                opacity: 1;
            }
        }

        /* حاوي النموذج */
        .auth-form-container {
            background: var(--glass-bg);
            border-radius: var(--auth-radius);
            box-shadow: var(--glass-shadow);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            background-image: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.05) 100%);
        }

        .auth-form-container:hover {
            transform: translateY(-5px);
            box-shadow: var(--glass-shadow), 0 0 40px rgba(99, 102, 241, 0.2);
            border-color: rgba(99, 102, 241, 0.3);
        }

        .auth-form {
            padding: 3rem;
            position: relative;
            z-index: 1;
        }

        .auth-form h2 {
            text-align: center;
            color: var(--auth-text);
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.8rem;
            position: relative;
        }

        .auth-form h2 i {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.2em;
            animation: iconPulse 2s ease-in-out infinite;
        }

        @keyframes iconPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* حقول الإدخال */
        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--auth-text-light);
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-group input {
            width: 100%;
            padding: 1rem 1.2rem;
            border: 2px solid var(--auth-border);
            border-radius: 12px;
            font-size: 1rem;
            font-family: inherit;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            color: var(--auth-text);
            box-sizing: border-box;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--auth-primary);
            background: rgba(255, 255, 255, 0.08);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2), 0 8px 25px rgba(99, 102, 241, 0.15);
            transform: translateY(-3px);
        }

        .form-group input::placeholder {
            color: var(--auth-text-muted);
        }

        /* الأزرار */
        .auth-btn {
            width: 100%;
            padding: 1.2rem;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            font-family: inherit;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.8rem;
            text-decoration: none;
            box-sizing: border-box;
            position: relative;
            overflow: hidden;
        }

        .auth-btn.primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3), 0 4px 10px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .auth-btn.primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(99, 102, 241, 0.4), 0 8px 15px rgba(0, 0, 0, 0.3);
            filter: brightness(1.1);
        }

        /* التبديل بين النماذج */
        .auth-footer {
            text-align: center;
            margin-top: 2rem;
            color: var(--auth-text-light);
        }

        .auth-footer a {
            color: var(--auth-primary);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .auth-footer a:hover {
            color: #8b5cf6;
            text-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
        }

        /* إخفاء النماذج */
        .auth-form-container.hidden {
            display: none;
        }

        /* الإشعارات */
        .auth-notification {
            position: fixed;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 15px;
            padding: 1rem 2rem;
            color: var(--auth-text);
            box-shadow: var(--glass-shadow);
            z-index: 1000;
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
        }

        .auth-notification.show {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(0);
        }

        .auth-notification.success {
            border-color: rgba(16, 185, 129, 0.3);
            background: rgba(16, 185, 129, 0.1);
        }

        .auth-notification.error {
            border-color: rgba(239, 68, 68, 0.3);
            background: rgba(239, 68, 68, 0.1);
        }

        /* مؤشر التحميل */
        .auth-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .auth-loader.show {
            opacity: 1;
            visibility: visible;
        }

        .loader-content {
            text-align: center;
            color: var(--auth-text);
        }

        .loader-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(99, 102, 241, 0.3);
            border-top: 3px solid #6366f1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* التصميم المتجاوب */
        @media (max-width: 768px) {
            .auth-container {
                margin: 1rem;
            }

            .auth-form {
                padding: 2rem;
            }

            .app-logo h1 {
                font-size: 2.5rem;
                letter-spacing: 4px;
            }

            .app-logo i {
                font-size: 3rem;
            }
        }

        @media (max-width: 480px) {
            .auth-form {
                padding: 1.5rem;
            }

            .app-logo h1 {
                font-size: 2rem;
                letter-spacing: 2px;
            }

            .app-logo i {
                font-size: 2.5rem;
            }
        }

        /* ========== حوار تأكيد إنشاء الحساب ========== */
        .account-created-dialog {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 3000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .account-created-dialog.show {
            opacity: 1;
            visibility: visible;
        }

        .account-created-dialog.hide {
            opacity: 0;
            visibility: hidden;
        }

        .dialog-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(8px);
        }

        .dialog-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.9);
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.5),
                0 0 40px rgba(99, 102, 241, 0.2);
            padding: 2.5rem;
            max-width: 500px;
            width: 90%;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .account-created-dialog.show .dialog-content {
            transform: translate(-50%, -50%) scale(1);
        }

        .dialog-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1.5rem;
            background: linear-gradient(135deg, var(--success), #059669);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: successPulse 2s ease-in-out infinite;
        }

        .success-icon i {
            font-size: 2.5rem;
            color: white;
        }

        @keyframes successPulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 0 30px rgba(16, 185, 129, 0.5);
            }
        }

        .dialog-header h3 {
            color: var(--text-primary);
            font-size: 1.8rem;
            font-weight: 700;
            margin: 0 0 0.5rem 0;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .dialog-header p {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin: 0;
        }

        .dialog-body {
            margin-bottom: 2rem;
        }

        .account-summary {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid var(--glass-border);
        }

        .summary-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
            color: var(--text-secondary);
        }

        .summary-item:last-child {
            margin-bottom: 0;
        }

        .summary-item i {
            width: 20px;
            color: var(--primary);
        }

        .dialog-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .dialog-btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            min-width: 150px;
            justify-content: center;
        }

        .dialog-btn.primary {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }

        .dialog-btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
        }

        .dialog-btn.secondary {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            border: 1px solid var(--glass-border);
        }

        .dialog-btn.secondary:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        /* تحسين responsive للحوار */
        @media (max-width: 768px) {
            .dialog-content {
                padding: 2rem;
                margin: 1rem;
                width: calc(100% - 2rem);
            }

            .dialog-actions {
                flex-direction: column;
            }

            .dialog-btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- الخلفية المتحركة -->
    <div class="auth-background">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
    </div>

    <!-- حاوي التطبيق الرئيسي -->
    <div class="auth-container">
        <!-- رأس التطبيق -->
        <div class="auth-header">
            <div class="app-logo">
                <i class="fas fa-tasks"></i>
                <h1>TDL</h1>
                <p>مدير المهام المتقدم</p>
            </div>
        </div>

        <!-- نموذج تسجيل الدخول -->
        <div id="loginFormContainer" class="auth-form-container">
            <form id="loginForm" class="auth-form">
                <h2><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</h2>

                <div class="form-group">
                    <label for="loginUsername">
                        <i class="fas fa-user"></i>
                        اسم المستخدم
                    </label>
                    <input type="text" id="loginUsername" name="username" placeholder="أدخل اسم المستخدم" required>
                </div>

                <div class="form-group">
                    <label for="loginPassword">
                        <i class="fas fa-lock"></i>
                        كلمة المرور
                    </label>
                    <input type="password" id="loginPassword" name="password" placeholder="أدخل كلمة المرور" required>
                </div>

                <button type="submit" class="auth-btn primary">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>

                <div class="auth-footer">
                    <p>ليس لديك حساب؟ <a href="#" id="showRegister">إنشاء حساب جديد</a></p>
                </div>
            </form>
        </div>

        <!-- نموذج إنشاء الحساب -->
        <div id="registerFormContainer" class="auth-form-container hidden">
            <form id="registerForm" class="auth-form">
                <h2><i class="fas fa-user-plus"></i> إنشاء حساب جديد</h2>

                <div class="form-group">
                    <label for="fullName">
                        <i class="fas fa-id-card"></i>
                        الاسم الكامل
                    </label>
                    <input type="text" id="fullName" name="fullName" placeholder="أدخل اسمك الكامل" required>
                </div>

                <div class="form-group">
                    <label for="username">
                        <i class="fas fa-user"></i>
                        اسم المستخدم
                    </label>
                    <input type="text" id="username" name="username" placeholder="اختر اسم مستخدم فريد" required>
                </div>

                <div class="form-group">
                    <label for="email">
                        <i class="fas fa-envelope"></i>
                        البريد الإلكتروني
                    </label>
                    <input type="email" id="email" name="email" placeholder="أدخل بريدك الإلكتروني" required>
                </div>

                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i>
                        كلمة المرور
                    </label>
                    <input type="password" id="password" name="password" placeholder="اختر كلمة مرور قوية" required>
                </div>

                <div class="form-group">
                    <label for="confirmPassword">
                        <i class="fas fa-lock"></i>
                        تأكيد كلمة المرور
                    </label>
                    <input type="password" id="confirmPassword" name="confirmPassword" placeholder="أعد إدخال كلمة المرور" required>
                </div>

                <button type="submit" class="auth-btn primary">
                    <i class="fas fa-user-plus"></i>
                    إنشاء الحساب
                </button>

                <div class="auth-footer">
                    <p>لديك حساب بالفعل؟ <a href="#" id="showLogin">تسجيل الدخول</a></p>
                </div>
            </form>
        </div>
    </div>

    <!-- رسائل التنبيه -->
    <div id="authNotification" class="auth-notification">
        <div class="notification-content">
            <i class="notification-icon"></i>
            <span class="notification-message"></span>
        </div>
    </div>

    <!-- مؤشر التحميل -->
    <div id="authLoader" class="auth-loader">
        <div class="loader-content">
            <div class="loader-spinner"></div>
            <p>جاري المعالجة...</p>
        </div>
    </div>

    <script>
        // نظام إدارة المستخدمين المدمج
        class StandaloneUserManager {
            constructor() {
                this.currentUser = null;
                this.sessionTimeout = 24 * 60 * 60 * 1000; // 24 ساعة
                console.log('✅ تم إنشاء StandaloneUserManager');
            }

            // تحميل المستخدمين
            loadUsers() {
                try {
                    const users = localStorage.getItem('tdl_users');
                    return users ? JSON.parse(users) : {};
                } catch (error) {
                    console.error('خطأ في تحميل المستخدمين:', error);
                    return {};
                }
            }

            // حفظ المستخدمين
            saveUsers(users) {
                try {
                    localStorage.setItem('tdl_users', JSON.stringify(users));
                    console.log('💾 تم حفظ المستخدمين بنجاح');
                    return true;
                } catch (error) {
                    console.error('خطأ في حفظ المستخدمين:', error);
                    if (error.name === 'QuotaExceededError') {
                        alert('مساحة التخزين ممتلئة، يرجى حذف بعض البيانات');
                    }
                    return false;
                }
            }

            // تشفير كلمة المرور
            hashPassword(password) {
                let hash = 0;
                for (let i = 0; i < password.length; i++) {
                    const char = password.charCodeAt(i);
                    hash = ((hash << 5) - hash) + char;
                    hash = hash & hash;
                }
                return hash.toString();
            }

            // التحقق من البريد الإلكتروني
            isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            // إنشاء صورة رمزية
            generateAvatar(fullName) {
                const initials = fullName.split(' ').map(name => name[0]).join('').toUpperCase();
                const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
                const color = colors[Math.floor(Math.random() * colors.length)];

                return {
                    type: 'initials',
                    initials,
                    backgroundColor: color,
                    textColor: '#FFFFFF'
                };
            }

            // إنشاء حساب
            async register(userData) {
                console.log('📝 بدء عملية التسجيل:', userData.username);

                const { username, email, password, fullName } = userData;

                // التحقق من البيانات
                if (!username || !email || !password || !fullName) {
                    throw new Error('جميع الحقول مطلوبة');
                }

                if (username.length < 3) {
                    throw new Error('اسم المستخدم يجب أن يكون 3 أحرف على الأقل');
                }

                if (password.length < 6) {
                    throw new Error('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                }

                if (!this.isValidEmail(email)) {
                    throw new Error('البريد الإلكتروني غير صالح');
                }

                const users = this.loadUsers();

                // التحقق من عدم وجود المستخدم
                if (users[username]) {
                    throw new Error('اسم المستخدم موجود بالفعل');
                }

                if (Object.values(users).some(user => user.email === email)) {
                    throw new Error('البريد الإلكتروني مستخدم بالفعل');
                }

                // إنشاء المستخدم
                const newUser = {
                    username,
                    email,
                    password: this.hashPassword(password),
                    fullName,
                    avatar: this.generateAvatar(fullName),
                    createdAt: new Date().toISOString(),
                    lastLogin: null,
                    settings: {
                        theme: 'dark',
                        language: 'ar',
                        notifications: true
                    },
                    tasks: [],
                    stats: {
                        totalTasks: 0,
                        completedTasks: 0,
                        loginCount: 0
                    }
                };

                users[username] = newUser;
                const saved = this.saveUsers(users);

                if (saved) {
                    console.log('✅ تم إنشاء المستخدم بنجاح:', username);
                    return { success: true, message: 'تم إنشاء الحساب بنجاح' };
                } else {
                    throw new Error('فشل في حفظ بيانات المستخدم');
                }
            }

            // تسجيل الدخول
            async login(username, password) {
                console.log('🔐 بدء عملية تسجيل الدخول:', username);

                if (!username || !password) {
                    throw new Error('اسم المستخدم وكلمة المرور مطلوبان');
                }

                const users = this.loadUsers();
                const user = users[username];

                if (!user) {
                    throw new Error('اسم المستخدم غير موجود');
                }

                if (user.password !== this.hashPassword(password)) {
                    throw new Error('كلمة المرور غير صحيحة');
                }

                // تحديث بيانات الدخول
                user.lastLogin = new Date().toISOString();
                user.stats.loginCount++;
                users[username] = user;
                this.saveUsers(users);

                // إنشاء جلسة
                this.currentUser = user;
                this.createSession(user);

                console.log('✅ تم تسجيل الدخول بنجاح:', username);
                return { success: true, user: this.getSafeUserData(user) };
            }

            // إنشاء جلسة
            createSession(user) {
                const session = {
                    username: user.username,
                    loginTime: Date.now(),
                    expiresAt: Date.now() + this.sessionTimeout
                };

                localStorage.setItem('tdl_session', JSON.stringify(session));
                localStorage.setItem('tdl_current_user', JSON.stringify(this.getSafeUserData(user)));
                console.log('🎫 تم إنشاء الجلسة:', user.username);
            }

            // فحص الجلسة
            checkSession() {
                try {
                    const sessionData = localStorage.getItem('tdl_session');
                    const currentUserData = localStorage.getItem('tdl_current_user');

                    if (!sessionData || !currentUserData) {
                        return false;
                    }

                    const session = JSON.parse(sessionData);
                    const currentUser = JSON.parse(currentUserData);

                    if (session && currentUser && Date.now() < session.expiresAt) {
                        const users = this.loadUsers();
                        const user = users[session.username];
                        if (user) {
                            this.currentUser = user;
                            console.log('✅ الجلسة صالحة:', user.username);
                            return true;
                        }
                    }
                } catch (error) {
                    console.error('خطأ في فحص الجلسة:', error);
                }

                this.logout();
                return false;
            }

            // تسجيل الخروج
            logout() {
                this.currentUser = null;
                localStorage.removeItem('tdl_session');
                localStorage.removeItem('tdl_current_user');
                console.log('👋 تم تسجيل الخروج');
            }

            // بيانات آمنة
            getSafeUserData(user) {
                const { password, ...safeData } = user;
                return safeData;
            }

            // المستخدم الحالي
            getCurrentUser() {
                return this.currentUser ? this.getSafeUserData(this.currentUser) : null;
            }
        }

        // واجهة التسجيل
        class StandaloneAuthInterface {
            constructor() {
                this.userManager = new StandaloneUserManager();
                this.currentForm = 'login';
                this.init();
            }

            init() {
                this.bindEvents();
                this.checkExistingSession();
            }

            // ربط الأحداث
            bindEvents() {
                // التبديل بين النماذج
                document.getElementById('showRegister').addEventListener('click', (e) => {
                    e.preventDefault();
                    this.showForm('register');
                });

                document.getElementById('showLogin').addEventListener('click', (e) => {
                    e.preventDefault();
                    this.showForm('login');
                });

                // معالجة النماذج
                document.getElementById('loginForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleLogin();
                });

                document.getElementById('registerForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleRegister();
                });
            }

            // عرض النموذج
            showForm(formType) {
                const loginContainer = document.getElementById('loginFormContainer');
                const registerContainer = document.getElementById('registerFormContainer');

                if (formType === 'login') {
                    loginContainer.classList.remove('hidden');
                    registerContainer.classList.add('hidden');
                    this.currentForm = 'login';
                } else {
                    loginContainer.classList.add('hidden');
                    registerContainer.classList.remove('hidden');
                    this.currentForm = 'register';
                }
            }

            // معالجة تسجيل الدخول
            async handleLogin() {
                const username = document.getElementById('loginUsername').value.trim();
                const password = document.getElementById('loginPassword').value;

                if (!username || !password) {
                    this.showNotification('يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
                    return;
                }

                this.showLoader(true);

                try {
                    const result = await this.userManager.login(username, password);

                    if (result.success) {
                        this.showNotification('تم تسجيل الدخول بنجاح! 🎉', 'success');

                        // إظهار رسالة ترحيب
                        setTimeout(() => {
                            this.showWelcomeMessage(result.user);
                        }, 1000);
                    }
                } catch (error) {
                    this.showNotification(error.message, 'error');
                } finally {
                    this.showLoader(false);
                }
            }

            // معالجة التسجيل
            async handleRegister() {
                const formData = new FormData(document.getElementById('registerForm'));

                const userData = {
                    fullName: formData.get('fullName').trim(),
                    username: formData.get('username').trim(),
                    email: formData.get('email').trim(),
                    password: formData.get('password')
                };

                const confirmPassword = formData.get('confirmPassword');

                // التحقق من تطابق كلمة المرور
                if (userData.password !== confirmPassword) {
                    this.showNotification('كلمات المرور غير متطابقة', 'error');
                    return;
                }

                this.showLoader(true);

                try {
                    const result = await this.userManager.register(userData);

                    if (result.success) {
                        this.showNotification('تم إنشاء الحساب بنجاح! 🎉', 'success');

                        // إظهار رسالة تأكيد
                        setTimeout(() => {
                            this.showAccountCreatedMessage(userData);
                        }, 1000);
                    }
                } catch (error) {
                    this.showNotification(error.message, 'error');
                } finally {
                    this.showLoader(false);
                }
            }

            // عرض رسالة الترحيب
            showWelcomeMessage(user) {
                const message = `مرحباً ${user.fullName}! 🎉\n\nتم تسجيل دخولك بنجاح.\nسيتم تحويلك للتطبيق الرئيسي خلال 3 ثوان...`;

                // إظهار رسالة ترحيب
                this.showNotification(message, 'success');

                // التحويل التلقائي للتطبيق الرئيسي
                setTimeout(() => {
                    this.redirectToMainApp();
                }, 3000);
            }

            // عرض رسالة إنشاء الحساب
            showAccountCreatedMessage(userData) {
                const message = `تم إنشاء حسابك بنجاح! 🎉\n\n👤 الاسم: ${userData.fullName}\n📧 البريد: ${userData.email}\n🔑 اسم المستخدم: ${userData.username}`;

                // إظهار حوار تأكيد مع خيارات
                this.showAccountCreatedDialog(userData);
            }

            // حوار تأكيد إنشاء الحساب مع خيارات
            showAccountCreatedDialog(userData) {
                const dialog = document.createElement('div');
                dialog.className = 'account-created-dialog';
                dialog.innerHTML = `
                    <div class="dialog-overlay"></div>
                    <div class="dialog-content">
                        <div class="dialog-header">
                            <div class="success-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <h3>تم إنشاء حسابك بنجاح!</h3>
                            <p>مرحباً بك في TDL</p>
                        </div>
                        <div class="dialog-body">
                            <div class="account-summary">
                                <div class="summary-item">
                                    <i class="fas fa-user"></i>
                                    <span>${userData.fullName}</span>
                                </div>
                                <div class="summary-item">
                                    <i class="fas fa-at"></i>
                                    <span>${userData.username}</span>
                                </div>
                                <div class="summary-item">
                                    <i class="fas fa-envelope"></i>
                                    <span>${userData.email}</span>
                                </div>
                            </div>
                        </div>
                        <div class="dialog-actions">
                            <button class="dialog-btn primary" onclick="authInterface.loginAndRedirect('${userData.username}')">
                                <i class="fas fa-sign-in-alt"></i>
                                تسجيل الدخول والانتقال للتطبيق
                            </button>
                            <button class="dialog-btn secondary" onclick="authInterface.stayOnPage('${userData.username}')">
                                <i class="fas fa-edit"></i>
                                البقاء هنا
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(dialog);

                // إظهار الحوار
                setTimeout(() => {
                    dialog.classList.add('show');
                }, 100);
            }

            // تسجيل الدخول والتحويل
            async loginAndRedirect(username) {
                // إغلاق الحوار
                const dialog = document.querySelector('.account-created-dialog');
                if (dialog) {
                    dialog.classList.add('hide');
                    setTimeout(() => {
                        document.body.removeChild(dialog);
                    }, 300);
                }

                // ملء بيانات تسجيل الدخول
                document.getElementById('loginUsername').value = username;
                this.showForm('login');

                // إظهار رسالة
                this.showNotification('يمكنك الآن تسجيل الدخول بكلمة المرور التي اخترتها', 'info');
            }

            // البقاء في الصفحة
            stayOnPage(username) {
                // إغلاق الحوار
                const dialog = document.querySelector('.account-created-dialog');
                if (dialog) {
                    dialog.classList.add('hide');
                    setTimeout(() => {
                        document.body.removeChild(dialog);
                    }, 300);
                }

                // ملء بيانات تسجيل الدخول
                document.getElementById('loginUsername').value = username;
                this.showForm('login');

                // إظهار رسالة
                this.showNotification('تم إنشاء حسابك بنجاح! يمكنك تسجيل الدخول الآن', 'success');
            }

            // التحويل للتطبيق الرئيسي
            redirectToMainApp() {
                // محاولة فتح التطبيق الرئيسي
                try {
                    // التحقق من وجود التطبيق المستقل أولاً
                    window.location.href = 'standalone-main-app.html?welcome=true';
                } catch (error) {
                    // إذا فشل، جرب التطبيق الأصلي
                    try {
                        window.location.href = 'index.html?welcome=true';
                    } catch (error2) {
                        // إذا فشل التحويل، إظهار رسالة
                        const message = `تم تسجيل الدخول بنجاح! 🎉\n\nللانتقال للتطبيق الرئيسي، افتح أحد الملفات:\n- standalone-main-app.html\n- index.html\n\nأو يمكنك البقاء هنا واستخدام النظام.`;
                        alert(message);
                    }
                }
            }

            // فحص الجلسة الموجودة
            checkExistingSession() {
                if (this.userManager.checkSession()) {
                    const user = this.userManager.getCurrentUser();
                    if (user) {
                        this.showNotification(`مرحباً مرة أخرى ${user.fullName}! 👋`, 'success');
                    }
                }
            }

            // عرض الإشعارات
            showNotification(message, type = 'info') {
                const notification = document.getElementById('authNotification');
                const messageElement = notification.querySelector('.notification-message');

                messageElement.textContent = message;
                notification.className = `auth-notification ${type} show`;

                setTimeout(() => {
                    notification.classList.remove('show');
                }, 4000);
            }

            // عرض مؤشر التحميل
            showLoader(show) {
                const loader = document.getElementById('authLoader');
                if (show) {
                    loader.classList.add('show');
                } else {
                    loader.classList.remove('show');
                }
            }
        }

        // متغير عام للواجهة
        let authInterface;

        // تهيئة التطبيق عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 تحميل واجهة التسجيل المستقلة...');

            try {
                authInterface = new StandaloneAuthInterface();
                console.log('✅ تم تحميل التطبيق بنجاح');
            } catch (error) {
                console.error('❌ خطأ في تحميل التطبيق:', error);
                alert('حدث خطأ في تحميل التطبيق، يرجى إعادة تحميل الصفحة');
            }
        });
    </script>
</body>
</html>