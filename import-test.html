<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاستيراد</title>
</head>
<body>
    <h1>اختبار الاستيراد</h1>
    <div id="result"></div>
    
    <script type="module">
        try {
            // اختبار استيراد TDLDatabase
            import { TDLDatabase } from './js/db.js';
            console.log('تم استيراد TDLDatabase بنجاح:', TDLDatabase);
            
            // اختبار استيراد الدوال من utils.js
            import { showNotification, isValidText, formatDate, debounce } from './js/utils.js';
            console.log('تم استيراد الدوال من utils.js بنجاح');
            
            // اختبار استيراد OfflineManager
            import { OfflineManager } from './js/offline.js';
            console.log('تم استيراد OfflineManager بنجاح:', OfflineManager);
            
            document.getElementById('result').innerHTML = '<p style="color: green;">تم استيراد جميع الوحدات بنجاح!</p>';
            
        } catch (error) {
            console.error('خطأ في الاستيراد:', error);
            document.getElementById('result').innerHTML = '<p style="color: red;">خطأ في الاستيراد: ' + error.message + '</p>';
        }
    </script>
</body>
</html>
