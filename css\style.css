:root {
    --primary-color: #1976d2;
    --primary-hover: #125ea7;
    --danger-color: #f44336;
    --danger-hover: #d32f2f;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --info-color: #2196f3;
    --bg-color: #f4f6fa;
    --text-color: #222;
    --text-light: #757575;
    --border-color: #e0e0e0;
    --card-bg: #fff;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: 0.2s;
    --border-radius: 12px;
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
}

[data-theme="dark"] {
    --bg-color: #121212;
    --card-bg: #1e1e1e;
    --text-color: #e0e0e0;
    --text-light: #9e9e9e;
    --border-color: #333;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

/* إصلاح مشاكل الخلفية للوضع المظلم */
[data-theme="dark"] html,
[data-theme="dark"] body {
    background: var(--bg-color);
}

/* منع ظهور الأجزاء البيضاء في الوضع المظلم */
[data-theme="dark"] * {
    background-color: inherit;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* تحسين أداء عرض المهام */
.active-view {
    display: block !important;
}

#listView.active-view {
    display: block !important;
}

#gridView.active-view {
    display: grid !important;
}

#listView:not(.active-view),
#gridView:not(.active-view) {
    display: none !important;
}

/* منع ظهور الأجزاء البيضاء */
*::before,
*::after {
    box-sizing: border-box;
}

/* إصلاح مشاكل الخلفية */
html {
    background: var(--bg-color);
    min-height: 100vh;
}

body {
    background: var(--bg-color);
    min-height: 100vh;
    position: relative;
}

/* منع التمرير الأفقي */
html, body {
    overflow-x: hidden;
    width: 100%;
}

/* إصلاح مشاكل التمرير على الأجهزة المحمولة */
@media (max-width: 768px) {
    html, body {
        -webkit-overflow-scrolling: touch;
        overscroll-behavior: none;
    }
}

body, html {
    font-family: 'Cairo', sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    line-height: 1.6;
    width: 100vw;
    min-height: 100vh;
    height: 100vh;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    overflow-x: hidden;
    overflow-y: auto;
    transition: var(--transition);
    font-size: 0.92rem;
    position: relative;
}

/* منع ظهور الأجزاء البيضاء عند التمرير */
html {
    background: var(--bg-color);
    overflow-x: hidden;
}

body {
    background: var(--bg-color);
    min-height: 100vh;
    position: relative;
}

/* إصلاح مشاكل التمرير على الأجهزة المحمولة */
@media (max-width: 768px) {
    body, html {
        overflow-x: hidden;
        -webkit-overflow-scrolling: touch;
        overscroll-behavior: none;
    }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    min-height: 100vh;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    background: transparent;
}

/* Header Styles */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
    margin-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    flex-wrap: wrap;
    gap: var(--spacing-md);
    padding: 0.7rem 0.7rem 0.3rem 0.7rem !important;
    min-height: 60px;
}
.header h1 {
    font-size: 1.7rem;
    margin-bottom: 0.4rem !important;
}

/* Task Stats Styles */
.task-stats {
    background-color: var(--card-bg);
    padding: 0.5rem 1.2rem;
    border-radius: 50px;
    box-shadow: var(--shadow);
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin: 0;
    border: 1px solid var(--border-color);
}

.task-stats span {
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
}

.task-stats .total {
    color: #9c27b0; /* لون بنفسجي فاتح */
}

.task-stats .completed {
    color: var(--success-color);
}

.task-stats .remaining {
    color: var(--warning-color);
}

.task-stats i {
    font-size: 1rem;
    opacity: 0.9;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        text-align: center;
    }
    
    .task-stats {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
        gap: 0.8rem;
        padding: 0.8rem;
    }
}

h1 {
    color: var(--primary-color);
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

h1 i {
    font-size: 1.8rem;
}

/* Task Input */
.task-input-container {
    display: flex;
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow);
    border-radius: var(--border-radius);
    overflow: hidden;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    margin-bottom: 0.7rem;
    gap: 0.5rem;
}

#taskInput {
    flex: 1;
    padding: 0.5rem 0.8rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    font-size: 0.95rem;
    background-color: var(--card-bg);
    color: var(--text-color);
    font-family: inherit;
    transition: var(--transition);
}

#taskInput:focus {
    outline: none;
    border-color: var(--primary-hover);
}

[data-theme="dark"] #taskInput {
    background: #2a2a2a;
    color: #ffffff;
    border-color: #444;
}

[data-theme="dark"] #taskInput:focus {
    background: #333;
    border-color: var(--primary-color);
    color: #ffffff;
}

#addTaskBtn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0 var(--spacing-lg);
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

#addTaskBtn:hover {
    background-color: var(--primary-hover);
}

#addTaskBtn:disabled {
    background-color: var(--text-light);
    cursor: not-allowed;
}

/* Controls Panel */
.controls-panel {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    align-items: stretch;
    margin-bottom: 0.7rem;
}
.controls-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    align-items: center;
}

.controls-group {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
    align-items: center;
}

.controls-group:not(:last-child) {
    border-left: 1px solid var(--border-color);
    padding-left: var(--spacing-md);
}

.control-btn {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    font-family: 'Cairo', sans-serif;
    font-size: 0.9rem;
    min-width: 90px;
    padding: 0.4rem 0.9rem;
    border-radius: 20px;
    border: 1px solid var(--border-color, #ddd);
    background: var(--card-bg, #fff);
    color: var(--text-color, #222);
    margin: 0 0.1rem;
    transition: background 0.2s, color 0.2s;
}

.control-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.control-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.control-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Filter Buttons */
.filter-buttons {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    align-items: center;
}

/* View Toggle Buttons */
.view-btn.active {
    background-color: var(--primary-color);
    color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .controls-container {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
    }
    
    .controls-group {
        justify-content: center;
        border-left: none !important;
        padding-left: 0 !important;
        padding-top: var(--spacing-sm);
        border-top: 1px solid var(--border-color);
    }
    
    .controls-group:first-child {
        padding-top: 0;
        border-top: none;
    }
    
    .control-btn {
        flex: 1;
        padding: 0.6rem 0.5rem;
        font-size: 0.85rem;
    }
}

/* أنماط أزرار المسح */
.action-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin: 2rem 0;
    flex-wrap: wrap;
}

.action-btn {
    padding: 0.45rem 0.9rem;
    border: none;
    border-radius: 6px;
    font-family: 'Cairo', sans-serif;
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    background-color: #f3f4f6;
    color: #4b5563;
}

.action-btn i {
    font-size: 1.1rem;
}

.action-btn.danger {
    background-color: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.action-btn.danger:hover {
    background-color: #fee2e2;
}

.action-btn:hover {
    background-color: #e5e7eb;
}

/* أنماط نموذج التأكيد */
.confirmation-dialog {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.confirmation-dialog.active {
    display: flex;
    opacity: 1;
}

.confirmation-content {
    background: var(--card-bg);
    border-radius: 10px;
    padding: 1.75rem;
    width: 90%;
    max-width: 450px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transform: translateY(20px);
    transition: transform 0.3s ease;
    text-align: center;
}

.confirmation-dialog.active .confirmation-content {
    transform: translateY(0);
}

.confirmation-content h3 {
    margin: 0 0 1.25rem 0;
    color: var(--text-color);
    font-size: 1.4rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.confirmation-content h3 i {
    color: #f59e0b;
}

.confirmation-content p {
    margin: 0 0 1.75rem 0;
    color: var(--text-color);
    line-height: 1.6;
    font-size: 1.05rem;
}

.confirmation-buttons {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
}

.btn {
    padding: 0.45rem 0.9rem;
    border: none;
    border-radius: 6px;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
}

.btn-cancel {
    background: #f3f4f6;
    color: #4b5563;
}

.btn-cancel:hover {
    background: #e5e7eb;
}

.btn-danger {
    background: #dc2626;
    color: white;
}

.btn-danger:hover {
    background: #b91c1c;
}

/* تحسينات للهواتف */
@media (max-width: 576px) {
    .confirmation-content {
        padding: 1.5rem;
        width: 95%;
    }
    
    .confirmation-content h3 {
        font-size: 1.25rem;
    }
    
    .confirmation-content p {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .confirmation-buttons {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .btn {
        width: 100%;
        padding: 0.75rem;
    }
    
    .action-buttons {
        gap: 0.75rem;
    }
    
    .action-btn {
        width: 100%;
        justify-content: center;
        padding: 0.75rem;
    }
}

/* Task Actions */
.task-actions {
    display: flex;
    justify-content: flex-start;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

#clearAll, #clearCompleted {
    background: none;
    border: 1px solid var(--danger-color);
    color: var(--danger-color);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    transition: var(--transition);
}

#clearAll:hover, #clearCompleted:hover {
    background-color: var(--danger-color);
    color: white;
}

/* Task List */
.task-list-view {
    flex: 1;
    margin-bottom: var(--spacing-xl);
}

.task-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.task-item {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    transition: var(--transition);
    box-shadow: var(--shadow);
}

.task-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.task-item.completed {
    opacity: 0.7;
}

.task-item.completed .task-text {
    text-decoration: line-through;
    color: var(--text-light);
}

.task-checkbox {
    margin-left: var(--spacing-md);
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.task-text {
    flex: 1;
    margin: 0 var(--spacing-md);
    word-break: break-word;
}

.task-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.task-actions button {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-light);
    transition: var(--transition);
    padding: var(--spacing-xs);
    border-radius: 4px;
}

.task-actions button:hover {
    color: var(--primary-color);
    background-color: rgba(0, 0, 0, 0.05);
}

.task-actions .delete-btn:hover {
    color: var(--danger-color);
}

/* Empty State */
.empty-state {
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl) 0;
    color: var(--text-light);
    text-align: center;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.empty-state p {
    font-size: 1.2rem;
    margin: 0;
}

/* Grid View */
.grid-view {
    display: none;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-md);
}

.grid-view .task-item {
    margin: 0;
    height: 100%;
    flex-direction: column;
    align-items: flex-start;
}

.grid-view .task-text {
    margin: var(--spacing-md) 0;
    width: 100%;
}

.grid-view .task-actions {
    width: 100%;
    justify-content: flex-end;
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

/* Notifications */
.notification-container {
    position: fixed;
    bottom: 1.5rem;
    right: 1.5rem;
    z-index: 2000;
    display: flex;
    flex-direction: column;
    gap: 0.7rem;
    align-items: flex-end;
}

.notification {
    min-width: 280px;
    max-width: 400px;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 500;
    color: #fff;
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 1rem;
    opacity: 0;
    transform: translateX(-50%) translateY(30px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: fixed !important;
    bottom: 2rem !important;
    left: 50% !important;
    z-index: 10000 !important;
}

@keyframes notifIn {
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

.notification.show {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}

.notification.fade-out {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
}

.notification.success { background: #43a047; }
.notification.error { background: #e53935; }
.notification.info { background: #1976d2; }
.notification.warning { background: #f9a825; color: #222; }
.notification .notif-close {
    margin-right: auto;
    background: none;
    border: none;
    color: inherit;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
}
.notification .notif-close:hover {
    opacity: 1;
}

/* Loading State */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl) 0;
    color: var(--text-light);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
    margin-bottom: var(--spacing-md);
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: var(--spacing-md);
    }
    
    .header {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }
    
    .controls-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-buttons, .view-toggle {
        justify-content: center;
    }
    
    .task-actions {
        justify-content: center;
    }
    
    .grid-view {
        grid-template-columns: 1fr;
    }
}

/* Dark Mode Toggle */
#themeToggle {
    font-size: 2.1rem;
    padding: 0.4rem 1.1rem;
    border-radius: 50%;
    background: var(--card-bg, #fff);
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    border: none;
    color: var(--primary-color, #1976d2);
    position: absolute;
    top: 0.7rem;
    left: 0.7rem;
    transition: background 0.2s, color 0.2s;
}

#themeToggle:hover {
    background: #e3f2fd;
    color: #125ea7;
}

#themeToggle.active {
    color: var(--warning-color);
}

/* Task Stats */
.task-stats {
    font-size: 0.95rem;
    color: var(--text-light);
    text-align: center;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.task-item {
    animation: fadeIn 0.3s ease-out forwards;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

/* تحسين صناديق الإدخال */
.task-category-select,
.task-priority-select {
    margin-right: 0.5rem;
    padding: 0.7rem 1.2rem;
    border-radius: 16px;
    border: 2px solid var(--border-color);
    font-family: inherit;
    font-size: 0.95rem;
    font-weight: 600;
    background: linear-gradient(135deg, var(--card-bg), rgba(25, 118, 210, 0.03));
    color: var(--text-color);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    min-width: 140px;
    position: relative;
    appearance: none;
    -webkit-appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.8rem center;
    background-repeat: no-repeat;
    background-size: 1.2em 1.2em;
    padding-right: 3rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.task-category-select:hover,
.task-priority-select:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(25, 118, 210, 0.2);
    background: linear-gradient(135deg, var(--card-bg), rgba(25, 118, 210, 0.08));
}

.task-category-select:focus,
.task-priority-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.15);
    transform: translateY(-2px);
    background: linear-gradient(135deg, var(--card-bg), rgba(25, 118, 210, 0.1));
}

/* تخصيص خيارات التصنيف */
.task-category-select option {
    background: var(--card-bg);
    color: var(--text-color);
    padding: 0.5rem;
    font-weight: 500;
}

/* تخصيص خيارات الأولوية */
.task-priority-select option {
    background: var(--card-bg);
    color: var(--text-color);
    padding: 0.5rem;
    font-weight: 500;
}

/* تخصيص خيارات الأولوية حسب اللون */
.task-priority-select option[value="urgent"] {
    color: #f44336;
    font-weight: 600;
}

.task-priority-select option[value="high"] {
    color: #ff9800;
    font-weight: 600;
}

.task-priority-select option[value="medium"] {
    color: #2196f3;
    font-weight: 600;
}

.task-priority-select option[value="low"] {
    color: #4caf50;
    font-weight: 600;
}

/* تحسينات للوضع الليلي */
[data-theme="dark"] .task-category-select,
[data-theme="dark"] .task-priority-select {
    background: linear-gradient(135deg, var(--card-bg), rgba(25, 118, 210, 0.05));
    color: var(--text-color);
    border-color: var(--border-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .task-category-select:hover,
[data-theme="dark"] .task-priority-select:hover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, var(--card-bg), rgba(25, 118, 210, 0.15));
    box-shadow: 0 6px 20px rgba(25, 118, 210, 0.3);
}

[data-theme="dark"] .task-category-select option,
[data-theme="dark"] .task-priority-select option {
    background: var(--card-bg);
    color: var(--text-color);
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .task-category-select,
    .task-priority-select {
        padding: 0.5rem 2rem 0.5rem 0.8rem;
        font-size: 0.85rem;
        min-width: 100px;
        margin-right: 0.3rem;
        height: 44px;
        line-height: 1.1;
        text-align: center;
        vertical-align: middle;
    }
    
    .task-input-bar .task-category-select,
    .task-input-bar .task-priority-select {
        min-width: 100px;
        width: 110px;
        font-size: 0.9rem;
        padding: 0.6rem 2rem 0.6rem 0.8rem;
        height: 44px;
        line-height: 1.1;
    }
    
    .task-input-bar .task-category-select option,
    .task-input-bar .task-priority-select option {
        font-size: 0.9rem;
        padding: 0.6rem 0.8rem;
    }
}

.task-category {
    display: inline-block;
    margin: 0 0.5rem;
    padding: 0.1rem 0.6rem;
    border-radius: 12px;
    background: #e3e3e3;
    color: #333;
    font-size: 0.85em;
    font-weight: 600;
    vertical-align: middle;
}

.category-filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    align-items: center;
    margin: 0.5rem 0 0 0;
    padding: 0;
    background: none;
    box-shadow: none;
    margin-bottom: 0.2rem;
}

.category-filter-btn {
    padding: 0.4rem 1.1rem;
    border-radius: 20px;
    border: 1px solid var(--border-color, #ddd);
    background: var(--card-bg, #fff);
    color: var(--text-color, #222);
    font-size: 1rem;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
}
.category-filter-btn.active,
.category-filter-btn:focus {
    background: var(--primary-color, #009688);
    color: #fff;
    border-color: var(--primary-color, #009688);
}

/* --- Sidebar Popup --- */
.sidebar-popup {
  position: fixed;
  top: 0; right: 0;
  width: 320px;
  max-width: 90vw;
  height: 100vh;
  background: var(--card-bg, #fff);
  box-shadow: -2px 0 16px rgba(0,0,0,0.08);
  z-index: 1100;
  display: none;
  flex-direction: column;
  transition: transform 0.25s cubic-bezier(.4,0,.2,1), opacity 0.2s;
  transform: translateX(100%);
}
.sidebar-popup.active {
    display: flex;
  transform: translateX(0);
  opacity: 1;
}
.sidebar-content {
  padding: 2rem 1.2rem 1.2rem 1.2rem;
  height: 100%;
  overflow-y: auto;
}
.sidebar-overlay {
  display: none;
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.18);
  z-index: 1099;
  transition: opacity 0.2s;
}
.sidebar-popup.active ~ .sidebar-overlay {
  display: block;
  opacity: 1;
}
.side-menu-close {
  position: absolute;
  left: 1rem;
  top: 1rem;
  background: none;
  border: none;
  font-size: 1.7rem;
  color: #888;
  cursor: pointer;
}

/* --- Main Header --- */
.main-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  background: var(--card-bg, #fff);
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  min-height: 54px;
  position: relative;
  z-index: 10;
}
.main-header h1 {
  font-size: 1.3rem;
  margin: 0;
  color: var(--text-color, #222);
  flex: 1;
    text-align: center;
}
.menu-toggle {
  background: none;
  border: none;
  font-size: 2rem;
  color: var(--text-color, #222);
  cursor: pointer;
}
#themeToggle {
  background: none;
  border: none;
  font-size: 1.7rem;
  color: var(--text-color, #222);
  cursor: pointer;
}

/* --- Toolbar --- */
.toolbar {
  display: flex;
  align-items: center;
    gap: 1rem;
  padding: 1rem 0.7rem 0.5rem 0.7rem;
  background: none;
    flex-wrap: wrap;
}
.search-input {
  flex: 1 1 200px;
  padding: 0.5rem 1rem;
    border-radius: 20px;
  border: 1px solid var(--border-color, #ddd);
  font-size: 1rem;
    background: var(--card-bg, #fff);
    color: var(--text-color, #222);
}
.add-task-btn {
  padding: 0.5rem 1.2rem;
  border-radius: 20px;
    border: none;
  background: var(--primary-color, #009688);
  color: #fff;
  font-size: 1rem;
  font-weight: 600;
    cursor: pointer;
  transition: background 0.2s;
}
.add-task-btn:hover {
  background: var(--primary-hover, #00796b);
}
.quick-filters {
  display: flex;
  gap: 0.5rem;
}
.filter-btn {
  padding: 0.4rem 1.1rem;
  border-radius: 20px;
  border: 1px solid var(--border-color, #ddd);
  background: var(--card-bg, #fff);
    color: var(--text-color, #222);
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.filter-btn.active,
.filter-btn:focus {
  background: var(--primary-color, #009688);
  color: #fff;
  border-color: var(--primary-color, #009688);
}

/* --- Stats Bar --- */
.stats-bar {
  display: flex;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  margin: 0.5rem 0 1rem 0;
}
.stat-box {
    background: var(--card-bg, #fff);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 0.7rem 1.2rem;
  font-size: 1rem;
  color: var(--text-color, #222);
  min-width: 110px;
  text-align: center;
}

.stat-box.stat-active span {
    color: var(--primary-color);
}

[data-theme="dark"] .stat-box.stat-active span {
    color: var(--primary-color);
}

/* --- Task List & Cards --- */
.task-list {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 0 0.7rem 1.5rem 0.7rem;
    min-height: 180px;
    max-height: 70vh;
    overflow-y: auto;
    overflow-x: hidden;
}

.task-list::-webkit-scrollbar {
    width: 8px;
}

.task-list::-webkit-scrollbar-track {
    background: rgba(25, 118, 210, 0.1);
    border-radius: 4px;
}

.task-list::-webkit-scrollbar-thumb {
    background: rgba(25, 118, 210, 0.3);
    border-radius: 4px;
}

.task-list::-webkit-scrollbar-thumb:hover {
    background: rgba(25, 118, 210, 0.5);
}
.task-card {
  background: var(--card-bg, #fff);
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.05);
  padding: 0.6rem 0.8rem;
  min-width: 200px;
  max-width: 100%;
  flex: 1 1 200px;
    display: flex;
    flex-direction: column;
  gap: 0.3rem;
  transition: box-shadow 0.18s;
}
.task-card:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.13);
}
.empty-state {
  width: 100%;
  text-align: center;
  color: #aaa;
  margin-top: 2rem;
}

/* --- Responsive --- */
@media (max-width: 700px) {
  .sidebar-popup {
    width: 95vw;
    max-width: 99vw;
  }
  .main-header h1 {
    font-size: 1.1rem;
  }
  .task-card {
    min-width: 90vw;
    padding: 0.5rem 0.4rem;
  }
  .stats-bar {
    flex-direction: column;
    gap: 0.5rem;
  }
}
@media (max-width: 500px) {
  .toolbar {
    flex-direction: column;
    gap: 0.5rem;
  }
  .task-list {
    flex-direction: column;
    gap: 0.7rem;
}
}
body.sidebar-open {
  overflow: hidden;
}

/* --- Sidebar Sections --- */
.sidebar-section {
  margin-bottom: 1.1rem;
}
.sidebar-title {
  font-size: 1.02rem;
  color: var(--primary-color, #009688);
  margin: 0 0 0.5rem 0;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-align: right;
}
.sidebar-section:last-child {
  margin-bottom: 0;
}

/* --- تحسين أزرار القائمة الجانبية --- */
.sidebar-section .nav-btn,
.sidebar-section .cat-btn {
    width: 100%;
  text-align: right;
  padding: 0.5rem 1rem;
    border: none;
  border-radius: 18px;
  background: var(--card-bg, #fff);
  color: var(--text-color, #222);
  font-size: 1.05rem;
    font-family: inherit;
  font-weight: 500;
  margin-bottom: 0.3rem;
    display: flex;
    align-items: center;
    gap: 0.7rem;
  transition: background 0.18s, color 0.18s;
  box-shadow: none;
    cursor: pointer;
}
.sidebar-section .nav-btn:last-child,
.sidebar-section .cat-btn:last-child {
  margin-bottom: 0;
}
.sidebar-section .nav-btn.active,
.sidebar-section .nav-btn:focus,
.sidebar-section .nav-btn:hover,
.sidebar-section .cat-btn.active,
.sidebar-section .cat-btn:focus,
.sidebar-section .cat-btn:hover {
  background: var(--primary-color, #009688);
    color: #fff;
}

/* --- تحسين الفواصل داخل القائمة الجانبية --- */
.sidebar-content hr {
  border: none;
  border-top: 1.5px solid #e0e0e0;
  margin: 1rem 0 1rem 0;
}

/* --- تحسين تباعد الأقسام --- */
.sidebar-section {
  padding-bottom: 0.2rem;
}

/* --- صندوق إضافة مهمة --- */
.task-input-bar {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.2rem 1.5rem;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.08), rgba(118, 75, 162, 0.05));
  border-radius: 20px;
  border: 2px solid rgba(102, 126, 234, 0.15);
  margin-bottom: 2rem;
  backdrop-filter: blur(15px);
  box-shadow: 
      0 8px 32px rgba(102, 126, 234, 0.12),
      0 2px 8px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.task-input-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s ease;
}

.task-input-bar:hover::before {
  left: 100%;
}

.task-input-bar:hover {
  transform: translateY(-2px);
  box-shadow: 
      0 12px 40px rgba(102, 126, 234, 0.18),
      0 4px 12px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border-color: rgba(102, 126, 234, 0.25);
}
.task-input-bar .task-input {
  flex: 2 1 200px;
  padding: 0.8rem 1.2rem;
  border-radius: 16px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.9);
  color: var(--text-color, #333);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  box-shadow: 
      0 2px 8px rgba(102, 126, 234, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.task-input-bar .task-input:focus {
  outline: none;
  border-color: rgba(102, 126, 234, 0.4);
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 
      0 4px 16px rgba(102, 126, 234, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
}

.task-input-bar .task-input::placeholder {
  color: rgba(102, 126, 234, 0.6);
  font-weight: 400;
}
.task-input-bar .task-category-select {
  min-width: 110px;
  width: 120px;
  max-width: 30%;
  padding: 0.8rem 2.5rem 0.8rem 1.2rem;
  border-radius: 16px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.9);
  color: var(--text-color, #333);
  height: 48px;
  box-sizing: border-box;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  box-shadow: 
      0 2px 8px rgba(102, 126, 234, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23667eea' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.8rem center;
  background-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1.2;
  font-family: 'Cairo', sans-serif;
  font-weight: 500;
  text-align: center;
  vertical-align: middle;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.task-input-bar .task-category-select:focus {
  outline: none;
  border-color: rgba(102, 126, 234, 0.4);
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 
      0 4px 16px rgba(102, 126, 234, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
}

.task-input-bar .task-category-select:hover {
  border-color: rgba(102, 126, 234, 0.3);
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 
      0 3px 12px rgba(102, 126, 234, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* تنسيق قائمة الأولوية لتكون متناسقة مع قائمة التصنيف */
.task-input-bar .task-priority-select {
  min-width: 110px;
  width: 120px;
  max-width: 30%;
  padding: 0.8rem 2.5rem 0.8rem 1.2rem;
  border-radius: 16px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.9);
  color: var(--text-color, #333);
  height: 48px;
  box-sizing: border-box;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  box-shadow: 
      0 2px 8px rgba(102, 126, 234, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23667eea' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.8rem center;
  background-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1.2;
  font-family: 'Cairo', sans-serif;
  font-weight: 500;
  text-align: center;
  vertical-align: middle;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.task-input-bar .task-priority-select:focus {
  outline: none;
  border-color: rgba(102, 126, 234, 0.4);
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 
      0 4px 16px rgba(102, 126, 234, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
}

.task-input-bar .task-priority-select:hover {
  border-color: rgba(102, 126, 234, 0.3);
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 
      0 3px 12px rgba(102, 126, 234, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
}
.task-input-bar .add-task-btn {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 1.5rem;
  border-radius: 16px;
  border: none;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
      0 4px 16px rgba(102, 126, 234, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.task-input-bar .add-task-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.task-input-bar .add-task-btn:hover::before {
  left: 100%;
}
.task-input-bar .add-task-btn:hover {
  background: linear-gradient(135deg, #5a6fd8, #6a4c9a);
  transform: translateY(-2px);
  box-shadow: 
      0 8px 25px rgba(102, 126, 234, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.task-input-bar .add-task-btn:active {
  transform: translateY(0);
  box-shadow: 
      0 2px 8px rgba(102, 126, 234, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.search-task-btn {
  height: 48px;
  width: 48px;
  min-width: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  font-size: 1.35rem;
  margin-left: 0.3rem;
  margin-right: 0.15rem;
  box-shadow: 
      0 2px 8px rgba(102, 126, 234, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.search-task-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.search-task-btn:hover::before {
  opacity: 1;
}
.search-task-btn:hover,
.search-task-btn:focus {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 
      0 8px 25px rgba(102, 126, 234, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.search-task-btn:active {
  transform: translateY(0);
  box-shadow: 
      0 2px 8px rgba(102, 126, 234, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* --- Sidebar Popup يبدأ أسفل الهيدر --- */
.sidebar-popup {
  margin-top: 58px;
}
@media (max-width: 700px) {
  .sidebar-popup {
    margin-top: 54px;
  }
}

/* --- تكبير عناوين الأقسام الرئيسية --- */
.sidebar-title-main {
  font-size: 1.22rem;
  color: var(--primary-color, #009688);
  margin: 0 0 0.7rem 0;
    font-weight: 900;
  letter-spacing: 0.5px;
  text-align: right;
}

/* --- شريط مؤشر القائمة الجانبية --- */
.sidebar-indicator {
  width: 100%;
  background: var(--primary-color, #009688);
  color: #fff;
  font-size: 1.08rem;
  font-weight: 700;
  text-align: center;
  padding: 0.5rem 0;
  border-radius: 0 0 12px 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  margin-bottom: 0.5rem;
  letter-spacing: 0.5px;
}

/* --- توحيد تنسيق صندوق إضافة المهمة --- */
#taskInput, .task-input-bar .task-input {
  flex: 2 1 200px;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  border: 1px solid var(--border-color, #ddd);
  font-size: 1rem;
  background: var(--card-bg, #fff);
  color: var(--text-color, #222);
  height: 44px;
  box-sizing: border-box;
}

/* --- تصغير القائمة الجانبية --- */
.sidebar-popup {
  width: 250px;
  max-width: 90vw;
  margin-top: 0;
  top: 0;
}
@media (max-width: 700px) {
  .sidebar-popup {
    width: 95vw;
    max-width: 99vw;
  }
}

/* --- تصغير حجم العناصر داخل القائمة الجانبية --- */
.sidebar-section .nav-btn,
.sidebar-section .cat-btn {
  font-size: 0.93rem;
  padding: 0.38rem 0.8rem;
}
.sidebar-title-main {
  font-size: 1.22rem !important;
  padding: 0.1rem 0 0.3rem 0;
}

/* --- تحسين إشعار النجاح --- */
.notification.success.notification-center {
  top: unset !important;
  bottom: 2.5rem !important;
    left: 50% !important;
  right: unset !important;
  transform: translateX(-50%) !important;
  min-width: 220px;
  max-width: 90vw;
  font-size: 1.08rem;
    z-index: 3000 !important;
}

/* --- تخصيص نافذة تأكيد الحذف --- */
.delete-confirm-modal {
  position: fixed !important;
  left: 50% !important;
  bottom: 2.5rem !important;
  transform: translateX(-50%) !important;
  z-index: 4000 !important;
  background: var(--card-bg, #fff);
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
  border-radius: 16px;
  padding: 1.2rem 1.5rem;
    min-width: 260px;
  max-width: 90vw;
    text-align: center;
  animation: fadeIn 0.18s;
}
.delete-confirm-content p {
  margin-bottom: 1.1rem;
  font-size: 1.08rem;
  color: var(--text-color, #222);
}
.delete-confirm-actions {
    display: flex;
  gap: 0.7rem;
  justify-content: center;
}
.delete-confirm-actions .btn {
  min-width: 90px;
  font-size: 1rem;
  border-radius: 20px;
  padding: 0.45rem 1.1rem;
}

/* --- تخصيص جميع الأزرار وتفاعلها --- */
button,
.add-task-btn,
.search-task-btn,
.control-btn,
.edit-btn,
.delete-btn,
.nav-btn,
.cat-btn {
  outline: none;
  box-shadow: none;
  transition: background 0.18s, color 0.18s, box-shadow 0.18s, transform 0.12s;
  cursor: pointer;
  border: none;
    border-radius: 20px;
  font-family: inherit;
  font-weight: 500;
}

.add-task-btn {
  background: var(--primary-color, #009688);
  color: #fff;
  box-shadow: 0 2px 8px rgba(0,150,136,0.08);
}
.add-task-btn:hover, .add-task-btn:focus {
  background: var(--primary-hover, #00796b);
  box-shadow: 0 4px 16px rgba(0,150,136,0.13);
  transform: translateY(-2px) scale(1.04);
}
.add-task-btn:active {
  background: var(--primary-hover, #00796b);
  box-shadow: 0 1px 4px rgba(0,150,136,0.10);
  transform: scale(0.98);
}

.search-task-btn {
  background: var(--card-bg, #fff);
  color: var(--primary-color, #009688);
  box-shadow: 0 1px 6px rgba(0,0,0,0.07);
}
.search-task-btn:hover, .search-task-btn:focus {
  background: var(--primary-color, #009688);
  color: #fff;
  box-shadow: 0 2px 12px rgba(0,150,136,0.13);
  transform: translateY(-1px) scale(1.05);
}
.search-task-btn:active {
  background: var(--primary-hover, #00796b);
  color: #fff;
  transform: scale(0.97);
}

.control-btn {
    background: var(--card-bg, #fff);
    color: var(--text-color, #222);
  margin: 0 0.1rem;
  padding: 0.35rem 0.9rem;
  border-radius: 16px;
  font-size: 1rem;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}
.control-btn:hover, .control-btn:focus {
  background: #e0f2f1;
  color: var(--primary-color, #009688);
  box-shadow: 0 2px 8px rgba(0,150,136,0.10);
}
.edit-btn {
  color: #1976d2;
}
.edit-btn:hover, .edit-btn:focus {
  background: #e3f2fd;
  color: #1976d2;
}
.delete-btn {
  color: #f44336;
}
.delete-btn:hover, .delete-btn:focus {
  background: #ffebee;
  color: #d32f2f;
}

.nav-btn, .cat-btn {
  background: var(--card-bg, #fff);
  color: var(--text-color, #222);
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  margin-bottom: 0.3rem;
  padding: 0.38rem 0.8rem;
  border-radius: 18px;
  font-size: 0.93rem;
}
.nav-btn.active, .cat-btn.active, .nav-btn:focus, .cat-btn:focus {
  background: var(--primary-color, #009688);
  color: #fff;
  box-shadow: 0 2px 8px rgba(0,150,136,0.13);
  transform: scale(1.04);
}
.nav-btn:hover, .cat-btn:hover {
  background: #e0f2f1;
  color: var(--primary-color, #009688);
  box-shadow: 0 2px 8px rgba(0,150,136,0.10);
}

.sidebar-view-toggle {
  display: flex;
  gap: 0.5rem;
  margin: 0.5rem 0 0.2rem 0;
  justify-content: flex-start;
}
.view-toggle-btn {
  background: var(--card-bg, #fff);
  color: var(--primary-color, #009688);
  border: none;
  border-radius: 16px;
  font-size: 0.98rem;
  padding: 0.32rem 0.95rem;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  transition: background 0.18s, color 0.18s, box-shadow 0.18s, transform 0.12s;
  display: flex;
    align-items: center;
  gap: 0.4rem;
  cursor: pointer;
}
.view-toggle-btn.active, .view-toggle-btn:focus {
  background: var(--primary-color, #009688);
  color: #fff;
  box-shadow: 0 2px 8px rgba(0,150,136,0.13);
  transform: scale(1.04);
}
.view-toggle-btn:hover {
  background: #e0f2f1;
  color: var(--primary-color, #009688);
}

.task-grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1.1rem;
  width: 100%;
  margin: 0 auto;
  align-items: stretch;
}
@media (max-width: 700px) {
  .task-grid-view {
    grid-template-columns: 1fr;
    gap: 0.7rem;
  }
}

/* تحسين مظهر checkbox المكتملة */
.task-checkbox {
    appearance: none;
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--card-bg);
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
    margin-left: 0.5rem;
}

.task-checkbox:hover {
    border-color: var(--primary-color);
    transform: scale(1.1);
}

.task-checkbox:checked {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.task-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* تحسين مظهر نص المهمة */
.task-title {
    flex: 1;
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-color);
    transition: all 0.2s ease;
    padding: 4px 8px;
    border-radius: 4px;
    margin-right: 0.5rem;
}

.task-title:hover {
    background-color: rgba(25, 118, 210, 0.05);
}

/* تحسين أزرار التعديل المباشر */
.inline-save-btn,
.inline-cancel-btn {
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.inline-save-btn:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.inline-cancel-btn:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

/* تحسين بطاقة المهمة */
.task-card {
    transition: all 0.3s ease;
    position: relative;
}

.task-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.task-card.completed {
    opacity: 0.7;
    background-color: rgba(76, 175, 80, 0.05);
}

.task-card.completed .task-title {
    text-decoration: line-through;
    color: var(--text-light);
}

/* تحسين أزرار التحكم */
.control-btn {
    transition: all 0.2s ease;
    border-radius: 6px;
}

.control-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.edit-btn:hover {
    background-color: var(--primary-hover);
    color: white;
}

.delete-btn:hover {
    background-color: var(--danger-hover);
    color: white;
}

/* تحسين تأثيرات التعديل المباشر */
.task-title[contenteditable="true"] {
    background-color: rgba(25, 118, 210, 0.1);
    border: 2px solid var(--primary-color);
    border-radius: 6px;
    padding: 6px 10px;
    outline: none;
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

/* تحسين مظهر المهام المكتملة */
.task-card.completed .task-checkbox {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.task-card.completed .task-title {
    cursor: not-allowed;
}

.task-card.completed .task-title:hover {
    background-color: transparent;
}

/* تحسين تأثيرات الانتقال */
.task-card-top {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: 8px;
    transition: background-color 0.2s ease;
}

.task-card-top:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* تحسين مظهر الأقسام والتواريخ */
.task-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0.5rem;
    font-size: 0.85rem;
    color: var(--text-light);
}

.task-category {
    background-color: rgba(25, 118, 210, 0.1);
    color: var(--primary-color);
    padding: 2px 6px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.task-date {
    font-size: 0.75rem;
    opacity: 0.8;
}

/* تحسين أزرار الإجراءات */
.task-actions {
    display: flex;
    gap: 0.25rem;
    padding: 0.25rem;
    justify-content: flex-end;
}

/* تحسين التأثيرات البصرية للمهام المكتملة */
.task-card.completed .task-actions .edit-btn {
    opacity: 0.5;
    cursor: not-allowed;
}

.task-card.completed .task-actions .edit-btn:hover {
    transform: none;
    background-color: var(--border-color);
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .task-checkbox {
        width: 18px;
        height: 18px;
    }
    
    .task-title {
        font-size: 0.9rem;
    }
    
    .inline-save-btn,
    .inline-cancel-btn {
        width: 28px;
        height: 28px;
        font-size: 10px;
    }
    
    .task-actions {
        gap: 0.5rem;
    }
    
    .control-btn {
        padding: 0.4rem;
        font-size: 0.8rem;
    }
}

/* عرض قائمة رأسية للمهام */
.task-list-view {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 0.7rem;
    width: 100%;
}

.task-list-item {
    width: 100%;
    margin: 0;
    padding: 0;
    background: none;
    border: none;
    box-shadow: none;
}

.task-list-item .task-card {
    width: 100%;
    margin: 0;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.06);
    border: 1px solid var(--border-color);
    background: var(--card-bg);
}

/* إزالة أي تأثيرات شبكية في وضع القائمة */
.task-list-view .task-card {
    display: flex;
    flex-direction: column;
    align-items: stretch;
}

@media (max-width: 700px) {
    .task-list-view {
        gap: 0.4rem;
    }
    .task-list-item .task-card {
        border-radius: 8px;
        font-size: 0.95rem;
    }
}

/* تصغير إضافي لصندوق المهمة */
.task-list-item .task-card,
.task-card {
    padding: 0.45rem 0.7rem 0.45rem 0.7rem;
    min-height: 36px;
    font-size: 0.93rem;
    border-radius: 8px;
}

.task-card-top {
    padding: 0.18rem 0.1rem;
}

.task-meta {
    padding: 0.08rem 0.25rem;
    font-size: 0.77rem;
}

@media (max-width: 700px) {
    .task-list-item .task-card,
    .task-card {
        padding: 0.32rem 0.3rem 0.32rem 0.3rem;
        font-size: 0.89rem;
        border-radius: 5px;
    }
    .task-meta {
        font-size: 0.7rem;
    }
}

/* صندوق البحث المنبثق */
.search-modal-overlay {
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(0,0,0,0.18);
    z-index: 5000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.2s;
}

.search-modal-box {
    background: var(--card-bg);
    border-radius: 18px;
    box-shadow: 0 8px 32px rgba(25,118,210,0.13);
    padding: 2.2rem 1.5rem 1.5rem 1.5rem;
    min-width: 320px;
    max-width: 95vw;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    gap: 1.1rem;
    position: relative;
    border: 1.5px solid var(--primary-color);
    animation: fadeInUp 0.25s;
}

@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(40px); }
    to   { opacity: 1; transform: translateY(0); }
}

/* تأثير فتح ناعم لصندوق البحث */
.search-modal-animate {
    animation: fadeInUp 0.33s cubic-bezier(.23,1.01,.32,1) both;
    box-shadow: 0 12px 40px 0 rgba(25,118,210,0.18), 0 2px 8px rgba(0,0,0,0.08);
    border: 2.5px solid var(--primary-color);
    background: linear-gradient(135deg, var(--card-bg) 80%, #e3f2fd 100%);
    transition: box-shadow 0.2s, transform 0.18s;
}

.search-modal-animate:focus-within {
    box-shadow: 0 16px 48px 0 rgba(25,118,210,0.22), 0 2px 8px rgba(0,0,0,0.10);
    transform: scale(1.025);
}

/* اهتزاز عند الخطأ */
@keyframes shake {
    0% { transform: translateX(0); }
    20% { transform: translateX(-8px); }
    40% { transform: translateX(8px); }
    60% { transform: translateX(-6px); }
    80% { transform: translateX(6px); }
    100% { transform: translateX(0); }
}
.search-modal-animate.shake {
    animation: shake 0.25s;
}

.search-modal-box input[type="text"] {
    background: linear-gradient(90deg, var(--bg-color) 80%, #e3f2fd 100%);
    box-shadow: 0 1px 6px rgba(25,118,210,0.07);
    transition: box-shadow 0.18s, border 0.18s;
}

.search-modal-box input[type="text"]:focus {
    box-shadow: 0 2px 12px rgba(25,118,210,0.13);
    border-color: var(--primary-hover);
}

.search-modal-close {
    position: absolute;
    top: 0.7rem;
    left: 0.7rem;
    background: none;
    border: none;
    color: var(--danger-color);
    font-size: 1.3rem;
    cursor: pointer;
    transition: color 0.2s;
    z-index: 10;
}

.search-modal-close:hover {
    color: var(--danger-hover);
}

.search-modal-results {
    max-height: 260px;
    overflow-y: auto;
    margin-top: 0.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.search-modal-noresults {
    color: var(--text-light);
    text-align: center;
    margin-top: 1.2rem;
    font-size: 1rem;
}

/* أزرار الحفظ والإلغاء أسفل المهمة */
.inline-edit-actions {
    display: flex;
    justify-content: center;
    gap: 1.2rem;
    margin-top: 0.7rem;
    margin-bottom: 0.2rem;
    width: 100%;
    animation: fadeIn 0.2s;
}

.inline-edit-actions .inline-save-btn,
.inline-edit-actions .inline-cancel-btn {
    min-width: 90px;
    padding: 0.45rem 1.1rem;
    font-size: 1rem;
    border-radius: 7px;
    font-weight: 600;
    border: none;
    outline: none;
    transition: all 0.18s;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.inline-edit-actions .inline-save-btn {
    background: var(--success-color);
    color: #fff;
}
.inline-edit-actions .inline-save-btn:hover {
    background: var(--success-color);
    filter: brightness(1.1);
    transform: scale(1.06);
}

.inline-edit-actions .inline-cancel-btn {
    background: var(--danger-color);
    color: #fff;
}
.inline-edit-actions .inline-cancel-btn:hover {
    background: var(--danger-hover);
    transform: scale(1.06);
}

/* تحسين الهيدر */
.main-header {
    background: linear-gradient(90deg, #1976d2 60%, #43a047 100%);
    color: #fff;
    box-shadow: 0 4px 24px rgba(25, 118, 210, 0.08);
    border-bottom: 2px solid #1976d2;
    padding: 0.7rem 1.2rem 0.3rem 1.2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 62px;
    border-radius: 0 0 18px 18px;
    position: sticky;
    top: 0;
    z-index: 100;
    transition: box-shadow 0.2s;
}
.main-header h1 {
    color: #fff;
    font-size: 1.7rem;
    font-weight: 700;
    letter-spacing: 1px;
    text-shadow: 0 2px 8px rgba(25,118,210,0.13);
}
.menu-toggle, #themeToggle {
    background: rgba(255,255,255,0.13);
    border: none;
    color: #fff;
    font-size: 1.3rem;
    border-radius: 50%;
    width: 38px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.18s, box-shadow 0.18s;
    box-shadow: 0 2px 8px rgba(25,118,210,0.08);
    cursor: pointer;
}
.menu-toggle:hover, #themeToggle:hover {
    background: #fff;
    color: #1976d2;
    box-shadow: 0 4px 16px rgba(25,118,210,0.13);
}

/* تحسين الشريط الجانبي */
.sidebar-popup {
    background: linear-gradient(120deg, #1976d2 70%, #43a047 100%);
    color: #fff;
    box-shadow: 0 8px 32px rgba(25,118,210,0.13);
    border-radius: 0 18px 18px 0;
    border-right: 2px solid #1976d2;
    padding-top: 1.2rem;
}
.sidebar-content nav {
    display: flex;
    flex-direction: column;
    gap: 1.2rem;
}
.sidebar-section {
    margin-bottom: 1.2rem;
}
.sidebar-title-main {
    color: #fff;
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    letter-spacing: 0.5px;
}
.nav-btn, .cat-btn, .view-toggle-btn {
    background: rgba(255,255,255,0.13);
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 0.5rem 1.1rem;
    margin-bottom: 0.3rem;
    font-size: 1rem;
    font-weight: 600;
    transition: background 0.18s, color 0.18s, box-shadow 0.18s;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(25,118,210,0.08);
}
.nav-btn.active, .cat-btn.active, .view-toggle-btn.active {
    background: #fff;
    color: #1976d2;
    box-shadow: 0 4px 16px rgba(25,118,210,0.13);
}
.nav-btn:hover, .cat-btn:hover, .view-toggle-btn:hover {
    background: #fff;
    color: #1976d2;
}

/* تحسين شريط الإحصاءات */
.stats-bar {
    display: flex;
    justify-content: center;
    gap: 2.2rem;
    background: linear-gradient(90deg, #e3f2fd 60%, #c8e6c9 100%);
    border-radius: 14px;
    box-shadow: 0 2px 12px rgba(25,118,210,0.07);
    padding: 0.7rem 0.5rem;
    margin: 1.1rem 0 1.5rem 0;
    border: 1.5px solid #1976d2;
    font-size: 1.08rem;
    font-weight: 600;
    transition: box-shadow 0.18s;
}
.stat-box {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: #fff;
    border-radius: 8px;
    padding: 0.4rem 1.1rem;
    box-shadow: 0 1px 6px rgba(25,118,210,0.07);
    font-size: 1.05rem;
    color: #1976d2;
    transition: background 0.18s, color 0.18s, box-shadow 0.18s;
}
.stat-box span {
    font-size: 1.13rem;
    font-weight: 700;
    color: #43a047;
    transition: color 0.18s, transform 0.18s;
}
.stat-box.stat-completed span {
    color: #1976d2;
}
.stat-box.stat-remaining span {
    color: #f9a825;
}
.stat-box:hover {
    background: #e3f2fd;
    color: #43a047;
    box-shadow: 0 4px 16px rgba(25,118,210,0.13);
}

/* تحسين أزرار الموقع بشكل عام */
button, .btn, .control-btn, .add-task-btn, .search-task-btn {
    border-radius: 8px !important;
    font-weight: 600;
    transition: background 0.18s, color 0.18s, box-shadow 0.18s, transform 0.13s;
    box-shadow: 0 1px 6px rgba(25,118,210,0.07);
}
button:active, .btn:active, .control-btn:active, .add-task-btn:active, .search-task-btn:active {
    transform: scale(0.97);
    box-shadow: 0 2px 12px rgba(25,118,210,0.13);
}

.add-task-btn, .search-task-btn {
    background: linear-gradient(90deg, #1976d2 60%, #43a047 100%);
    color: #fff;
    border: none;
    padding: 0.5rem 1.3rem;
    font-size: 1.08rem;
    margin-right: 0.3rem;
}
.add-task-btn:hover, .search-task-btn:hover {
    background: #fff;
    color: #1976d2;
    box-shadow: 0 4px 16px rgba(25,118,210,0.13);
}

/* تحسين صناديق المهام */
.task-card, .task-list-item .task-card {
    background: linear-gradient(120deg, #fff 80%, #e3f2fd 100%);
    box-shadow: 0 2px 10px rgba(25,118,210,0.06);
    border: 1.5px solid #1976d2;
    border-radius: 10px;
    transition: box-shadow 0.18s, border 0.18s, transform 0.13s;
}
.task-card:hover {
    box-shadow: 0 8px 25px rgba(25,118,210,0.13);
    border-color: #43a047;
    transform: translateY(-2px) scale(1.01);
}

/* تحسين نصوص المهام */
.task-title {
    font-size: 1.05rem;
    font-weight: 600;
    color: #1976d2;
    letter-spacing: 0.2px;
    transition: color 0.18s, background 0.18s;
}
.task-title[contenteditable="true"] {
    background: #e3f2fd;
    border: 2px solid #1976d2;
    color: #222;
}

/* تحسين الفلاتر والأقسام */
.category-filter-btn {
    background: #fff;
    color: #1976d2;
    border: 1.2px solid #1976d2;
    border-radius: 8px;
    padding: 0.3rem 1rem;
    margin: 0.2rem 0.2rem 0.2rem 0;
    font-size: 0.98rem;
    font-weight: 600;
    transition: background 0.18s, color 0.18s, box-shadow 0.18s;
    box-shadow: 0 1px 6px rgba(25,118,210,0.07);
}
.category-filter-btn.active, .category-filter-btn:hover {
    background: #1976d2;
    color: #fff;
    box-shadow: 0 4px 16px rgba(25,118,210,0.13);
}

/* تحسين الرسائل الفارغة */
.empty-state {
    background: linear-gradient(120deg, #fff 80%, #e3f2fd 100%);
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(25,118,210,0.06);
    border: 1.5px solid #1976d2;
    color: #1976d2;
    font-size: 1.1rem;
    padding: 2.2rem 0.7rem;
    margin: 1.2rem 0;
    text-align: center;
    opacity: 0.93;
}

/* تحسينات عامة */
body, html {
    background: linear-gradient(120deg, #e3f2fd 60%, #c8e6c9 100%);
    min-height: 100vh;
    font-family: 'Cairo', sans-serif;
    font-size: 1.01rem;
    color: #222;
    letter-spacing: 0.1px;
    transition: background 0.2s;
}

[data-theme="dark"] body,
[data-theme="dark"] html {
    background: linear-gradient(120deg, #23272f 60%, #1a1d22 100%) !important;
    color: var(--text-color) !important;
}
[data-theme="dark"] .main-header {
    background: linear-gradient(90deg, #23272f 60%, #1976d2 100%) !important;
    color: #fff !important;
    border-bottom: 2px solid #23272f !important;
}
[data-theme="dark"] .sidebar-popup {
    background: linear-gradient(120deg, #23272f 70%, #1976d2 100%) !important;
    color: #fff !important;
    border-right: 2px solid #23272f !important;
}
[data-theme="dark"] .stats-bar {
    background: linear-gradient(90deg, #23272f 60%, #1a1d22 100%) !important;
    border: 1.5px solid #23272f !important;
}
[data-theme="dark"] .stat-box {
    background: #23272f !important;
    color: #90caf9 !important;
    box-shadow: 0 1px 6px rgba(25,118,210,0.13);
}
[data-theme="dark"] .stat-box span {
    color: #90caf9 !important;
}
[data-theme="dark"] .stat-box.stat-completed span {
    color: #4caf50 !important;
}
[data-theme="dark"] .stat-box.stat-remaining span {
    color: #ffd600 !important;
}
[data-theme="dark"] .task-card, [data-theme="dark"] .task-list-item .task-card {
    background: linear-gradient(120deg, #23272f 80%, #1a1d22 100%) !important;
    border: 1.5px solid #23272f !important;
    color: #e0e0e0 !important;
}

[data-theme="dark"] .task-list::-webkit-scrollbar-track {
    background: rgba(25, 118, 210, 0.05);
}

[data-theme="dark"] .task-list::-webkit-scrollbar-thumb {
    background: rgba(25, 118, 210, 0.4);
}

[data-theme="dark"] .task-list::-webkit-scrollbar-thumb:hover {
    background: rgba(25, 118, 210, 0.6);
}
[data-theme="dark"] .task-title {
    color: #90caf9 !important;
}
[data-theme="dark"] .task-title[contenteditable="true"] {
    background: #23272f !important;
    border: 2px solid #1976d2 !important;
    color: #fff !important;
}
[data-theme="dark"] .category-filter-btn {
    background: #23272f !important;
    color: #90caf9 !important;
    border: 1.2px solid #1976d2 !important;
}
[data-theme="dark"] .category-filter-btn.active, [data-theme="dark"] .category-filter-btn:hover {
    background: #1976d2 !important;
    color: #fff !important;
}
[data-theme="dark"] .empty-state {
    background: linear-gradient(120deg, #23272f 80%, #1a1d22 100%) !important;
    border: 1.5px solid #23272f !important;
    color: #90caf9 !important;
}
[data-theme="dark"] .add-task-btn, [data-theme="dark"] .search-task-btn {
    background: linear-gradient(90deg, #23272f 60%, #1976d2 100%) !important;
    color: #fff !important;
}
[data-theme="dark"] .add-task-btn:hover, [data-theme="dark"] .search-task-btn:hover {
    background: #fff !important;
    color: #23272f !important;
}
[data-theme="dark"] .task-checkbox {
    background: #23272f !important;
    border-color: #1976d2 !important;
}
[data-theme="dark"] .task-checkbox:checked {
    background-color: #4caf50 !important;
    border-color: #4caf50 !important;
}
[data-theme="dark"] .task-meta {
    color: #b0bec5 !important;
}
[data-theme="dark"] .inline-edit-actions .inline-save-btn {
    background: #388e3c !important;
    color: #fff !important;
}
[data-theme="dark"] .inline-edit-actions .inline-cancel-btn {
    background: #d32f2f !important;
    color: #fff !important;
}
[data-theme="dark"] .search-modal-animate {
    background: linear-gradient(135deg, #23272f 80%, #1a1d22 100%) !important;
    border: 2.5px solid #1976d2 !important;
    color: #e0e0e0 !important;
}
[data-theme="dark"] .search-modal-box input[type="text"] {
    background: linear-gradient(90deg, #23272f 80%, #1a1d22 100%) !important;
    color: #e0e0e0 !important;
    border: 1.5px solid #1976d2 !important;
}
[data-theme="dark"] .search-modal-box input[type="text"]:focus {
    border-color: #43a047 !important;
}
[data-theme="dark"] .sidebar-section .nav-btn,
[data-theme="dark"] .sidebar-section .cat-btn,
[data-theme="dark"] .sidebar-section .view-toggle-btn {
    background: #23272f !important;
    color: #90caf9 !important;
}
[data-theme="dark"] .sidebar-section .nav-btn.active,
[data-theme="dark"] .sidebar-section .cat-btn.active,
[data-theme="dark"] .sidebar-section .view-toggle-btn.active {
    background: #1976d2 !important;
    color: #fff !important;
}

::-webkit-scrollbar {
    width: 8px;
    background: #e3f2fd;
    border-radius: 8px;
}
::-webkit-scrollbar-thumb {
    background: #1976d2;
    border-radius: 8px;
}

/* تحسينات responsive */
@media (max-width: 700px) {
    .main-header {
        font-size: 1.1rem;
        padding: 0.5rem 0.5rem 0.2rem 0.5rem;
        min-height: 48px;
    }
    .stats-bar {
        gap: 0.7rem;
        font-size: 0.97rem;
        padding: 0.5rem 0.2rem;
    }
    .stat-box {
        font-size: 0.97rem;
        padding: 0.3rem 0.7rem;
    }
    .task-title {
        font-size: 0.97rem;
    }
}

#sidebarDragger {
    position: absolute;
    top: 0;
    left: -7px;
    width: 14px;
    height: 100%;
    cursor: ew-resize;
    z-index: 10000;
    background: linear-gradient(90deg, rgba(25,118,210,0.13) 0%, rgba(25,118,210,0.04) 100%);
    border-radius: 8px 0 0 8px;
    transition: background 0.18s, box-shadow 0.18s;
    box-shadow: 0 0 8px rgba(25,118,210,0.08);
    opacity: 0.5;
}
#sidebarDragger:hover {
    background: linear-gradient(90deg, #1976d2 0%, #43a047 100%);
    opacity: 1;
    box-shadow: 0 0 16px 2px #1976d2;
}

[data-theme="dark"] #sidebarDragger {
    background: linear-gradient(90deg, #23272f 0%, #1976d2 100%);
    box-shadow: 0 0 12px 2px #1976d2;
    opacity: 0.7;
}
[data-theme="dark"] #sidebarDragger:hover {
    background: linear-gradient(90deg, #1976d2 0%, #23272f 100%);
    opacity: 1;
    box-shadow: 0 0 20px 4px #1976d2;
}

/* نافذة الإعدادات المنبثقة */
.settings-modal-overlay {
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(25, 30, 40, 0.25);
    z-index: 6000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.2s;
}
.settings-modal-box {
    background: linear-gradient(120deg, #fff 80%, #e3f2fd 100%);
    border-radius: 22px;
    box-shadow: 0 8px 40px rgba(25,118,210,0.13);
    padding: 2.2rem 1.7rem 1.5rem 1.7rem;
    min-width: 340px;
    max-width: 95vw;
    max-height: 95vh;
    overflow-y: auto;
    position: relative;
    border: 2px solid #1976d2;
    animation: fadeInUp 0.25s;
    display: flex;
    flex-direction: column;
}
.settings-modal-close {
    position: absolute;
    top: 1.1rem;
    left: 1.1rem;
    background: none;
    border: none;
    color: #f44336;
    font-size: 1.5rem;
    cursor: pointer;
    transition: color 0.2s;
    z-index: 10;
}
.settings-modal-close:hover {
    color: #d32f2f;
}
.settings-title {
    text-align: center;
    color: #1976d2;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    letter-spacing: 0.5px;
    padding-bottom: 1rem;
    border-bottom: 3px solid rgba(25, 118, 210, 0.1);
    background: linear-gradient(135deg, rgba(25, 118, 210, 0.02), rgba(25, 118, 210, 0.05));
    margin: -2.2rem -1.7rem 1.5rem -1.7rem;
    padding: 2.2rem 1.7rem 1.5rem 1.7rem;
    border-radius: 22px 22px 0 0;
}
.settings-sections {
    display: flex;
    flex-direction: column;
    gap: 1.7rem;
    max-height: 70vh;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.settings-sections::-webkit-scrollbar {
    width: 6px;
}

.settings-sections::-webkit-scrollbar-track {
    background: rgba(25, 118, 210, 0.1);
    border-radius: 3px;
}

.settings-sections::-webkit-scrollbar-thumb {
    background: rgba(25, 118, 210, 0.3);
    border-radius: 3px;
}

.settings-sections::-webkit-scrollbar-thumb:hover {
    background: rgba(25, 118, 210, 0.5);
}
.settings-section {
    background: linear-gradient(135deg, #f7fafd, #f0f4f8);
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(25,118,210,0.08);
    padding: 1.3rem 1.2rem 1rem 1.2rem;
    margin-bottom: 0.5rem;
    border: 1.5px solid #e3f2fd;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.settings-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(25,118,210,0.12);
    border-color: #bbdefb;
}
.settings-section h3 {
    color: #1976d2;
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.6rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid rgba(25, 118, 210, 0.1);
}
.settings-row {
    display: flex;
    align-items: center;
    gap: 1.2rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    padding: 0.8rem;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 10px;
    border: 1px solid rgba(25, 118, 210, 0.05);
    transition: all 0.2s ease;
}

.settings-row:hover {
    background: rgba(255, 255, 255, 0.8);
    border-color: rgba(25, 118, 210, 0.1);
    transform: translateX(2px);
}
.settings-row label {
    font-size: 1rem;
    color: #333;
    font-weight: 500;
    cursor: pointer;
}
.settings-row select, .settings-row input[type="text"] {
    border-radius: 7px;
    border: 1.2px solid #1976d2;
    padding: 0.35rem 0.8rem;
    font-size: 1rem;
    background: #fff;
    color: #1976d2;
    transition: border 0.18s;
}
.settings-row select:focus, .settings-row input[type="text"]:focus {
    border-color: #43a047;
    outline: none;
}
.settings-row input[type="checkbox"] {
    accent-color: #1976d2;
    width: 1.1em;
    height: 1.1em;
    margin-left: 0.3em;
}
.settings-row .btn {
    margin-right: 0.5rem;
    font-size: 0.97rem;
    padding: 0.4rem 1.1rem;
    border-radius: 7px;
    font-weight: 600;
    border: none;
    outline: none;
    transition: all 0.18s;
    box-shadow: 0 1px 6px rgba(25,118,210,0.07);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}
.settings-row .btn-outline {
    background: #fff;
    color: #1976d2;
    border: 1.2px solid #1976d2;
}
.settings-row .btn-outline:hover {
    background: #1976d2;
    color: #fff;
}
.settings-row .btn-danger {
    background: #f44336;
    color: #fff;
    border: 1.2px solid #f44336;
}
.settings-row .btn-danger:hover {
    background: #d32f2f;
    color: #fff;
}
.settings-row a {
    color: #1976d2;
    text-decoration: underline;
    font-weight: 600;
    margin-left: 0.5rem;
    transition: color 0.18s;
}
.settings-row a:hover {
    color: #43a047;
}
@media (max-width: 600px) {
    .settings-modal-box {
        min-width: 98vw;
        padding: 1.1rem 0.3rem 0.7rem 0.3rem;
    }
    .settings-section {
        padding: 0.7rem 0.3rem 0.3rem 0.3rem;
    }
    .settings-title {
        font-size: 1.1rem;
    }
}

[data-theme="dark"] .settings-modal-box {
    background: linear-gradient(120deg, #23272f 80%, #1a1d22 100%) !important;
    border: 2px solid #1976d2 !important;
    color: #e0e0e0 !important;
}
[data-theme="dark"] .settings-section {
    background: linear-gradient(135deg, #23272f, #1a1d22) !important;
    border: 1.5px solid #2c313a !important;
    color: #e0e0e0 !important;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .settings-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(25, 118, 210, 0.2);
    border-color: #1976d2;
}
[data-theme="dark"] .settings-title {
    background: linear-gradient(135deg, rgba(25, 118, 210, 0.1), rgba(25, 118, 210, 0.05)) !important;
    border-bottom-color: rgba(25, 118, 210, 0.2) !important;
}

[data-theme="dark"] .settings-section h3 {
    color: #90caf9 !important;
    border-bottom-color: rgba(25, 118, 210, 0.2) !important;
}
[data-theme="dark"] .settings-row {
    background: rgba(25, 118, 210, 0.05) !important;
    border-color: rgba(25, 118, 210, 0.1) !important;
}

[data-theme="dark"] .settings-row:hover {
    background: rgba(25, 118, 210, 0.1) !important;
    border-color: rgba(25, 118, 210, 0.2) !important;
}

[data-theme="dark"] .settings-row label {
    color: #e0e0e0 !important;
}
[data-theme="dark"] .settings-row select, [data-theme="dark"] .settings-row input[type="text"] {
    background: #23272f !important;
    color: #90caf9 !important;
    border: 1.2px solid #1976d2 !important;
}
[data-theme="dark"] .settings-row .btn-outline {
    background: #23272f !important;
    color: #90caf9 !important;
    border: 1.2px solid #1976d2 !important;
}
[data-theme="dark"] .settings-row .btn-outline:hover {
    background: #1976d2 !important;
    color: #fff !important;
}
[data-theme="dark"] .settings-row .btn-danger {
    background: #d32f2f !important;
    color: #fff !important;
    border: 1.2px solid #d32f2f !important;
}
[data-theme="dark"] .settings-row .btn-danger:hover {
    background: #b71c1c !important;
    color: #fff !important;
}
[data-theme="dark"] .settings-row a {
    color: #90caf9 !important;
}
[data-theme="dark"] .settings-row a:hover {
    color: #43a047 !important;
}

/* فاصل بين أقسام الإعدادات */
.settings-section:not(:last-child) {
    border-bottom: 2px dashed #e3f2fd;
    margin-bottom: 1.5rem;
    padding-bottom: 1.2rem;
}

/* Accordion: جعل كل قسم قابل للطي */
.settings-section {
    position: relative;
    transition: box-shadow 0.18s, background 0.18s;
}
.settings-section.collapsed > .settings-section-content {
    display: none;
}
.settings-section .accordion-toggle {
    position: absolute;
    right: 0.7rem;
    top: 1.1rem;
    background: none;
    border: none;
    color: #1976d2;
    font-size: 1.1rem;
    cursor: pointer;
    transition: color 0.18s;
    z-index: 2;
}
.settings-section .accordion-toggle:hover {
    color: #43a047;
}
.settings-section.active {
    box-shadow: 0 4px 18px rgba(25,118,210,0.13);
    background: #e3f2fd;
}

/* تمييز القسم النشط */
.settings-section.active h3 {
    color: #43a047;
}

/* تلميحات (tooltips) */
.settings-row [data-tooltip] {
    position: relative;
    cursor: help;
}
.settings-row [data-tooltip]:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    right: 110%;
    top: 50%;
    transform: translateY(-50%);
    background: #1976d2;
    color: #fff;
    padding: 0.3rem 0.7rem;
    border-radius: 7px;
    font-size: 0.93rem;
    white-space: nowrap;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(25,118,210,0.13);
    opacity: 0.97;
}

/* أزرار الحفظ والإلغاء في الأسفل */
.settings-modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1.2rem;
    margin-top: 1.7rem;
    padding-top: 1.1rem;
    border-top: 2px dashed #e3f2fd;
    background: linear-gradient(135deg, rgba(25, 118, 210, 0.02), rgba(25, 118, 210, 0.05));
    margin: 1.7rem -1.7rem -1.5rem -1.7rem;
    padding: 1.5rem 1.7rem 1.5rem 1.7rem;
    border-radius: 0 0 22px 22px;
}
.settings-modal-actions .btn {
    min-width: 120px;
    font-size: 1.1rem;
    padding: 0.7rem 1.5rem;
    border-radius: 12px;
    font-weight: 700;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.settings-modal-actions .btn-save {
    background: linear-gradient(135deg, #1976d2, #43a047);
    color: #fff;
    border: none;
}
.settings-modal-actions .btn-save:hover {
    background: linear-gradient(135deg, #1565c0, #388e3c);
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(25, 118, 210, 0.3);
}
.settings-modal-actions .btn-cancel {
    background: linear-gradient(135deg, #fff, #f8f9fa);
    color: #1976d2;
    border: 2px solid #1976d2;
}
.settings-modal-actions .btn-cancel:hover {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    color: #fff;
    border-color: #f44336;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.3);
}

[data-theme="dark"] .settings-section.active {
    background: #23272f !important;
    box-shadow: 0 4px 18px rgba(25,118,210,0.18);
}
[data-theme="dark"] .settings-section .accordion-toggle {
    color: #90caf9 !important;
}
[data-theme="dark"] .settings-section .accordion-toggle:hover {
    color: #43a047 !important;
}
[data-theme="dark"] .settings-row [data-tooltip]:hover::after {
    background: #23272f;
    color: #90caf9;
    border: 1px solid #1976d2;
}
[data-theme="dark"] .settings-modal-actions .btn-cancel {
    background: linear-gradient(135deg, #23272f, #1a1d22) !important;
    color: #90caf9 !important;
    border: 2px solid #1976d2 !important;
}
[data-theme="dark"] .settings-modal-actions .btn-cancel:hover {
    background: linear-gradient(135deg, #d32f2f, #b71c1c) !important;
    color: #fff !important;
    border-color: #d32f2f !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(211, 47, 47, 0.4);
}

/* ========== تخصيص الألوان والثيمات ========== */

/* الثيمات الجاهزة */
.theme-presets {
  display: flex;
  gap: 0.8rem;
  flex-wrap: wrap;
  margin-top: 0.5rem;
}
.theme-preset {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3rem;
  background: none;
  border: 2px solid transparent;
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 60px;
}
.theme-preset:hover {
  border-color: var(--primary);
  transform: translateY(-2px);
}
.theme-preset.active {
  border-color: var(--primary);
  background: rgba(25, 118, 210, 0.1);
}
.preset-preview {
  width: 40px;
  height: 25px;
  border-radius: 6px;
  border: 1px solid #ddd;
}
.theme-preset span {
  font-size: 0.8rem;
  color: var(--text-color);
}

/* معاينة الثيمات الجاهزة */
.default-theme { background: linear-gradient(45deg, #1976d2, #42a5f5); }
.ocean-theme { background: linear-gradient(45deg, #006064, #00bcd4); }
.forest-theme { background: linear-gradient(45deg, #2e7d32, #4caf50); }
.sunset-theme { background: linear-gradient(45deg, #ff5722, #ff9800); }
.purple-theme { background: linear-gradient(45deg, #7b1fa2, #9c27b0); }

/* حاوية اختيار اللون */
.color-picker-container {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin-top: 0.5rem;
}
#customColorPicker {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  background: none;
}
.color-preview {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  border: 2px solid #ddd;
  background: var(--primary);
}

/* أزرار الوضع */
.theme-mode-selector {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}
.mode-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3rem;
  padding: 0.8rem 1rem;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  background: #fff;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 70px;
}
.mode-btn:hover {
  border-color: var(--primary);
  transform: translateY(-2px);
}
.mode-btn.active {
  border-color: var(--primary);
  background: var(--primary);
  color: #fff;
}
.mode-btn i {
  font-size: 1.2rem;
}
.mode-btn span {
  font-size: 0.8rem;
  font-weight: 600;
}

/* التخصيص المتقدم */
.advanced-colors {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
  border-left: 4px solid var(--primary);
}
.advanced-colors .settings-row {
  margin-bottom: 0.8rem;
}
.advanced-colors input[type="color"] {
  width: 50px;
  height: 35px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}
.advanced-colors input[type="range"] {
  width: 150px;
  margin-right: 0.5rem;
}
#opacityValue {
  font-weight: 600;
  color: var(--primary);
}

/* المعاينة المباشرة */
.live-preview {
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 0.5rem;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.preview-header {
  background: var(--primary);
  color: #fff;
  padding: 0.5rem;
    text-align: center;
  font-size: 0.9rem;
  font-weight: 600;
}
.preview-sidebar {
  background: #f5f5f5;
  padding: 0.5rem;
  text-align: center;
  font-size: 0.8rem;
  border-bottom: 1px solid #e0e0e0;
}
.preview-content {
  padding: 0.8rem;
    display: flex;
    flex-direction: column;
  gap: 0.5rem;
}
.preview-task {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 0.5rem;
  font-size: 0.8rem;
  color: var(--text-color);
}
.preview-button {
  background: var(--primary);
  color: #fff;
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  text-align: center;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: opacity 0.2s;
}
.preview-button:hover {
  opacity: 0.8;
}

/* الوضع الليلي */
[data-theme="dark"] .mode-btn {
  background: #23272f;
  border-color: #444;
  color: #90caf9;
}
[data-theme="dark"] .mode-btn.active {
  background: var(--primary);
  color: #fff;
}
[data-theme="dark"] .advanced-colors {
  background: #2c313a;
  border-left-color: var(--primary);
}
[data-theme="dark"] .live-preview {
  background: #23272f;
  border-color: #444;
}
[data-theme="dark"] .preview-sidebar {
  background: #2c313a;
  border-bottom-color: #444;
}
[data-theme="dark"] .preview-task {
  background: #2c313a;
  border-color: #444;
  color: #90caf9;
}

/* متغيرات CSS للثيمات */
:root {
  --primary: #1976d2;
  --text-color: #333;
  --bg-color: #fff;
  --border-color: #e0e0e0;
  --sidebar-bg: #f5f5f5;
  --task-bg: #fff;
  --shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* ثيم المحيط */
[data-theme-preset="ocean"] {
  --primary: #006064;
  --text-color: #004d40;
  --bg-color: #e0f2f1;
  --border-color: #b2dfdb;
  --sidebar-bg: #e8f5e8;
  --task-bg: #f1f8e9;
  --shadow: 0 2px 8px rgba(0, 96, 100, 0.15);
}

/* ثيم الغابة */
[data-theme-preset="forest"] {
  --primary: #2e7d32;
  --text-color: #1b5e20;
  --bg-color: #e8f5e8;
  --border-color: #c8e6c9;
  --sidebar-bg: #f1f8e9;
  --task-bg: #f9fbe7;
  --shadow: 0 2px 8px rgba(46, 125, 50, 0.15);
}

/* ثيم الغروب */
[data-theme-preset="sunset"] {
  --primary: #ff5722;
  --text-color: #bf360c;
  --bg-color: #fff3e0;
  --border-color: #ffcc02;
  --sidebar-bg: #fff8e1;
  --task-bg: #fffde7;
  --shadow: 0 2px 8px rgba(255, 87, 34, 0.15);
}

/* ثيم البنفسج */
[data-theme-preset="purple"] {
  --primary: #7b1fa2;
  --text-color: #4a148c;
  --bg-color: #f3e5f5;
  --border-color: #e1bee7;
  --sidebar-bg: #f8f0fc;
  --task-bg: #faf5ff;
  --shadow: 0 2px 8px rgba(123, 31, 162, 0.15);
}

/* زر تبديل الوضع في القائمة الجانبية */
#themeToggleSidebar {
  transition: all 0.3s ease;
}

#themeToggleSidebar:hover {
  background: var(--primary);
  color: #fff;
  transform: translateX(-5px);
}

#themeToggleSidebar i {
  transition: transform 0.3s ease;
}

#themeToggleSidebar:hover i {
  transform: rotate(180deg);
}

/* تحسين زر تبديل الوضع في الهيدر */
#themeToggle {
  transition: all 0.3s ease;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
    align-items: center;
  justify-content: center;
}

#themeToggle:hover {
  background: var(--primary);
  color: #fff;
  transform: scale(1.1);
}

#themeToggle i {
  transition: transform 0.3s ease;
}

#themeToggle:hover i {
  transform: rotate(180deg);
}

/* ========== تحسينات الأداء والتأثيرات الحركية ========== */

/* تأثيرات حركية للمهام */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(100px);
  }
}

/* تحسين بطاقات المهام */
.task-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;
}

.task-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.task-card.completed {
  animation: slideIn 0.3s ease;
}

/* تحسين الإحصائيات */
.stats-bar {
  transition: all 0.3s ease;
}

.stat-box {
  transition: all 0.3s ease;
  will-change: transform;
}

.stat-box:hover {
  transform: scale(1.05);
}

.stat-box span {
  transition: all 0.3s ease;
  display: inline-block;
}

/* إشعارات محسنة */
.enhanced-notification {
  position: fixed;
  z-index: 10000;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.12);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
    max-width: 400px;
  transform: translateY(100px);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-notification.show {
  transform: translateY(0);
  opacity: 1;
}

.enhanced-notification.notification-bottom {
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%) translateY(100px);
}

.enhanced-notification.notification-bottom.show {
  transform: translateX(-50%) translateY(0);
}

.enhanced-notification.notification-top {
  top: 2rem;
  left: 50%;
  transform: translateX(-50%) translateY(-100px);
}

.enhanced-notification.notification-top.show {
  transform: translateX(-50%) translateY(0);
}

.enhanced-notification.notification-center {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.8);
}

.enhanced-notification.notification-center.show {
  transform: translate(-50%, -50%) scale(1);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.notification-content i {
  font-size: 1.2rem;
}

.notification-close {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.notification-close:hover {
  opacity: 1;
}

.notification-success {
  background: linear-gradient(135deg, #4caf50, #45a049);
  color: white;
}

.notification-warning {
  background: linear-gradient(135deg, #ff9800, #f57c00);
  color: white;
}

.notification-info {
  background: linear-gradient(135deg, #2196f3, #1976d2);
  color: white;
}

/* تحسين القائمة الجانبية */
.sidebar-popup {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

.sidebar-overlay {
  transition: opacity 0.3s ease;
  backdrop-filter: blur(5px);
}

/* تحسين الأزرار */
.nav-btn, .cat-btn, .view-toggle-btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, background-color;
}

.nav-btn:hover, .cat-btn:hover, .view-toggle-btn:hover {
  transform: translateX(-5px);
}

.nav-btn.active, .cat-btn.active, .view-toggle-btn.active {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(25, 118, 210, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(25, 118, 210, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(25, 118, 210, 0);
  }
}

/* تحسين حقول الإدخال */
.task-input, .search-box input {
  transition: all 0.3s ease;
  will-change: border-color, box-shadow;
}

.task-input:focus, .search-box input:focus {
  transform: scale(1.02);
}

/* تحسين الأزرار الرئيسية */
.add-task-btn, .search-task-btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, background-color;
}

.add-task-btn:hover, .search-task-btn:hover {
  transform: scale(1.05);
}

/* تحسين نافذة الإعدادات */
.settings-modal-overlay {
  transition: opacity 0.3s ease;
  backdrop-filter: blur(10px);
}

.settings-modal-box {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;
}

/* تحسين طريقة العرض */
.task-list[data-view="grid"] {
  transition: all 0.3s ease;
}

.task-list[data-view="grid"] .task-card {
  animation: slideIn 0.3s ease;
}

/* تحسين الحالة الفارغة */
.empty-state {
  animation: fadeIn 0.5s ease;
}

/* تحسين الأداء العام */
* {
  box-sizing: border-box;
}

body {
  will-change: scroll-position;
}

/* تحسين التمرير */
.task-list {
  scroll-behavior: smooth;
}

/* تحسين التفاعل */
button, input, select {
  -webkit-tap-highlight-color: transparent;
}

/* تحسين الأداء على الأجهزة المحمولة */
@media (max-width: 768px) {
  .enhanced-notification {
    max-width: 90%;
    margin: 0 1rem;
  }
  
  .task-card {
    transition: all 0.2s ease;
    padding: 0.4rem;
    margin-bottom: 0.3rem;
  }
  
  .nav-btn:hover, .cat-btn:hover, .view-toggle-btn:hover {
    transform: none;
  }
}

/* تحسين الوضع الليلي */
[data-theme="dark"] .enhanced-notification {
  background: rgba(35, 39, 47, 0.95);
  border-color: rgba(255,255,255,0.1);
}

[data-theme="dark"] .task-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

/* تحسين الأداء للرسوم المتحركة */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ========== صندوق البحث المحسن ========== */

.search-box {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.search-container {
  background: var(--bg-color, #fff);
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  animation: slideIn 0.3s ease;
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.2rem 1.5rem;
  border-bottom: 2px solid var(--border-color, #e0e0e0);
  background: var(--primary, #1976d2);
  color: white;
}

.search-header h3 {
  margin: 0;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.close-search-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-search-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.search-input-container {
  padding: 1.5rem;
}

.search-input {
  width: 100%;
  padding: 0.8rem 1.2rem;
  border: 2px solid var(--border-color, #e0e0e0);
  border-radius: 10px;
  font-size: 1rem;
  background: var(--bg-color, #fff);
  color: var(--text-color, #333);
  transition: all 0.3s ease;
  margin-bottom: 1.2rem;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary, #1976d2);
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
  transform: scale(1.02);
}

.search-options {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.search-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: var(--sidebar-bg, #f5f5f5);
  border: 1px solid var(--border-color, #e0e0e0);
}

.search-option:hover {
  background: var(--primary, #1976d2);
  color: white;
  transform: translateY(-2px);
}

.search-option input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--primary, #1976d2);
}

.search-results {
  padding: 1.5rem 2rem;
  border-top: 1px solid var(--border-color, #e0e0e0);
  background: var(--sidebar-bg, #f5f5f5);
}

.search-stats {
    text-align: center;
  color: var(--text-color, #333);
  font-weight: 600;
}

.search-stats span {
  color: var(--primary, #1976d2);
  font-size: 1.2rem;
}

/* تحسين الوضع الليلي لصندوق البحث */
[data-theme="dark"] .search-container {
  background: var(--bg-color, #23272f);
  border: 1px solid var(--border-color, #444);
}

[data-theme="dark"] .search-input {
  background: var(--bg-color, #23272f);
  color: var(--text-color, #90caf9);
  border-color: var(--border-color, #444);
}

[data-theme="dark"] .search-option {
  background: var(--sidebar-bg, #2c313a);
  border-color: var(--border-color, #444);
  color: var(--text-color, #90caf9);
}

[data-theme="dark"] .search-results {
  background: var(--sidebar-bg, #2c313a);
  border-color: var(--border-color, #444);
}

/* تحسين للأجهزة المحمولة */
@media (max-width: 768px) {
  .search-container {
    width: 95%;
    margin: 1rem;
  }
  
  .search-header {
    padding: 1rem 1.5rem;
  }
  
  .search-input-container {
    padding: 1.5rem;
  }
  
  .search-options {
    flex-direction: column;
    gap: 1rem;
  }
  
  .search-option {
    justify-content: space-between;
  }
}

/* ========== الأنيميشن ========== */

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes slideOut {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
  }
}

/* ========== صندوق البحث المحسن ========== */

/* ===== تحسينات صندوق المهام مع السحب والإفلات ===== */

/* صندوق المهمة المحسن */
.task-card {
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: 16px;
    padding: 1.2rem;
    margin-bottom: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    cursor: grab;
    user-select: none;
}

.task-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.task-card:active {
    cursor: grabbing;
    transform: scale(0.98);
}

/* حالة السحب */
.task-card.dragging {
    opacity: 0.8;
    transform: rotate(5deg) scale(1.02);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
    z-index: 1000;
}

/* منطقة الإفلات */
.task-card.drop-zone {
    border: 2px dashed var(--primary-color);
    background: rgba(25, 118, 210, 0.05);
    transform: scale(1.02);
}

/* محتوى المهمة */
.task-content {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

/* نص المهمة */
.task-text {
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--text-color);
    line-height: 1.5;
    word-break: break-word;
    transition: color 0.2s ease;
}

.task-card.completed .task-text {
    text-decoration: line-through;
    color: var(--text-light);
    opacity: 0.7;
}

/* معلومات المهمة */
.task-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
    font-size: 0.85rem;
    color: var(--text-light);
}

/* التصنيف */
.task-category {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
}

/* الأولوية */
.task-priority {
    padding: 0.25rem 0.6rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.task-priority.urgent {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    color: white;
}

.task-priority.high {
    background: linear-gradient(135deg, #ff9800, #f57c00);
    color: white;
}

.task-priority.medium {
    background: linear-gradient(135deg, #2196f3, #1976d2);
    color: white;
}

.task-priority.low {
    background: linear-gradient(135deg, #4caf50, #388e3c);
    color: white;
}

/* التاريخ */
.task-date {
    color: var(--text-light);
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.task-date i {
    font-size: 0.7rem;
    opacity: 0.8;
}

/* أزرار التحكم */
.task-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* زر الإكمال */
.complete-task-btn {
    background: transparent;
    border: 2px solid var(--success-color);
    color: var(--success-color);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.complete-task-btn:hover {
    background: var(--success-color);
    color: white;
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.task-card.completed .complete-task-btn {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

/* أزرار التعديل والحذف */
.edit-task-btn,
.delete-task-btn {
    background: transparent;
    border: 2px solid var(--border-color);
    color: var(--text-color);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.edit-task-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.delete-task-btn:hover {
    border-color: var(--danger-color);
    color: var(--danger-color);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

/* مؤشر السحب */
.drag-handle {
    position: absolute;
    top: 0.8rem;
    right: 0.8rem;
    color: var(--text-light);
    cursor: grab;
    font-size: 1.2rem;
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

.task-card:hover .drag-handle {
    opacity: 1;
}

.drag-handle:active {
    cursor: grabbing;
}

/* تحسينات للوضع الليلي */
[data-theme="dark"] .task-card {
    background: var(--card-bg);
    border-color: var(--border-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .task-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .task-card.drop-zone {
    background: rgba(25, 118, 210, 0.1);
}

[data-theme="dark"] .task-text {
    color: var(--text-color);
}

[data-theme="dark"] .task-meta {
    color: var(--text-light);
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .task-card {
        padding: 1rem;
        margin-bottom: 0.8rem;
    }
    
    .task-text {
        font-size: 1rem;
    }
    
    .task-meta {
        gap: 0.6rem;
        font-size: 0.8rem;
    }
    
    .task-actions {
        gap: 0.4rem;
        margin-top: 0.8rem;
        padding-top: 0.8rem;
    }
    
    .complete-task-btn,
    .edit-task-btn,
    .delete-task-btn {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }
    
    .drag-handle {
        top: 0.6rem;
        right: 0.6rem;
        font-size: 1rem;
    }
}

/* تأثيرات حركية */
@keyframes taskSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.task-card {
    animation: taskSlideIn 0.4s ease-out;
}

/* تحسين عرض الشبكة */
.task-list[data-view="grid"] {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 1rem;
}

.task-list[data-view="grid"] .task-card {
    margin-bottom: 0;
    height: fit-content;
}

@media (max-width: 768px) {
    .task-list[data-view="grid"] {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0.5rem;
    }
}

/* تحسين عرض القائمة */
.task-list[data-view="list"] {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    padding: 1rem;
    max-height: none;
    overflow: visible;
}

.task-list[data-view="list"] .task-card {
    margin-bottom: 0;
    flex-shrink: 0;
}

/* تأثيرات إضافية */
.task-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.task-card:hover::before {
    opacity: 1;
}

.task-card.completed::before {
    background: linear-gradient(90deg, var(--success-color), #66bb6a);
    opacity: 1;
}

/* نافذة تأكيد الحذف المخصصة */
.custom-confirmation-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.custom-confirmation-modal.show {
    opacity: 1;
}

.custom-confirmation-content {
    background: var(--card-bg);
    border-radius: 16px;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    transform: scale(0.8);
    transition: transform 0.3s ease;
    border: 1px solid var(--border-color);
}

.custom-confirmation-modal.show .custom-confirmation-content {
    transform: scale(1);
}

.custom-confirmation-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.custom-confirmation-header i {
    font-size: 2rem;
    color: #f44336;
}

.custom-confirmation-header h3 {
    margin: 0;
    color: var(--text-color);
    font-size: 1.5rem;
}

.custom-confirmation-body {
    margin-bottom: 2rem;
}

.custom-confirmation-body p {
    margin: 0.5rem 0;
    color: var(--text-color);
    font-size: 1rem;
}

.task-preview {
    background: var(--bg-color);
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
    border-left: 4px solid var(--primary-color);
    font-weight: 600;
    color: var(--text-color);
}

.warning-text {
    color: #f44336 !important;
    font-weight: 600;
    font-size: 0.9rem;
}

.custom-confirmation-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.custom-confirmation-actions button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
}

.btn-cancel {
    background: #f5f5f5;
    color: #666;
}

.btn-cancel:hover {
    background: #e0e0e0;
    transform: translateY(-2px);
}

.btn-confirm {
    background: #f44336;
    color: white;
}

.btn-confirm:hover {
    background: #d32f2f;
    transform: translateY(-2px);
}

[data-theme="dark"] .custom-confirmation-content {
    background: var(--card-bg);
    border-color: var(--border-color);
}

[data-theme="dark"] .btn-cancel {
    background: #333;
    color: #ccc;
}

[data-theme="dark"] .btn-cancel:hover {
    background: #444;
}

[data-theme="dark"] .task-preview {
    background: #2a2a2a;
    border-left-color: var(--primary-color);
}

/* أزرار التعديل */
.edit-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid var(--border-color);
}

.save-edit-btn,
.cancel-edit-btn {
    padding: 0.4rem 0.8rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.save-edit-btn {
    background: var(--success-color);
    color: white;
}

.save-edit-btn:hover {
    background: #388e3c;
    transform: translateY(-1px);
}

.cancel-edit-btn {
    background: #f5f5f5;
    color: #666;
}

.cancel-edit-btn:hover {
    background: #e0e0e0;
    transform: translateY(-1px);
}

.task-text.editing {
    background: rgba(25, 118, 210, 0.1);
    border: 2px solid var(--primary-color);
    border-radius: 6px;
    padding: 0.5rem;
    outline: none;
}

[data-theme="dark"] .cancel-edit-btn {
    background: #333;
    color: #ccc;
}

[data-theme="dark"] .cancel-edit-btn:hover {
    background: #444;
}

[data-theme="dark"] .task-text.editing {
    background: rgba(25, 118, 210, 0.2);
    border-color: var(--primary-color);
}

@media (max-width: 768px) {
    .custom-confirmation-content {
        padding: 1.5rem;
        margin: 1rem;
    }
    
    .custom-confirmation-actions {
        flex-direction: column;
    }
    
    .custom-confirmation-actions button {
        width: 100%;
        justify-content: center;
    }
}

/* تحسين الأداء */
@media (prefers-reduced-motion: reduce) {
    .task-card,
    .complete-task-btn,
    .edit-task-btn,
    .delete-task-btn,
    .custom-confirmation-modal,
    .custom-confirmation-content {
        transition: none;
    }
    
    .task-card {
        animation: none;
    }
}

/* لمعة العداد */
.stat-glow {
    box-shadow: 0 0 16px 2px #1976d2, 0 0 8px 2px var(--primary-color);
    background: linear-gradient(90deg, #e3f2fd 0%, #bbdefb 100%);
    transition: box-shadow 0.4s, background 0.4s;
}
[data-theme="dark"] .stat-glow {
    box-shadow: 0 0 24px 4px #42a5f5, 0 0 8px 2px var(--primary-color);
    background: linear-gradient(90deg, #263238 0%, #37474f 100%);
}

/* لمعة القائمة الجانبية */
.sidebar-popup.active {
    box-shadow: 0 0 40px 0 rgba(25,118,210,0.18), 0 8px 32px 0 rgba(0,0,0,0.18);
    animation: sidebarGlow 0.5s;
}
@keyframes sidebarGlow {
    0% { box-shadow: 0 0 0 0 rgba(25,118,210,0.0); }
    60% { box-shadow: 0 0 40px 0 rgba(25,118,210,0.25); }
    100% { box-shadow: 0 0 40px 0 rgba(25,118,210,0.18); }
}

/* لمعة عامة للأزرار */
button:active, .btn:active, .control-btn:active, .add-task-btn:active, .search-task-btn:active {
    box-shadow: 0 0 12px 2px var(--primary-color);
    filter: brightness(1.08);
}

/* سلاسة عامة */
body, .task-card, .sidebar-popup, .header, .stat-box, .btn, .control-btn, .add-task-btn, .search-task-btn {
    transition: box-shadow 0.3s, background 0.3s, color 0.3s, filter 0.3s, border 0.3s, transform 0.3s;
}

/* ========== نظام البحث المتقدم ========== */

.advanced-search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.85);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(10px);
    animation: fadeIn 0.3s ease-out;
}

.advanced-search-modal {
    background: var(--card-bg);
    border-radius: 14px;
    width: 80%;
    max-width: 450px;
    max-height: 85vh;
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.25);
    position: relative;
    animation: modalSlideIn 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    overflow-y: auto;
    overflow-x: hidden;
    border: 1px solid var(--border-color);
    /* إضافة إمكانية التنقل */
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) transparent;
}

.advanced-search-modal::-webkit-scrollbar {
    width: 8px;
}

.advanced-search-modal::-webkit-scrollbar-track {
    background: rgba(25, 118, 210, 0.1);
    border-radius: 4px;
}

.advanced-search-modal::-webkit-scrollbar-thumb {
    background: rgba(25, 118, 210, 0.3);
    border-radius: 4px;
}

.advanced-search-modal::-webkit-scrollbar-thumb:hover {
    background: rgba(25, 118, 210, 0.5);
}

/* الوضع الداكن لأزرار التعديل */
[data-theme="dark"] .cancel-edit-btn {
    background: #2c313a;
    color: #90caf9;
    border: 1px solid #444;
}

[data-theme="dark"] .cancel-edit-btn:hover {
    background: #3c414a;
    color: #fff;
}

[data-theme="dark"] .task-text.editing {
    background: rgba(25, 118, 210, 0.1);
    border-color: var(--primary-color);
    color: #90caf9;
}

[data-theme="dark"] .task-text.editing:focus {
    background: rgba(25, 118, 210, 0.15);
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.2);
}

/* مؤشر البحث في الواجهة الرئيسية */
.search-indicator {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 12px 20px;
    margin: 10px 0;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    display: none;
    animation: slideDown 0.3s ease-out;
}

.search-indicator-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
}

.search-indicator i {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
}

.search-indicator span {
    flex: 1;
    font-weight: 500;
    font-size: 14px;
}

.btn-clear-search {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 6px 12px;
    border-radius: 8px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.btn-clear-search:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.no-tasks-found {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-muted);
}

.no-tasks-found i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.6;
}

.no-tasks-found h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
    font-weight: 600;
}

.no-tasks-found p {
    margin: 0 0 20px 0;
    font-size: 14px;
    opacity: 0.8;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تمييز النص المطابق في البحث */
mark {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
    padding: 2px 4px;
    border-radius: 4px;
    font-weight: 600;
    box-shadow: 0 1px 3px rgba(255, 215, 0, 0.3);
    animation: highlightPulse 0.6s ease-in-out;
}

/* أزرار التعديل المباشر */
.edit-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    animation: slideInUp 0.3s ease-out;
}

.save-edit-btn,
.cancel-edit-btn {
    background: none;
    border: none;
    padding: 6px 10px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.save-edit-btn {
    background: var(--primary-color);
    color: white;
}

.save-edit-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.cancel-edit-btn {
    background: #f5f5f5;
    color: #666;
    border: 1px solid #ddd;
}

.cancel-edit-btn:hover {
    background: #e0e0e0;
    color: #333;
    transform: translateY(-1px);
}

/* حالة التعديل */
.task-text.editing {
    background: rgba(25, 118, 210, 0.05);
    border: 2px solid var(--primary-color);
    border-radius: 8px;
    padding: 8px 12px;
    outline: none;
    cursor: text;
    transition: all 0.2s ease;
}

.task-text.editing:focus {
    background: rgba(25, 118, 210, 0.1);
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes slideOutDown {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translateY(0);
    }
    40%, 43% {
        transform: translateY(-10px);
    }
    70% {
        transform: translateY(-5px);
    }
    90% {
        transform: translateY(-2px);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes highlightPulse {
    0% {
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        transform: scale(1);
    }
    50% {
        background: linear-gradient(135deg, #ffed4e, #ffd700);
        transform: scale(1.05);
    }
    100% {
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        transform: scale(1);
    }
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px) scale(0.95);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

/* رأس البحث */
.search-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
}

.search-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.search-title i {
    font-size: 1.5rem;
    color: rgba(255, 255, 255, 0.9);
}

.search-title h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.search-close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 0.75rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

.search-close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg) scale(1.1);
}

/* منطقة البحث الرئيسية */
.search-input-area {
    padding: 1.5rem;
}

.main-search-input {
    position: relative;
    margin-bottom: 1.5rem;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    font-size: 1.2rem;
    z-index: 2;
}

.main-search-input input {
    width: 100%;
    padding: 1rem 2.5rem 1rem 2.5rem;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    font-size: 1rem;
    background: var(--bg-color);
    color: var(--text-color);
    transition: all 0.2s ease;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.main-search-input input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.15), 0 8px 25px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.clear-search-btn {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: var(--border-color);
    border: none;
    color: var(--text-light);
    padding: 0.5rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0.7;
}

.clear-search-btn:hover {
    background: var(--primary-color);
    color: white;
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
}

/* خيارات البحث */
.search-options {
    margin-bottom: 1.5rem;
}

.search-filters {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-btn {
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    color: var(--text-color);
    padding: 0.5rem 0.8rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 0.4rem;
    font-weight: 500;
}

.filter-btn:hover {
    border-color: var(--primary-color);
    background: rgba(25, 118, 210, 0.1);
    transform: translateY(-1px);
}

.filter-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.filter-btn i {
    font-size: 0.85rem;
}

/* منطقة النتائج */
.search-results-area {
    border-top: 1px solid var(--border-color);
    padding-top: 1.5rem;
}

.results-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding: 0 0.5rem;
}

.results-count {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.95rem;
}

.results-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 0.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.action-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

/* قائمة النتائج */
.results-list {
    max-height: 300px;
    overflow-y: auto;
    border-radius: 8px;
    background: var(--bg-color);
    border: 1px solid var(--border-color);
}

.no-results {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-light);
}

.no-results i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
    color: var(--border-color);
}

.no-results p {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    font-weight: 500;
}

.no-results small {
    opacity: 0.7;
    font-size: 0.9rem;
}

/* عناصر النتائج */
.search-result-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.2rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    background: linear-gradient(135deg, var(--card-bg), rgba(25, 118, 210, 0.02));
    border-radius: 12px;
    margin-bottom: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.search-result-item:hover {
    background: linear-gradient(135deg, var(--card-bg), rgba(25, 118, 210, 0.08));
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(25, 118, 210, 0.15);
    border-color: rgba(25, 118, 210, 0.2);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-item.completed {
    opacity: 0.7;
}

.search-result-item.completed .result-title {
    text-decoration: line-through;
}

.result-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.result-text {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.result-number {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 600;
    min-width: 2rem;
    text-align: center;
}

.result-title {
    flex: 1;
    font-weight: 500;
    line-height: 1.4;
    color: var(--text-color);
}

.result-title mark {
    background: rgba(255, 193, 7, 0.3);
    color: inherit;
    padding: 0.1rem 0.2rem;
    border-radius: 3px;
    font-weight: 600;
}

.result-meta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-left: 2.75rem;
}

.result-category,
.result-priority,
.result-status {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    font-size: 0.85rem;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    background: var(--card-bg);
    color: var(--text-light);
}

.result-priority.urgent {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

.result-priority.high {
    background: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

.result-priority.medium {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.result-priority.low {
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.result-actions {
    display: flex;
    gap: 0.8rem;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    margin-top: 0.5rem;
    border: 1px solid rgba(25, 118, 210, 0.1);
}

.search-result-item:hover .result-actions {
    opacity: 1;
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(25, 118, 210, 0.2);
}

.result-action-btn {
    background: linear-gradient(135deg, rgba(25, 118, 210, 0.1), rgba(25, 118, 210, 0.05));
    border: 1px solid rgba(25, 118, 210, 0.2);
    color: var(--text-color);
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 13px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 80px;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.result-action-btn:hover {
    background: linear-gradient(135deg, var(--primary-color), #1565c0);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.result-action-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(25, 118, 210, 0.2);
}

.result-action-btn.delete {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.1), rgba(244, 67, 54, 0.05));
    border-color: rgba(244, 67, 54, 0.2);
}

.result-action-btn.delete:hover {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    border-color: #f44336;
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

.result-action-btn.complete {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.05));
    border-color: rgba(76, 175, 80, 0.2);
}

.result-action-btn.complete:hover {
    background: linear-gradient(135deg, #4caf50, #388e3c);
    border-color: #4caf50;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.result-action-btn.edit {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05));
    border-color: rgba(255, 193, 7, 0.2);
}

.result-action-btn.edit:hover {
    background: linear-gradient(135deg, #ffc107, #f57c00);
    border-color: #ffc107;
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

/* الوضع الداكن */
[data-theme="dark"] .advanced-search-modal {
    background: #1a1a1a;
    border-color: #333;
}

[data-theme="dark"] .advanced-search-modal::-webkit-scrollbar-track {
    background: rgba(25, 118, 210, 0.05);
}

[data-theme="dark"] .advanced-search-modal::-webkit-scrollbar-thumb {
    background: rgba(25, 118, 210, 0.4);
}

[data-theme="dark"] .advanced-search-modal::-webkit-scrollbar-thumb:hover {
    background: rgba(25, 118, 210, 0.6);
}

[data-theme="dark"] .search-header {
    background: linear-gradient(135deg, #1976d2, #1565c0);
}

[data-theme="dark"] .main-search-input input {
    background: #2a2a2a;
    border-color: #444;
    color: #ffffff;
}

[data-theme="dark"] .main-search-input input:focus {
    border-color: #1976d2;
    box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.2);
}

[data-theme="dark"] .filter-btn {
    background: #2a2a2a;
    border-color: #444;
    color: #ffffff;
}

[data-theme="dark"] .filter-btn:hover {
    border-color: #1976d2;
    background: rgba(25, 118, 210, 0.2);
}

[data-theme="dark"] .results-list {
    background: #2a2a2a;
    border-color: #444;
}

[data-theme="dark"] .search-result-item {
    background: linear-gradient(135deg, #2a2a2a, rgba(25, 118, 210, 0.05));
    border-color: #444;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .search-result-item:hover {
    background: linear-gradient(135deg, #2a2a2a, rgba(25, 118, 210, 0.15));
    box-shadow: 0 6px 20px rgba(25, 118, 210, 0.25);
    border-color: rgba(25, 118, 210, 0.4);
}

[data-theme="dark"] .result-category,
[data-theme="dark"] .result-priority,
[data-theme="dark"] .result-status {
    background: #333;
    color: #ccc;
}

[data-theme="dark"] .result-action-btn {
    background: linear-gradient(135deg, rgba(25, 118, 210, 0.15), rgba(25, 118, 210, 0.08));
    border-color: rgba(25, 118, 210, 0.3);
    color: #e0e0e0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .result-action-btn:hover {
    background: linear-gradient(135deg, var(--primary-color), #1565c0);
    color: white;
    border-color: var(--primary-color);
    box-shadow: 0 6px 20px rgba(25, 118, 210, 0.4);
}

[data-theme="dark"] .result-action-btn.delete {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.15), rgba(244, 67, 54, 0.08));
    border-color: rgba(244, 67, 54, 0.3);
}

[data-theme="dark"] .result-action-btn.delete:hover {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    border-color: #f44336;
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
}

[data-theme="dark"] .result-action-btn.complete {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.15), rgba(76, 175, 80, 0.08));
    border-color: rgba(76, 175, 80, 0.3);
}

[data-theme="dark"] .result-action-btn.complete:hover {
    background: linear-gradient(135deg, #4caf50, #388e3c);
    border-color: #4caf50;
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

[data-theme="dark"] .result-action-btn.edit {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.15), rgba(255, 193, 7, 0.08));
    border-color: rgba(255, 193, 7, 0.3);
}

[data-theme="dark"] .result-action-btn.edit:hover {
    background: linear-gradient(135deg, #ffc107, #f57c00);
    border-color: #ffc107;
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
}

[data-theme="dark"] .result-actions {
    background: rgba(25, 118, 210, 0.05);
    border-color: rgba(25, 118, 210, 0.1);
}

[data-theme="dark"] .search-result-item:hover .result-actions {
    background: rgba(25, 118, 210, 0.1);
    border-color: rgba(25, 118, 210, 0.2);
}

/* اقتراحات البحث */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    display: none;
    margin-top: 0.5rem;
}

.suggestion-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid var(--border-color);
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item:hover,
.suggestion-item.selected {
    background: rgba(25, 118, 210, 0.1);
    transform: translateX(4px);
}

.suggestion-item i {
    color: var(--primary-color);
    font-size: 0.9rem;
    width: 1rem;
    text-align: center;
}

.suggestion-text {
    flex: 1;
    font-weight: 500;
    color: var(--text-color);
}

.suggestion-type {
    font-size: 0.8rem;
    color: var(--text-light);
    background: var(--border-color);
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
}

/* خيارات البحث المتقدمة */
.advanced-search-options {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 0.5rem;
}

.checkbox-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-color);
    transition: all 0.2s ease;
}

.checkbox-option:hover {
    color: var(--primary-color);
}

.checkbox-option input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: all 0.2s ease;
}

.checkbox-option input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-option input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.8rem;
    font-weight: bold;
}

/* معلومات النتائج المحسنة */
.results-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.search-time {
    font-size: 0.8rem;
    color: var(--text-light);
    font-style: italic;
}

/* درجات التطابق */
.match-score {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.excellent-match .match-score {
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.good-match .match-score {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.fair-match .match-score {
    background: rgba(158, 158, 158, 0.1);
    color: #9e9e9e;
}

/* رأس النتيجة */
.result-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

/* تاريخ النتيجة */
.result-date {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    font-size: 0.85rem;
    color: var(--text-light);
    margin-top: 0.3rem;
}

/* تأثيرات حركية للنتائج */
.search-result-item {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.result-animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* تحسينات إضافية */
.search-result-item.excellent-match {
    border-left: 4px solid #4caf50;
}

.search-result-item.good-match {
    border-left: 4px solid #ffc107;
}

.search-result-item.fair-match {
    border-left: 4px solid #9e9e9e;
}

/* الوضع الداكن */
[data-theme="dark"] .search-suggestions {
    background: #2a2a2a;
    border-color: #444;
}

[data-theme="dark"] .suggestion-item:hover,
[data-theme="dark"] .suggestion-item.selected {
    background: rgba(25, 118, 210, 0.2);
}

[data-theme="dark"] .suggestion-type {
    background: #444;
    color: #ccc;
}

[data-theme="dark"] .checkmark {
    border-color: #555;
}

[data-theme="dark"] .checkbox-option:hover {
    color: #42a5f5;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .advanced-search-modal {
        width: 98%;
        max-height: 95vh;
        margin: 1rem;
    }
    
    .search-header {
        padding: 1rem 1.5rem;
    }
    
    .search-input-area {
        padding: 1.5rem;
    }
    
    .filter-buttons {
        gap: 0.4rem;
    }
    
    .filter-btn {
        padding: 0.5rem 0.8rem;
        font-size: 0.85rem;
    }
    
    .advanced-search-options {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .result-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .result-actions {
        opacity: 1;
    }
    
    .result-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .search-suggestions {
        max-height: 200px;
    }
}

/* ===== واجهة البحث الجديدة ===== */
.search-interface-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(15px);
    z-index: 10000;
    display: none;
    animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: scale(0.95);
}

.search-interface-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.search-interface-overlay.show {
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(1);
}

.search-interface-modal {
    background: linear-gradient(135deg, #fff 80%, #f8f9fa 100%);
    border-radius: 28px;
    box-shadow: 
        0 25px 80px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    width: 95%;
    max-width: 900px;
    max-height: 85vh;
    overflow: hidden;
    position: relative;
    animation: slideInUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid rgba(25, 118, 210, 0.1);
    transform: translateY(20px);
    opacity: 0;
}

.search-interface-overlay.show .search-interface-modal {
    transform: translateY(0);
    opacity: 1;
}

/* رأس واجهة البحث */
.search-interface-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, #1976d2, #1565c0);
    color: white;
    border-radius: 24px 24px 0 0;
}

.search-interface-title {
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.search-interface-title i {
    font-size: 1.5rem;
    color: #fff;
}

.search-interface-title h3 {
    font-size: 1.4rem;
    font-weight: 700;
    margin: 0;
    color: #fff;
}

.search-interface-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 0.8rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

.search-interface-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* أزرار اختيار نوع البحث */
.search-type-selector {
    display: flex;
    padding: 1rem 2rem;
    background: rgba(25, 118, 210, 0.05);
    border-bottom: 1px solid rgba(25, 118, 210, 0.1);
    position: relative;
    overflow: hidden;
}

.search-type-selector::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #1976d2, #42a5f5, #1976d2);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.search-type-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.6rem;
    padding: 1.2rem 1.5rem;
    background: transparent;
    border: 2px solid transparent;
    border-radius: 16px;
    color: #666;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 1rem;
    position: relative;
    overflow: hidden;
}

.search-type-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(25, 118, 210, 0.1);
    border-radius: 50%;
    transition: all 0.4s ease;
    transform: translate(-50%, -50%);
}

.search-type-btn:hover::before {
    width: 300px;
    height: 300px;
}

.search-type-btn:hover {
    background: rgba(25, 118, 210, 0.1);
    color: #1976d2;
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(25, 118, 210, 0.2);
}

.search-type-btn.active {
    background: linear-gradient(135deg, #1976d2, #1565c0);
    color: white;
    border-color: #1976d2;
    box-shadow: 0 6px 20px rgba(25, 118, 210, 0.4);
    transform: translateY(-2px);
}

.search-type-btn.active::before {
    background: rgba(255, 255, 255, 0.1);
}

.search-type-btn i {
    font-size: 1.1rem;
}

/* محتوى البحث */
.search-content {
    display: none;
    padding: 2rem;
    max-height: 60vh;
    overflow-y: auto;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.search-content.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

/* تحسين التمرير في النتائج */
.quick-search-results,
.advanced-search-results-area {
    max-height: 45vh;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.quick-results-list,
.results-list {
    max-height: 40vh;
    overflow-y: auto;
    padding-bottom: 1rem;
}

/* تحسين شريط التمرير */
.quick-search-results::-webkit-scrollbar,
.advanced-search-results-area::-webkit-scrollbar,
.quick-results-list::-webkit-scrollbar,
.results-list::-webkit-scrollbar {
    width: 8px;
}

.quick-search-results::-webkit-scrollbar-track,
.advanced-search-results-area::-webkit-scrollbar-track,
.quick-results-list::-webkit-scrollbar-track,
.results-list::-webkit-scrollbar-track {
    background: rgba(102, 126, 234, 0.1);
    border-radius: 4px;
}

.quick-search-results::-webkit-scrollbar-thumb,
.advanced-search-results-area::-webkit-scrollbar-thumb,
.quick-results-list::-webkit-scrollbar-thumb,
.results-list::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.3);
    border-radius: 4px;
}

.quick-search-results::-webkit-scrollbar-thumb:hover,
.advanced-search-results-area::-webkit-scrollbar-thumb:hover,
.quick-results-list::-webkit-scrollbar-thumb:hover,
.results-list::-webkit-scrollbar-thumb:hover {
    background: rgba(102, 126, 234, 0.5);
}

.search-content::-webkit-scrollbar {
    width: 8px;
}

.search-content::-webkit-scrollbar-track {
    background: rgba(25, 118, 210, 0.1);
    border-radius: 4px;
}

.search-content::-webkit-scrollbar-thumb {
    background: rgba(25, 118, 210, 0.3);
    border-radius: 4px;
}

.search-content::-webkit-scrollbar-thumb:hover {
    background: rgba(25, 118, 210, 0.5);
}

/* البحث السريع */
.quick-search-container {
    margin-bottom: 2rem;
}

.quick-search-input-wrapper {
    position: relative;
    margin-bottom: 1rem;
}

.quick-search-input {
    width: 100%;
    padding: 1.2rem 3rem 1.2rem 3.5rem;
    border: 2px solid rgba(25, 118, 210, 0.2);
    border-radius: 20px;
    font-size: 1.1rem;
    background: white;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    position: relative;
}

.quick-search-input:focus {
    outline: none;
    border-color: #1976d2;
    box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.1), 0 8px 25px rgba(25, 118, 210, 0.15);
    transform: translateY(-3px) scale(1.02);
}

.quick-search-input:not(:placeholder-shown) {
    border-color: #1976d2;
    box-shadow: 0 6px 20px rgba(25, 118, 210, 0.1);
}

.quick-search-input-wrapper .search-icon {
    position: absolute;
    left: 1.2rem;
    top: 50%;
    transform: translateY(-50%);
    color: #1976d2;
    font-size: 1.2rem;
}

.clear-quick-search-btn {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(244, 67, 54, 0.1);
    border: none;
    color: #f44336;
    padding: 0.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0;
}

.clear-quick-search-btn:hover {
    background: rgba(244, 67, 54, 0.2);
    transform: translateY(-50%) scale(1.1);
}

.quick-search-input:not(:placeholder-shown) + .clear-quick-search-btn {
    opacity: 1;
}

.quick-search-hint {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    font-size: 0.9rem;
    padding: 0.8rem 1rem;
    background: rgba(25, 118, 210, 0.05);
    border-radius: 8px;
    border-left: 3px solid #1976d2;
}

.quick-search-hint i {
    color: #1976d2;
}

/* نتائج البحث السريع */
.quick-search-results {
    background: white;
    border-radius: 16px;
    border: 1px solid rgba(25, 118, 210, 0.1);
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.quick-results-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, rgba(25, 118, 210, 0.05), rgba(25, 118, 210, 0.02));
    border-bottom: 1px solid rgba(25, 118, 210, 0.1);
}

.quick-results-header h4 {
    margin: 0;
    color: #1976d2;
    font-weight: 600;
}

.quick-results-count {
    background: #1976d2;
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.quick-results-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 0.5rem;
}

.quick-result-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.2rem;
    margin-bottom: 0.8rem;
    background: linear-gradient(135deg, #f8f9fa, #fff);
    border: 1px solid rgba(25, 118, 210, 0.1);
    border-radius: 16px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.quick-result-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(25, 118, 210, 0.05), transparent);
    transition: left 0.6s ease;
}

.quick-result-item:hover::before {
    left: 100%;
}

.quick-result-item:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 12px 30px rgba(25, 118, 210, 0.2);
    border-color: rgba(25, 118, 210, 0.4);
}

.quick-result-item:active {
    transform: translateY(-2px) scale(1.01);
}

/* تحسينات التنقل في البحث */
.quick-result-item.selected,
.search-result-item.selected {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-color: #667eea;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
    transform: translateY(-2px) scale(1.02);
    animation: pulse 2s infinite;
}

.quick-result-item.selected::before,
.search-result-item.selected::before {
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
}

/* تحسينات إضافية للتفاعل السلس */
.search-interface-modal {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-interface-modal:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 30px 90px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.2);
}

/* تحسينات للأزرار */
.search-type-btn:active {
    transform: translateY(0) scale(0.98);
}

.quick-result-btn:active,
.result-action-btn:active {
    transform: translateY(0) scale(0.95);
}

/* تحسينات للتنقل */
.search-content {
    scroll-behavior: smooth;
}

/* تحسينات للتفاعل مع الأجهزة المحمولة */
@media (max-width: 768px) {
    .search-interface-modal {
        margin: 0.5rem;
        border-radius: 20px;
        width: 98%;
        max-height: 90vh;
    }
    
    .search-type-btn {
        padding: 0.8rem 0.8rem;
        font-size: 0.85rem;
    }
    
    .quick-result-item,
    .search-result-item {
        margin-bottom: 0.5rem;
        padding: 0.8rem;
    }
    
    .quick-result-btn,
    .result-action-btn {
        padding: 0.4rem 0.6rem;
        font-size: 0.75rem;
    }
    
    .search-content {
        padding: 1rem;
        max-height: 70vh;
    }
    
    .quick-search-results,
    .advanced-search-results-area {
        max-height: 50vh;
    }
    
    .quick-results-list,
    .results-list {
        max-height: 45vh;
    }
}

/* تحسينات إضافية للتفاعل */
.search-result-item,
.quick-result-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.search-result-item:hover,
.quick-result-item:hover {
    transform: translateY(-2px) scale(1.01);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.search-result-item:active,
.quick-result-item:active {
    transform: translateY(0) scale(0.98);
}

/* تحسين منطقة الأزرار */
.quick-result-actions,
.result-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-end;
    min-width: 120px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.quick-result-item:hover .quick-result-actions,
.search-result-item:hover .result-actions {
    opacity: 1;
}

/* تحسين عرض النتائج */
.result-content,
.quick-result-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: 1rem;
}

.result-text,
.quick-result-text {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
    min-width: 0;
}

.result-number,
.quick-result-number {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.result-title,
.quick-result-text {
    font-weight: 500;
    line-height: 1.4;
    word-break: break-word;
}

.result-meta {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 0.5rem;
}

.result-category,
.result-priority,
.result-status {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    padding: 0.3rem 0.6rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.3rem;
    white-space: nowrap;
}

/* تحسين عرض النتائج */
.result-content,
.quick-result-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.result-text,
.quick-result-text {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.result-number,
.quick-result-number {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.result-title,
.quick-result-text {
    font-weight: 500;
    line-height: 1.4;
}

.result-meta {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 0.5rem;
}

.result-category,
.result-priority,
.result-status {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    padding: 0.3rem 0.6rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

/* تحسينات للوضع الليلي */
[data-theme="dark"] .search-interface-modal {
    background: linear-gradient(135deg, #1a202c 80%, #2d3748 100%);
    border-color: rgba(102, 126, 234, 0.3);
}

[data-theme="dark"] .search-type-selector {
    background: rgba(102, 126, 234, 0.1);
    border-bottom-color: rgba(102, 126, 234, 0.2);
}

[data-theme="dark"] .quick-result-item.selected,
[data-theme="dark"] .search-result-item.selected {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
    border-color: #667eea;
}

/* تحسينات الفلاتر */
.filter-btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.filter-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 50%;
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
}

.filter-btn:hover::before {
    width: 200px;
    height: 200px;
}

.filter-btn.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

/* تحسينات خيارات البحث */
.search-option {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.search-option:hover {
    transform: translateX(-5px);
}

.search-option input[type="checkbox"] {
    transition: all 0.3s ease;
}

.search-option input[type="checkbox"]:checked {
    transform: scale(1.2);
}

/* تحسينات أزرار البحث المتقدم */
.result-action-btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.result-action-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
}

.result-action-btn:hover::before {
    width: 200px;
    height: 200px;
}

.result-action-btn:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.result-action-btn.complete:hover {
    background: linear-gradient(135deg, #4caf50, #388e3c);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
}

.result-action-btn.edit:hover {
    background: linear-gradient(135deg, #ffc107, #f57c00);
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.3);
}

.result-action-btn.delete:hover {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.3);
}

.quick-result-content {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.quick-result-number {
    background: #1976d2;
    color: white;
    padding: 0.3rem 0.6rem;
    border-radius: 8px;
    font-size: 0.8rem;
    font-weight: 600;
    min-width: 2rem;
    text-align: center;
}

.quick-result-text {
    flex: 1;
    font-weight: 500;
    color: #333;
}

.quick-result-text mark {
    background: rgba(255, 193, 7, 0.3);
    color: inherit;
    padding: 0.1rem 0.2rem;
    border-radius: 3px;
    font-weight: 600;
}

.quick-result-actions {
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.quick-result-item:hover .quick-result-actions {
    opacity: 1;
}

.quick-result-btn {
    background: linear-gradient(135deg, rgba(25, 118, 210, 0.1), rgba(25, 118, 210, 0.05));
    border: 1px solid rgba(25, 118, 210, 0.2);
    color: #1976d2;
    padding: 0.6rem 1rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.8rem;
    font-weight: 500;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 0.4rem;
    min-width: 40px;
    justify-content: center;
}

.quick-result-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(25, 118, 210, 0.2);
    border-radius: 50%;
    transition: all 0.4s ease;
    transform: translate(-50%, -50%);
}

.quick-result-btn:hover::before {
    width: 200px;
    height: 200px;
}

.quick-result-btn:hover {
    background: linear-gradient(135deg, #1976d2, #1565c0);
    color: white;
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 20px rgba(25, 118, 210, 0.3);
}

.quick-result-btn:active {
    transform: translateY(0) scale(1.02);
}

.quick-result-btn.complete:hover {
    background: linear-gradient(135deg, #4caf50, #388e3c);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
}

.quick-result-btn.edit:hover {
    background: linear-gradient(135deg, #ffc107, #f57c00);
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.3);
}

.quick-result-btn.delete:hover {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.3);
}

/* البحث المتقدم */
.advanced-search-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.advanced-search-header {
    margin-bottom: 1.5rem;
}

.advanced-search-title {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    color: #1976d2;
}

.advanced-search-title i {
    font-size: 1.3rem;
}

.advanced-search-title h4 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.advanced-search-input-area {
    margin-bottom: 1.5rem;
}

.advanced-search-input {
    width: 100%;
    padding: 1rem 3rem 1rem 3rem;
    border: 2px solid rgba(25, 118, 210, 0.2);
    border-radius: 12px;
    font-size: 1rem;
    background: white;
    transition: all 0.3s ease;
}

.advanced-search-input:focus {
    outline: none;
    border-color: #1976d2;
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.clear-advanced-search-btn {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(244, 67, 54, 0.1);
    border: none;
    color: #f44336;
    padding: 0.4rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0;
}

.clear-advanced-search-btn:hover {
    background: rgba(244, 67, 54, 0.2);
}

.advanced-search-input:not(:placeholder-shown) ~ .clear-advanced-search-btn {
    opacity: 1;
}

.advanced-search-results-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 300px;
}

/* الوضع الليلي */
[data-theme="dark"] .search-interface-modal {
    background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
    border-color: rgba(25, 118, 210, 0.3);
}

[data-theme="dark"] .search-type-selector {
    background: rgba(25, 118, 210, 0.1);
    border-bottom-color: rgba(25, 118, 210, 0.2);
}

[data-theme="dark"] .search-type-btn {
    color: #ccc;
}

[data-theme="dark"] .search-type-btn:hover {
    background: rgba(25, 118, 210, 0.2);
    color: #90caf9;
}

[data-theme="dark"] .quick-search-input,
[data-theme="dark"] .advanced-search-input {
    background: #2a2a2a;
    color: #e0e0e0;
    border-color: #444;
}

[data-theme="dark"] .quick-search-input:focus,
[data-theme="dark"] .advanced-search-input:focus {
    border-color: #1976d2;
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.2);
}

[data-theme="dark"] .quick-search-hint {
    background: rgba(25, 118, 210, 0.1);
    color: #ccc;
}

[data-theme="dark"] .quick-search-results {
    background: #2a2a2a;
    border-color: #444;
}

[data-theme="dark"] .quick-results-header {
    background: linear-gradient(135deg, rgba(25, 118, 210, 0.1), rgba(25, 118, 210, 0.05));
    border-bottom-color: #444;
}

[data-theme="dark"] .quick-result-item {
    background: linear-gradient(135deg, #333, #2a2a2a);
    border-color: #444;
}

[data-theme="dark"] .quick-result-text {
    color: #e0e0e0;
}

[data-theme="dark"] .quick-result-btn {
    background: linear-gradient(135deg, rgba(25, 118, 210, 0.2), rgba(25, 118, 210, 0.1));
    border-color: rgba(25, 118, 210, 0.3);
    color: #90caf9;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .search-interface-modal {
        width: 95%;
        max-height: 95vh;
        border-radius: 16px;
    }
    
    .search-interface-header {
        padding: 1rem 1.5rem;
    }
    
    .search-type-selector {
        padding: 0.8rem 1.5rem;
    }
    
    .search-type-btn {
        padding: 0.8rem 1rem;
        font-size: 0.9rem;
    }
    
    .search-content {
        padding: 1.5rem;
    }
    
    .quick-search-input,
    .advanced-search-input {
        padding: 1rem 2.5rem 1rem 2.5rem;
        font-size: 1rem;
    }
}

/* تنسيق الوضع الداكن لشريط إدخال المهام */
[data-theme="dark"] .task-input-bar {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.1));
  border-color: rgba(102, 126, 234, 0.25);
  box-shadow: 
      0 8px 32px rgba(102, 126, 234, 0.2),
      0 2px 8px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .task-input-bar:hover {
  border-color: rgba(102, 126, 234, 0.35);
  box-shadow: 
      0 12px 40px rgba(102, 126, 234, 0.25),
      0 4px 12px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

[data-theme="dark"] .task-input-bar .task-input {
  background: rgba(45, 55, 72, 0.9);
  color: #e2e8f0;
  border-color: rgba(102, 126, 234, 0.3);
  box-shadow: 
      0 2px 8px rgba(102, 126, 234, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .task-input-bar .task-input:focus {
  background: rgba(45, 55, 72, 0.95);
  border-color: rgba(102, 126, 234, 0.5);
  box-shadow: 
      0 4px 16px rgba(102, 126, 234, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

[data-theme="dark"] .task-input-bar .task-input::placeholder {
  color: rgba(102, 126, 234, 0.7);
}

[data-theme="dark"] .task-input-bar .task-category-select,
[data-theme="dark"] .task-input-bar .task-priority-select {
  background: rgba(45, 55, 72, 0.9);
  color: #e2e8f0;
  border-color: rgba(102, 126, 234, 0.3);
  box-shadow: 
      0 2px 8px rgba(102, 126, 234, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

[data-theme="dark"] .task-input-bar .task-category-select:focus,
[data-theme="dark"] .task-input-bar .task-priority-select:focus {
  background: rgba(45, 55, 72, 0.95);
  border-color: rgba(102, 126, 234, 0.5);
  box-shadow: 
      0 4px 16px rgba(102, 126, 234, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

[data-theme="dark"] .task-input-bar .task-category-select:hover,
[data-theme="dark"] .task-input-bar .task-priority-select:hover {
  background: rgba(45, 55, 72, 0.95);
  border-color: rgba(102, 126, 234, 0.4);
  box-shadow: 
      0 3px 12px rgba(102, 126, 234, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* تنسيق القوائم المنسدلة */
.task-input-bar .task-category-select option,
.task-input-bar .task-priority-select option {
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  padding: 0.8rem 1rem;
  font-size: 1rem;
  font-family: 'Cairo', sans-serif;
  font-weight: 500;
  border: none;
  border-radius: 8px;
  margin: 2px;
  transition: all 0.2s ease;
  text-align: center;
  line-height: 1.2;
  min-height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.task-input-bar .task-category-select option:hover,
.task-input-bar .task-priority-select option:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  color: #667eea;
  transform: translateX(4px);
  text-align: center;
  line-height: 1.2;
  min-height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.task-input-bar .task-category-select option:checked,
.task-input-bar .task-priority-select option:checked {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  text-align: center;
  line-height: 1.2;
  min-height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* تنسيق الوضع الداكن للقوائم المنسدلة */
[data-theme="dark"] .task-input-bar .task-category-select option,
[data-theme="dark"] .task-input-bar .task-priority-select option {
  background: rgba(45, 55, 72, 0.95);
  color: #e2e8f0;
  border: 1px solid rgba(102, 126, 234, 0.2);
  text-align: center;
  line-height: 1.2;
  min-height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

[data-theme="dark"] .task-input-bar .task-category-select option:hover,
[data-theme="dark"] .task-input-bar .task-priority-select option:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
  color: #fff;
  border-color: rgba(102, 126, 234, 0.4);
  text-align: center;
  line-height: 1.2;
  min-height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

[data-theme="dark"] .task-input-bar .task-category-select option:checked,
[data-theme="dark"] .task-input-bar .task-priority-select option:checked {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: rgba(102, 126, 234, 0.6);
  text-align: center;
  line-height: 1.2;
  min-height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

[data-theme="dark"] .search-task-btn {
  background: rgba(45, 55, 72, 0.9);
  color: #667eea;
  border-color: rgba(102, 126, 234, 0.3);
  box-shadow: 
      0 2px 8px rgba(102, 126, 234, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .search-task-btn:hover,
[data-theme="dark"] .search-task-btn:focus {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  box-shadow: 
      0 8px 25px rgba(102, 126, 234, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* تحسينات إضافية لمنع ظهور الأجزاء البيضاء */
html {
    background: var(--bg-color) !important;
    min-height: 100vh !important;
}

body {
    background: var(--bg-color) !important;
    min-height: 100vh !important;
    position: relative !important;
}

/* منع التمرير الأفقي نهائياً */
html, body {
    overflow-x: hidden !important;
    width: 100% !important;
    max-width: 100vw !important;
}

/* إصلاح مشاكل التمرير على جميع الأجهزة */
@media (max-width: 1200px) {
    html, body {
        overflow-x: hidden !important;
        -webkit-overflow-scrolling: touch;
        overscroll-behavior: none;
    }
}

/* إصلاح مشاكل التمرير على الأجهزة المحمولة */
@media (max-width: 768px) {
    html, body {
        overflow-x: hidden !important;
        -webkit-overflow-scrolling: touch;
        overscroll-behavior: none;
        width: 100vw !important;
    }
}

/* إصلاح مشاكل التمرير على الشاشات الصغيرة جداً */
@media (max-width: 480px) {
    html, body {
        overflow-x: hidden !important;
        width: 100vw !important;
        max-width: 100vw !important;
    }
}

/* ========== تحسينات القائمة الجانبية ========== */
.quick-stats {
    display: flex;
    flex-direction: column;
    gap: 0.6rem;
    margin-top: 1rem;
    padding: 0.5rem;
    background: rgba(var(--primary-color-rgb, 25, 118, 210), 0.05);
    border-radius: 12px;
    border: 1px solid rgba(var(--primary-color-rgb, 25, 118, 210), 0.1);
}

.stat-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.8rem;
    padding: 0.8rem 1rem;
    background: var(--card-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary-color);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateX(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.stat-item:hover::before {
    transform: scaleY(1);
}

.stat-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(var(--primary-color-rgb, 25, 118, 210), 0.1);
    border-radius: 50%;
    flex-shrink: 0;
}

.stat-item span {
    font-size: 0.9rem;
    color: var(--text-color);
    font-weight: 500;
    flex: 1;
}

.stat-item span span {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1rem;
    margin-left: 0.5rem;
}

/* تأثيرات خاصة لكل نوع إحصائية */
.stat-item:nth-child(1) i { /* المجموع */
    background: rgba(33, 150, 243, 0.1);
    color: #2196f3;
}

.stat-item:nth-child(2) i { /* المكتملة */
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.stat-item:nth-child(3) i { /* المعلقة */
    background: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

.stat-item:nth-child(4) i { /* نسبة الإنجاز */
    background: rgba(156, 39, 176, 0.1);
    color: #9c27b0;
}

/* تحسين للوضع الليلي */
[data-theme="dark"] .quick-stats {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .stat-item {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .stat-item:hover {
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* تحسين أزرار القائمة الجانبية الجديدة */
#clearCompletedBtn {
    color: #ff5722 !important;
}

#clearCompletedBtn:hover {
    background: rgba(255, 87, 34, 0.1) !important;
    border-color: #ff5722 !important;
}

#backupBtn {
    color: #4caf50 !important;
}

#backupBtn:hover {
    background: rgba(76, 175, 80, 0.1) !important;
    border-color: #4caf50 !important;
}

#exportDataBtn {
    color: #2196f3 !important;
}

#exportDataBtn:hover {
    background: rgba(33, 150, 243, 0.1) !important;
    border-color: #2196f3 !important;
}

#importDataBtn {
    color: #9c27b0 !important;
}

#importDataBtn:hover {
    background: rgba(156, 39, 176, 0.1) !important;
    border-color: #9c27b0 !important;
}

/* ========== القوائم المنسدلة في القائمة الجانبية ========== */
.dropdown-container {
    position: relative;
    width: 100%;
}

.dropdown-btn {
    width: 100%;
    display: flex !important;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

.dropdown-arrow {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
    margin-left: 0.5rem;
}

.dropdown-container.active .dropdown-arrow {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    overflow: hidden;
    margin-top: 0.5rem;
}

.dropdown-container.active .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    width: 100%;
    padding: 0.8rem 1rem;
    background: transparent;
    border: none;
    text-align: right;
    color: var(--text-color);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    border-bottom: 1px solid var(--border-color);
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(-3px);
}

.dropdown-item i {
    font-size: 1rem;
    width: 20px;
    text-align: center;
}

/* ألوان مخصصة لكل نوع ملف */
.dropdown-item[data-format="json"]:hover {
    background: #4caf50;
}

.dropdown-item[data-format="csv"]:hover {
    background: #ff9800;
}

.dropdown-item[data-format="txt"]:hover {
    background: #2196f3;
}

/* تحسين للوضع الليلي */
[data-theme="dark"] .dropdown-menu {
    background: #1e1e1e;
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .dropdown-item {
    color: #e0e0e0;
    border-color: rgba(255, 255, 255, 0.1);
}

/* تأثيرات responsive */
@media (max-width: 768px) {
    .dropdown-menu {
        position: fixed;
        left: 1rem;
        right: 1rem;
        top: auto;
        bottom: 1rem;
        margin-top: 0;
        border-radius: 12px;
    }

    .dropdown-item {
        padding: 1rem;
        font-size: 1rem;
    }
}

/* ========== زر التعديل المتقدم ========== */
.edit-dropdown-container {
    position: relative;
    display: inline-block;
}

.advanced-edit-btn {
    display: flex !important;
    align-items: center;
    gap: 0.3rem;
    padding: 0.5rem 0.8rem !important;
    background: linear-gradient(135deg, #2196f3, #1976d2) !important;
    border: none !important;
    border-radius: 8px !important;
    color: white !important;
    font-size: 0.85rem !important;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.advanced-edit-btn:hover {
    background: linear-gradient(135deg, #1976d2, #1565c0) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
}

.edit-dropdown-arrow {
    font-size: 0.7rem !important;
    transition: transform 0.3s ease;
}

.edit-dropdown-container.active .edit-dropdown-arrow {
    transform: rotate(180deg);
}

.edit-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px) scale(0.95);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    margin-top: 0.5rem;
    min-width: 200px;
}

.edit-dropdown-container.active .edit-dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.edit-dropdown-item {
    width: 100%;
    padding: 0.8rem 1rem;
    background: transparent;
    border: none;
    text-align: right;
    color: var(--text-color);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    border-bottom: 1px solid var(--border-color);
}

.edit-dropdown-item:last-child {
    border-bottom: none;
}

.edit-dropdown-item:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(-3px);
}

.edit-dropdown-item i {
    font-size: 1rem;
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

/* ألوان مخصصة لكل إجراء */
.edit-dropdown-item[data-action="quick-edit"]:hover {
    background: #4caf50;
}

.edit-dropdown-item[data-action="advanced-edit"]:hover {
    background: #2196f3;
}

.edit-dropdown-item[data-action="duplicate"]:hover {
    background: #ff9800;
}

.edit-dropdown-item[data-action="priority"]:hover {
    background: #9c27b0;
}

.edit-dropdown-item[data-action="category"]:hover {
    background: #607d8b;
}

.edit-dropdown-item[data-action="due-date"]:hover {
    background: #795548;
}

/* تحسين للوضع الليلي */
[data-theme="dark"] .edit-dropdown-menu {
    background: #1e1e1e;
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .edit-dropdown-item {
    color: #e0e0e0;
    border-color: rgba(255, 255, 255, 0.1);
}

/* حقل التعديل السريع */
.quick-edit-input {
    width: 100%;
    padding: 0.5rem;
    border: 2px solid var(--primary-color);
    border-radius: 6px;
    font-size: 1rem;
    font-family: inherit;
    background: var(--card-bg);
    color: var(--text-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

/* تحسين responsive للزر المتقدم */
@media (max-width: 768px) {
    .edit-dropdown-menu {
        position: fixed;
        left: 1rem;
        right: 1rem;
        top: auto;
        bottom: 1rem;
        margin-top: 0;
        border-radius: 16px;
        min-width: auto;
    }

    .edit-dropdown-item {
        padding: 1rem;
        font-size: 1rem;
    }

    .advanced-edit-btn {
        padding: 0.6rem 1rem !important;
        font-size: 0.9rem !important;
    }
}

/* ========== معلومات المستخدم في القائمة الجانبية ========== */
.user-profile {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
}

.user-profile:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  flex-shrink: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.user-avatar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.user-profile:hover .user-avatar::before {
  transform: translateX(100%);
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: 600;
  font-size: 1rem;
  color: var(--text-color);
  margin-bottom: 0.2rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-email {
  font-size: 0.8rem;
  color: var(--text-light);
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.user-action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.user-action-btn:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.user-action-btn:active {
  transform: translateY(0);
}

/* تحسين للوضع الليلي */
[data-theme="dark"] .user-profile {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .user-profile:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));
}

[data-theme="dark"] .user-name {
  color: #e0e0e0;
}

[data-theme="dark"] .user-email {
  color: #b0b0b0;
}

[data-theme="dark"] .user-action-btn {
  background: rgba(255, 255, 255, 0.05);
  color: #e0e0e0;
}

[data-theme="dark"] .user-action-btn:hover {
  background: var(--primary-color);
  color: white;
}

/* تحسين responsive */
@media (max-width: 768px) {
  .user-profile {
    padding: 0.8rem;
    gap: 0.8rem;
  }

  .user-avatar {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .user-name {
    font-size: 0.9rem;
  }

  .user-email {
    font-size: 0.75rem;
  }

  .user-action-btn {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }
}

/* ========== نافذة تعديل المهمة المحسنة ========== */
.edit-task-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.edit-task-modal-overlay.show {
    opacity: 1;
}

.edit-task-modal-box {
    background: var(--card-bg);
    border-radius: 16px;
    padding: 2rem;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s ease;
    position: relative;
}

.edit-task-modal-overlay.show .edit-task-modal-box {
    transform: scale(1) translateY(0);
}

.edit-task-modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: transparent;
    border: none;
    font-size: 1.5rem;
    color: var(--text-light);
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.edit-task-modal-close:hover {
    background: var(--danger-color);
    color: white;
    transform: rotate(90deg);
}

.edit-task-title {
    color: var(--text-color);
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.edit-task-title i {
    color: var(--primary-color);
}

.edit-task-form {
    display: flex;
    flex-direction: column;
    gap: 1.2rem;
}

.edit-task-field {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.edit-task-field label {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.95rem;
}

.edit-task-field input,
.edit-task-field select,
.edit-task-field textarea {
    padding: 0.8rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    font-family: inherit;
    background: var(--card-bg);
    color: var(--text-color);
    transition: all 0.3s ease;
}

.edit-task-field input:focus,
.edit-task-field select:focus,
.edit-task-field textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.edit-task-field textarea {
    resize: vertical;
    min-height: 80px;
}

.edit-task-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.edit-task-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.edit-task-actions .btn {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.edit-task-actions .btn-primary {
    background: var(--primary-color);
    color: white;
}

.edit-task-actions .btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(25, 118, 210, 0.3);
}

.edit-task-actions .btn-secondary {
    background: var(--border-color);
    color: var(--text-color);
}

.edit-task-actions .btn-secondary:hover {
    background: var(--text-light);
    color: white;
    transform: translateY(-2px);
}

/* ========== إصلاح ألوان النصوص في الوضع الليلي والنهاري ========== */
.task-text {
    color: var(--text-color) !important;
}

.edit-task-input {
    color: var(--text-color) !important;
    background: var(--card-bg) !important;
    border: 2px solid var(--border-color) !important;
}

.edit-task-input:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1) !important;
}

/* تحسين ألوان النصوص في صناديق المهام */
.task-item .task-text,
.task-item .task-meta span,
.task-item .task-category,
.task-item .task-priority,
.task-item .task-date {
    color: var(--text-color);
}

[data-theme="dark"] .task-item .task-text,
[data-theme="dark"] .task-item .task-meta span,
[data-theme="dark"] .task-item .task-category,
[data-theme="dark"] .task-item .task-priority,
[data-theme="dark"] .task-item .task-date {
    color: #e0e0e0;
}

[data-theme="light"] .task-item .task-text,
[data-theme="light"] .task-item .task-meta span,
[data-theme="light"] .task-item .task-category,
[data-theme="light"] .task-item .task-priority,
[data-theme="light"] .task-item .task-date {
    color: #222;
}

/* تحسين ألوان حقول الإدخال */
input[type="text"],
input[type="date"],
textarea,
select {
    color: var(--text-color) !important;
    background: var(--card-bg) !important;
}

[data-theme="dark"] input[type="text"],
[data-theme="dark"] input[type="date"],
[data-theme="dark"] textarea,
[data-theme="dark"] select {
    color: #e0e0e0 !important;
    background: #1e1e1e !important;
}

[data-theme="light"] input[type="text"],
[data-theme="light"] input[type="date"],
[data-theme="light"] textarea,
[data-theme="light"] select {
    color: #222 !important;
    background: #fff !important;
}

/* تحسين responsive للنافذة */
@media (max-width: 768px) {
    .edit-task-modal-box {
        width: 95%;
        padding: 1.5rem;
        margin: 1rem;
    }

    .edit-task-row {
        grid-template-columns: 1fr;
    }

    .edit-task-actions {
        flex-direction: column;
    }

    .edit-task-actions .btn {
        width: 100%;
        justify-content: center;
    }
}
