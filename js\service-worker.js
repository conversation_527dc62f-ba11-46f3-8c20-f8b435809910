// TDL V1.0 - Service Worker for Offline Functionality

const CACHE_NAME = 'tdl-v1.1-cache';
const APP_VERSION = '1.1.0';

// تحديد الملفات الأساسية التي سيتم تخزينها للعمل بدون اتصال
const CORE_ASSETS = [
  '/',
  '/index.html',
  '/login.html',
  '/css/style.css',
  '/css/optimized.css',
  '/js/app.js',
  '/js/auth.js',
  '/js/utils.js',
  '/js/optimized.js',
  '/js/db.js',
  '/js/offline.js',
  '/js/sw-register.js',
  '/manifest.json',
  '/img/icons/icon-192x192.svg',
  '/img/icons/icon-512x512.svg',
  '/img/icons/add-task.svg',
  '/img/icons/important-tasks.svg',
  'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css',
  'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap'
];

// تثبيت Service Worker وتخزين الملفات الأساسية
self.addEventListener('install', (event) => {
  console.log('[Service Worker] تثبيت');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('[Service Worker] تخزين الملفات الأساسية');
        return cache.addAll(CORE_ASSETS);
      })
      .then(() => self.skipWaiting())
  );
});

// تنشيط Service Worker وحذف التخزينات القديمة
self.addEventListener('activate', (event) => {
  console.log('[Service Worker] تنشيط');
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('[Service Worker] حذف التخزين القديم:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => self.clients.claim())
  );
});

// استراتيجية التخزين: الشبكة أولاً مع التخزين المؤقت كاحتياطي
self.addEventListener('fetch', (event) => {
  // تجاهل طلبات Chrome Extension وطلبات التحليلات
  if (
    event.request.url.startsWith('chrome-extension://') ||
    event.request.url.includes('analytics') ||
    event.request.url.includes('googletagmanager')
  ) {
    return;
  }

  // استراتيجية مختلفة للملفات الأساسية (Cache First)
  const isCoreAsset = CORE_ASSETS.some(asset => 
    event.request.url.endsWith(asset) || 
    event.request.url.includes(asset)
  );

  if (isCoreAsset) {
    event.respondWith(
      caches.match(event.request).then(cachedResponse => {
        // إرجاع النسخة المخزنة إذا وجدت
        if (cachedResponse) {
          // تحديث التخزين المؤقت في الخلفية
          fetch(event.request).then(response => {
            if (response.ok) {
              caches.open(CACHE_NAME).then(cache => {
                cache.put(event.request, response.clone());
              });
            }
          }).catch(() => console.log('فشل تحديث التخزين المؤقت للملف الأساسي'));
          
          return cachedResponse;
        }
        
        // إذا لم تكن موجودة في التخزين المؤقت، جلبها من الشبكة وتخزينها
        return fetch(event.request).then(response => {
          if (!response || response.status !== 200) {
            return response;
          }
          
          caches.open(CACHE_NAME).then(cache => {
            cache.put(event.request, response.clone());
          });
          
          return response.clone();
        });
      })
    );
  } else {
    // استراتيجية Network First للملفات الأخرى
    event.respondWith(
      fetch(event.request)
        .then((response) => {
          // تخزين النسخة الجديدة في التخزين المؤقت
          if (event.request.method === 'GET' && response.ok) {
            const responseClone = response.clone();
            caches.open(CACHE_NAME)
              .then((cache) => {
                cache.put(event.request, responseClone);
              });
          }
          return response;
        })
        .catch(() => {
          // استخدام النسخة المخزنة في حالة عدم الاتصال
          return caches.match(event.request)
            .then((cachedResponse) => {
              if (cachedResponse) {
                return cachedResponse;
              }
              
              // إذا كان الطلب لصفحة HTML، قم بإرجاع صفحة الوضع غير المتصل
              if (event.request.headers.get('accept')?.includes('text/html')) {
                return caches.match('/index.html');
              }
              
              // إذا لم يتم العثور على الملف في التخزين المؤقت
              return new Response('غير متوفر حالياً بدون اتصال بالإنترنت', {
                status: 503,
                statusText: 'Service Unavailable',
                headers: new Headers({
                  'Content-Type': 'text/plain'
                })
              });
            });
        })
    );
  }
});

// التعامل مع رسائل من الصفحة الرئيسية
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});

// إرسال إشعار للمستخدم عند تغيير حالة الاتصال
self.addEventListener('sync', (event) => {
  if (event.tag === 'sync-tasks') {
    event.waitUntil(
      self.clients.matchAll().then(clients => {
        clients.forEach(client => {
          client.postMessage({
            type: 'SYNC_COMPLETE',
            message: 'تمت مزامنة المهام بنجاح'
          });
        });
      })
    );
  }
});

// إضافة دعم للإشعارات
self.addEventListener('push', (event) => {
  let notificationData = {};
  
  try {
    notificationData = event.data.json();
  } catch (e) {
    notificationData = {
      title: 'إشعار جديد',
      body: event.data ? event.data.text() : 'لا توجد تفاصيل'
    };
  }
  
  const options = {
    body: notificationData.body || 'تم تحديث قائمة المهام الخاصة بك',
    icon: '/img/icons/icon-192x192.svg',
    badge: '/img/icons/icon-192x192.svg',
    data: notificationData.data || {},
    dir: 'rtl',
    vibrate: [100, 50, 100],
    actions: [
      {
        action: 'open',
        title: 'فتح التطبيق'
      },
      {
        action: 'close',
        title: 'إغلاق'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification(notificationData.title || 'قائمة المهام', options)
  );
});

// التعامل مع النقر على الإشعارات
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  if (event.action === 'close') {
    return;
  }
  
  event.waitUntil(
    self.clients.matchAll({ type: 'window' }).then(clientList => {
      // إذا كان التطبيق مفتوحاً، قم بالتركيز عليه
      for (const client of clientList) {
        if (client.url.includes(self.location.origin) && 'focus' in client) {
          return client.focus();
        }
      }
      
      // إذا لم يكن التطبيق مفتوحاً، قم بفتحه
      if (self.clients.openWindow) {
        return self.clients.openWindow('/');
      }
    })
  );
});

// إعلام التطبيق بإصدار Service Worker
self.addEventListener('activate', (event) => {
  event.waitUntil(
    self.clients.matchAll().then(clients => {
      clients.forEach(client => {
        client.postMessage({
          type: 'SW_VERSION',
          version: APP_VERSION
        });
      });
    })
  );
});