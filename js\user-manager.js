// TDL V1.0 - User Management System
// نظام إدارة المستخدمين المتقدم

export class UserManager {
  constructor() {
    this.currentUser = null;
    this.users = this.loadUsers();
    this.sessionTimeout = 24 * 60 * 60 * 1000; // 24 ساعة
    this.init();
  }

  // تهيئة النظام
  init() {
    this.checkSession();
    this.setupSessionTimeout();
  }

  // تحميل المستخدمين من التخزين المحلي
  loadUsers() {
    try {
      const users = localStorage.getItem('tdl_users');
      return users ? JSON.parse(users) : {};
    } catch (error) {
      console.error('خطأ في تحميل بيانات المستخدمين:', error);
      return {};
    }
  }

  // حفظ المستخدمين في التخزين المحلي
  saveUsers() {
    try {
      const usersData = JSON.stringify(this.users);
      localStorage.setItem('tdl_users', usersData);
      console.log('💾 تم حفظ بيانات المستخدمين بنجاح، عدد المستخدمين:', Object.keys(this.users).length);
      return true;
    } catch (error) {
      console.error('❌ خطأ في حفظ بيانات المستخدمين:', error);

      // التحقق من امتلاء التخزين المحلي
      if (error.name === 'QuotaExceededError') {
        alert('مساحة التخزين ممتلئة، يرجى حذف بعض البيانات');
      }
      return false;
    }
  }

  // تشفير كلمة المرور (بسيط للتطبيق المحلي)
  hashPassword(password) {
    let hash = 0;
    for (let i = 0; i < password.length; i++) {
      const char = password.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // تحويل إلى 32bit integer
    }
    return hash.toString();
  }

  // إنشاء حساب جديد
  async register(userData) {
    console.log('🔐 UserManager: بدء عملية التسجيل...', userData);

    const { username, email, password, fullName, avatar } = userData;

    // التحقق من صحة البيانات
    console.log('🔍 التحقق من صحة البيانات...');

    if (!username || !email || !password || !fullName) {
      console.error('❌ بيانات مفقودة:', { username: !!username, email: !!email, password: !!password, fullName: !!fullName });
      throw new Error('جميع الحقول مطلوبة');
    }

    if (username.length < 3) {
      console.error('❌ اسم المستخدم قصير جداً:', username.length);
      throw new Error('اسم المستخدم يجب أن يكون 3 أحرف على الأقل');
    }

    if (password.length < 6) {
      console.error('❌ كلمة المرور قصيرة جداً:', password.length);
      throw new Error('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
    }

    if (!this.isValidEmail(email)) {
      console.error('❌ بريد إلكتروني غير صالح:', email);
      throw new Error('البريد الإلكتروني غير صالح');
    }

    // التحقق من عدم وجود المستخدم
    console.log('🔍 التحقق من توفر اسم المستخدم والبريد الإلكتروني...');

    if (this.users[username]) {
      console.error('❌ اسم المستخدم موجود بالفعل:', username);
      throw new Error('اسم المستخدم موجود بالفعل');
    }

    if (Object.values(this.users).some(user => user.email === email)) {
      console.error('❌ البريد الإلكتروني مستخدم بالفعل:', email);
      throw new Error('البريد الإلكتروني مستخدم بالفعل');
    }

    // إنشاء المستخدم الجديد
    console.log('👤 إنشاء المستخدم الجديد...');

    const newUser = {
      username,
      email,
      password: this.hashPassword(password),
      fullName,
      avatar: avatar || this.generateAvatar(fullName),
      createdAt: new Date().toISOString(),
      lastLogin: null,
      settings: this.getDefaultSettings(),
      tasks: [],
      stats: {
        totalTasks: 0,
        completedTasks: 0,
        loginCount: 0
      }
    };

    console.log('💾 حفظ المستخدم في التخزين المحلي...');
    this.users[username] = newUser;
    this.saveUsers();

    console.log('✅ تم إنشاء الحساب بنجاح!', { username, email, fullName });
    return { success: true, message: 'تم إنشاء الحساب بنجاح' };
  }

  // تسجيل الدخول
  async login(username, password) {
    if (!username || !password) {
      throw new Error('اسم المستخدم وكلمة المرور مطلوبان');
    }

    const user = this.users[username];
    if (!user) {
      throw new Error('اسم المستخدم غير موجود');
    }

    if (user.password !== this.hashPassword(password)) {
      throw new Error('كلمة المرور غير صحيحة');
    }

    // تحديث بيانات تسجيل الدخول
    user.lastLogin = new Date().toISOString();
    user.stats.loginCount++;
    this.saveUsers();

    // إنشاء جلسة
    this.currentUser = user;
    this.createSession(user);

    return { success: true, user: this.getSafeUserData(user) };
  }

  // تسجيل الخروج
  logout() {
    this.currentUser = null;
    localStorage.removeItem('tdl_session');
    localStorage.removeItem('tdl_current_user');
  }

  // إنشاء جلسة
  createSession(user) {
    const session = {
      username: user.username,
      loginTime: Date.now(),
      expiresAt: Date.now() + this.sessionTimeout
    };

    localStorage.setItem('tdl_session', JSON.stringify(session));
    localStorage.setItem('tdl_current_user', JSON.stringify(this.getSafeUserData(user)));
  }

  // فحص الجلسة
  checkSession() {
    try {
      const sessionData = localStorage.getItem('tdl_session');
      const currentUserData = localStorage.getItem('tdl_current_user');

      // إذا لم توجد جلسة، ارجع false مباشرة
      if (!sessionData || !currentUserData) {
        return false;
      }

      const session = JSON.parse(sessionData);
      const currentUser = JSON.parse(currentUserData);

      // التحقق من صحة الجلسة وانتهاء الصلاحية
      if (session && currentUser && Date.now() < session.expiresAt) {
        const user = this.users[session.username];
        if (user) {
          this.currentUser = user;
          return true;
        }
      }
    } catch (error) {
      console.error('خطأ في فحص الجلسة:', error);
    }

    // إذا فشل أي شيء، امسح الجلسة
    this.logout();
    return false;
  }

  // إعداد انتهاء الجلسة
  setupSessionTimeout() {
    setInterval(() => {
      if (!this.checkSession()) {
        window.location.reload();
      }
    }, 60000); // فحص كل دقيقة
  }

  // الحصول على بيانات المستخدم الآمنة (بدون كلمة المرور)
  getSafeUserData(user) {
    const { password, ...safeData } = user;
    return safeData;
  }

  // التحقق من صحة البريد الإلكتروني
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // إنشاء صورة رمزية افتراضية
  generateAvatar(fullName) {
    const initials = fullName.split(' ').map(name => name[0]).join('').toUpperCase();
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
    const color = colors[Math.floor(Math.random() * colors.length)];
    
    return {
      type: 'initials',
      initials,
      backgroundColor: color,
      textColor: '#FFFFFF'
    };
  }

  // الحصول على الإعدادات الافتراضية
  getDefaultSettings() {
    return {
      theme: 'light',
      language: 'ar',
      notifications: true,
      autoSave: true,
      defaultView: 'list',
      primaryColor: '#1976d2'
    };
  }

  // تحديث بيانات المستخدم
  async updateUser(updates) {
    if (!this.currentUser) {
      throw new Error('لم يتم تسجيل الدخول');
    }

    const user = this.users[this.currentUser.username];
    Object.assign(user, updates);
    this.saveUsers();

    this.currentUser = user;
    localStorage.setItem('tdl_current_user', JSON.stringify(this.getSafeUserData(user)));

    return { success: true, user: this.getSafeUserData(user) };
  }

  // الحصول على المستخدم الحالي
  getCurrentUser() {
    return this.currentUser ? this.getSafeUserData(this.currentUser) : null;
  }

  // تحديث إحصائيات المستخدم
  updateUserStats(stats) {
    if (this.currentUser) {
      Object.assign(this.currentUser.stats, stats);
      this.saveUsers();
    }
  }

  // حذف الحساب
  async deleteAccount(password) {
    if (!this.currentUser) {
      throw new Error('لم يتم تسجيل الدخول');
    }

    if (this.currentUser.password !== this.hashPassword(password)) {
      throw new Error('كلمة المرور غير صحيحة');
    }

    delete this.users[this.currentUser.username];
    this.saveUsers();
    this.logout();

    return { success: true, message: 'تم حذف الحساب بنجاح' };
  }
}

// إنشاء نسخة واحدة من مدير المستخدمين
let userManager;

try {
  userManager = new UserManager();
  console.log('✅ تم إنشاء UserManager بنجاح');
} catch (error) {
  console.error('❌ خطأ في إنشاء UserManager:', error);
  userManager = null;
}

// تصدير للاستخدام في الملفات الأخرى
export default userManager;

// إضافة إلى window للوصول العام
if (typeof window !== 'undefined') {
  window.userManager = userManager;
  console.log('✅ تم إضافة userManager إلى window');
}
