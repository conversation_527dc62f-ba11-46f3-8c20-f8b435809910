// استيراد الدوال المساعدة والمكونات الجديدة
import { showNotification, isValidText, formatDate, debounce } from './utils.js';
import { TDLDatabase } from './db.js';
import { OfflineManager } from './offline.js';

// تهيئة قاعدة البيانات وإدارة الوضع غير المتصل
const db = new TDLDatabase();
const offlineManager = new OfflineManager();

// تعريف المتغيرات العامة
let tasks = [];
let currentFilter = 'all';
let currentView = localStorage.getItem('viewMode') || 'list';
let currentCategoryFilter = 'all';
let pendingDeleteAction = null;
let searchQuery = '';
let pendingDeleteTaskId = null;
let isInitialized = false;

// العناصر الرئيسية
const elements = {
    taskInput: null,
    addTaskBtn: null,
    taskList: null,
    taskGrid: null,
    taskCount: null,
    clearAllBtn: null,
    clearCompletedBtn: null,
    themeToggle: null,
    filterButtons: null,
    viewToggleButtons: null,
    emptyState: null,
    listView: null,
    gridView: null,
    categoryInput: null,
    categoryFilterContainer: null,
    searchInput: null,
    statsBar: null,
    attachmentInput: null,
    offlineIndicator: null
};

// ========== إعدادات متقدمة وتخصيص واجهة الإعدادات ==========

const settingsModal = document.getElementById('settingsModal');
const closeSettingsModal = document.getElementById('closeSettingsModal');
const saveSettingsBtn = document.getElementById('saveSettingsBtn');
const cancelSettingsBtn = document.getElementById('cancelSettingsBtn');
const resetSettingsBtn = document.getElementById('resetSettingsBtn');
const deleteAllTasksBtn = document.getElementById('deleteAllTasksBtn');
const exportTasksBtn = document.getElementById('exportTasksBtn');
const importTasksBtn = document.getElementById('importTasksBtn');

// عناصر الإعدادات
const primaryColorSelect = document.getElementById('primaryColorSelect');
const themeSelect = document.getElementById('themeSelect');
const notifSound = document.getElementById('notifSound');
const notifPosition = document.getElementById('notifPosition');
const notifDuration = document.getElementById('notifDuration');
const confirmDelete = document.getElementById('confirmDelete');
const undoDelete = document.getElementById('undoDelete');
const defaultView = document.getElementById('defaultView');
const addCompleted = document.getElementById('addCompleted');
const instantSearch = document.getElementById('instantSearch');
const searchScope = document.getElementById('searchScope');
const fontSize = document.getElementById('fontSize');
const roundedBorders = document.getElementById('roundedBorders');
const shadows = document.getElementById('shadows');
const animations = document.getElementById('animations');
const offlineMode = document.getElementById('offlineMode');

// ========== Accordion منطق ========== //
function initAccordion() {
  document.querySelectorAll('.settings-section .accordion-toggle').forEach(btn => {
    btn.addEventListener('click', function(e) {
      const section = btn.closest('.settings-section');
      const isCollapsed = section.classList.toggle('collapsed');
      section.classList.toggle('active', !isCollapsed);
      // تدوير السهم
      btn.querySelector('i').style.transform = isCollapsed ? 'rotate(0deg)' : 'rotate(180deg)';
    });
  });
  // افتراضيًا: أول قسم مفتوح
  setTimeout(() => {
    const firstSection = document.querySelector('.settings-section');
    if (firstSection) {
      firstSection.classList.add('active');
      firstSection.classList.remove('collapsed');
      const icon = firstSection.querySelector('.accordion-toggle i');
      if (icon) icon.style.transform = 'rotate(180deg)';
    }
  }, 200);
}

// ========== تحميل/حفظ الإعدادات من/إلى IndexedDB ==========
const SETTINGS_KEY = 'tdl_settings';
let settings = {
  primaryColor: '#1976d2',
  theme: 'auto',
  notifSound: false,
  notifPosition: 'bottom',
  notifDuration: 'normal',
  confirmDelete: true,
  undoDelete: true,
  defaultView: 'list',
  addCompleted: false,
  instantSearch: true,
  searchScope: 'all',
  fontSize: 'medium',
  roundedBorders: true,
  shadows: true,
  animations: true,
  offlineMode: true
};

// تحميل الإعدادات من IndexedDB
async function loadSettings() {
  try {
    const savedSettings = await db.getSetting(SETTINGS_KEY);
    if (savedSettings) {
      settings = { ...settings, ...savedSettings };
    } else {
      // إذا لم تكن الإعدادات موجودة في IndexedDB، حاول تحميلها من localStorage
      const localSettings = localStorage.getItem(SETTINGS_KEY);
      if (localSettings) {
        settings = { ...settings, ...JSON.parse(localSettings) };
        // حفظ الإعدادات في IndexedDB
        await db.saveSetting(SETTINGS_KEY, settings);
      }
    }
    applySettings();
  } catch (error) {
    console.error('خطأ في تحميل الإعدادات:', error);
    // استخدام الإعدادات الافتراضية
    applySettings();
  }
}

// حفظ الإعدادات في IndexedDB
async function saveSettings() {
  try {
    await db.saveSetting(SETTINGS_KEY, settings);
    // حفظ نسخة احتياطية في localStorage للتوافق
    localStorage.setItem(SETTINGS_KEY, JSON.stringify(settings));
    showNotification('تم حفظ الإعدادات بنجاح', 'success');
  } catch (error) {
    console.error('خطأ في حفظ الإعدادات:', error);
    showNotification('حدث خطأ أثناء حفظ الإعدادات', 'error');
  }
}

// تطبيق الإعدادات على واجهة المستخدم
function applySettings() {
  // تطبيق الألوان والمظهر
  document.documentElement.style.setProperty('--primary-color', settings.primaryColor);
  
  // تطبيق الوضع الليلي/النهاري
  if (settings.theme === 'dark') {
    document.body.classList.add('dark-theme');
    document.documentElement.setAttribute('data-theme', 'dark');
  } else if (settings.theme === 'light') {
    document.body.classList.remove('dark-theme');
    document.documentElement.setAttribute('data-theme', 'light');
  } else {
    // وضع تلقائي يعتمد على تفضيلات النظام
    const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
    document.body.classList.toggle('dark-theme', prefersDarkMode);
    document.documentElement.setAttribute('data-theme', prefersDarkMode ? 'dark' : 'light');
  }
  
  // تطبيق حجم الخط
  document.body.classList.remove('font-small', 'font-medium', 'font-large');
  document.body.classList.add(`font-${settings.fontSize}`);
  
  // تطبيق الحدود المستديرة
  document.body.classList.toggle('rounded-borders', settings.roundedBorders);
  
  // تطبيق الظلال
  document.body.classList.toggle('with-shadows', settings.shadows);
  
  // تطبيق الرسوم المتحركة
  document.body.classList.toggle('with-animations', settings.animations);
  
  // تحديث عناصر الإعدادات في النموذج
  if (primaryColorSelect) primaryColorSelect.value = settings.primaryColor;
  if (themeSelect) themeSelect.value = settings.theme;
  if (notifSound) notifSound.checked = settings.notifSound;
  if (notifPosition) notifPosition.value = settings.notifPosition;
  if (notifDuration) notifDuration.value = settings.notifDuration;
  if (confirmDelete) confirmDelete.checked = settings.confirmDelete;
  if (undoDelete) undoDelete.checked = settings.undoDelete;
  if (defaultView) defaultView.value = settings.defaultView;
  if (addCompleted) addCompleted.checked = settings.addCompleted;
  if (instantSearch) instantSearch.checked = settings.instantSearch;
  if (searchScope) searchScope.value = settings.searchScope;
  if (fontSize) fontSize.value = settings.fontSize;
  if (roundedBorders) roundedBorders.checked = settings.roundedBorders;
  if (shadows) shadows.checked = settings.shadows;
  if (animations) animations.checked = settings.animations;
  if (offlineMode) offlineMode.checked = settings.offlineMode;
  
  // تطبيق وضع العرض الافتراضي
  if (!localStorage.getItem('viewMode')) {
    currentView = settings.defaultView;
    localStorage.setItem('viewMode', currentView);
  }
  
  // تفعيل/تعطيل وضع عدم الاتصال
  if (settings.offlineMode) {
    offlineManager.enable();
  } else {
    offlineManager.disable();
  }
}

// ========== تهيئة التطبيق ==========
async function initApp() {
  if (isInitialized) return;
  
  // إظهار شاشة التحميل بشكل واضح
  showLoadingScreen('جاري تهيئة التطبيق...');
  
  try {
    // تهيئة قاعدة البيانات (بدء العملية بشكل متوازي)
    const dbInitPromise = db.init();
    
    // تهيئة مؤشر الاتصال (لا تنتظر)
    initOfflineIndicator();
    
    // تهيئة الأحداث (لا تنتظر)
    initEvents();
    
    // تهيئة Accordion (لا تنتظر)
    initAccordion();
    
    // انتظار اكتمال تهيئة قاعدة البيانات
    await dbInitPromise;
    
    // تحميل الإعدادات
    await loadSettings();
    
    // تحديث رسالة التحميل
    updateLoadingMessage('جاري تحميل المهام...');
    
    // تحميل المهام
    await loadTasks();
    
    // تحديث واجهة المستخدم
    updateUI();
    
    // تحديث الإحصائيات
    updateStats();
    
    // تهيئة مكتملة
    isInitialized = true;
  } catch (error) {
    console.error('خطأ أثناء تهيئة التطبيق:', error);
    showNotification('حدث خطأ أثناء تهيئة التطبيق', 'error');
  } finally {
    // إخفاء شاشة التحميل
    hideLoadingScreen();
  }
}

// إظهار شاشة التحميل مع رسالة
function showLoadingScreen(message = 'جاري تحميل التطبيق...') {
  const loadingScreen = document.getElementById('loadingScreen');
  if (loadingScreen) {
    const loadingText = loadingScreen.querySelector('.loading-text');
    if (loadingText) {
      loadingText.textContent = message;
    }
    loadingScreen.classList.remove('fade-out');
    loadingScreen.classList.remove('hidden');
    loadingScreen.style.display = 'flex';
  }
}

// تحديث رسالة التحميل
function updateLoadingMessage(message) {
  const loadingScreen = document.getElementById('loadingScreen');
  if (loadingScreen) {
    const loadingText = loadingScreen.querySelector('.loading-text');
    if (loadingText) {
      loadingText.textContent = message;
    }
  }
}

// إخفاء شاشة التحميل
function hideLoadingScreen() {
  const loadingScreen = document.getElementById('loadingScreen');
  if (loadingScreen) {
    loadingScreen.classList.add('fade-out');
    setTimeout(() => {
      loadingScreen.style.display = 'none';
    }, 300); // تقليل وقت الانتظار
  }
}

// تهيئة مؤشر الاتصال
function initOfflineIndicator() {
  // إنشاء مؤشر الاتصال إذا لم يكن موجودًا
  if (!document.getElementById('offlineIndicator')) {
    const indicator = document.createElement('div');
    indicator.id = 'offlineIndicator';
    indicator.className = 'offline-indicator';
    indicator.innerHTML = '<i class="fas fa-wifi-slash"></i> أنت غير متصل بالإنترنت';
    document.body.appendChild(indicator);
    elements.offlineIndicator = indicator;
  } else {
    elements.offlineIndicator = document.getElementById('offlineIndicator');
  }
  
  // تحديث حالة الاتصال
  updateConnectionStatus();
  
  // إضافة مستمعي أحداث الاتصال
  window.addEventListener('online', updateConnectionStatus);
  window.addEventListener('offline', updateConnectionStatus);
}

// تحديث حالة الاتصال
function updateConnectionStatus() {
  if (navigator.onLine) {
    elements.offlineIndicator.classList.remove('visible');
    // مزامنة البيانات المعلقة
    if (offlineManager.hasPendingActions()) {
      syncPendingActions();
    }
  } else {
    elements.offlineIndicator.classList.add('visible');
  }
}

// مزامنة الإجراءات المعلقة
async function syncPendingActions() {
  try {
    const pendingActions = offlineManager.getPendingActions();
    if (pendingActions.length > 0) {
      showNotification('جاري مزامنة البيانات...', 'info');
      
      for (const action of pendingActions) {
        switch (action.type) {
          case 'add':
            await db.addTask(action.data);
            break;
          case 'update':
            await db.updateTask(action.data.id, action.data);
            break;
          case 'delete':
            await db.deleteTask(action.data.id);
            break;
        }
      }
      
      // مسح الإجراءات المعلقة بعد المزامنة
      offlineManager.clearPendingActions();
      
      // إعادة تحميل المهام
      await loadTasks();
      
      showNotification('تمت مزامنة البيانات بنجاح', 'success');
    }
  } catch (error) {
    console.error('خطأ في مزامنة البيانات:', error);
    showNotification('حدث خطأ أثناء مزامنة البيانات', 'error');
  }
}

// ========== تحميل المهام ==========
async function loadTasks() {
  try {
    // تحميل المهام من IndexedDB
    const startTime = performance.now();
    tasks = await db.getAllTasks();
    const loadTime = performance.now() - startTime;
    console.log(`تم تحميل ${tasks.length} مهمة في ${loadTime.toFixed(2)} مللي ثانية`);
    
    // إذا لم تكن هناك مهام في IndexedDB، حاول تحميلها من localStorage
    if (tasks.length === 0) {
      const localTasks = localStorage.getItem('tasks');
      if (localTasks) {
        try {
          const parsedTasks = JSON.parse(localTasks);
          if (Array.isArray(parsedTasks) && parsedTasks.length > 0) {
            tasks = parsedTasks;
            
            // حفظ المهام في IndexedDB بشكل متوازي
            updateLoadingMessage(`جاري نقل ${tasks.length} مهمة إلى قاعدة البيانات...`);
            
            // استخدام Promise.all لتنفيذ العمليات بشكل متوازي
            const batchSize = 10; // حجم الدفعة
            for (let i = 0; i < tasks.length; i += batchSize) {
              const batch = tasks.slice(i, i + batchSize);
              await Promise.all(batch.map(task => db.addTask(task)));
              
              // تحديث رسالة التحميل كل 10 مهام
              if (i % 10 === 0) {
                updateLoadingMessage(`تم نقل ${i} من ${tasks.length} مهمة...`);
              }
            }
          }
        } catch (parseError) {
          console.error('خطأ في تحليل المهام من localStorage:', parseError);
        }
      }
    }
    
    // تحديث واجهة المستخدم
    renderTasks();
    updateStats();
  } catch (error) {
    console.error('خطأ في تحميل المهام:', error);
    showNotification('حدث خطأ أثناء تحميل المهام', 'error');
  }
}

// ========== إضافة مهمة جديدة ==========
async function addTask() {
  const taskText = elements.taskInput.value.trim();
  const category = elements.categoryInput.value;
  const priority = document.getElementById('taskPriority').value;
  
  if (!isValidText(taskText)) {
    showNotification('الرجاء إدخال نص المهمة', 'error');
    elements.taskInput.focus();
    return;
  }
  
  const newTask = {
    id: Date.now().toString(),
    text: taskText,
    completed: settings.addCompleted,
    category: category,
    priority: priority,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    dueDate: '',
    notes: ''
  };
  
  try {
    if (navigator.onLine) {
      // إضافة المهمة إلى IndexedDB
      await db.addTask(newTask);
    } else {
      // تخزين الإجراء للمزامنة لاحقًا
      offlineManager.addPendingAction('add', newTask);
    }
    
    // إضافة المهمة إلى المصفوفة المحلية
    tasks.push(newTask);
    
    // مسح حقل الإدخال
    elements.taskInput.value = '';
    elements.taskInput.focus();
    
    // تحديث واجهة المستخدم
    renderTasks();
    updateStats();
    
    showNotification('تمت إضافة المهمة بنجاح', 'success');
  } catch (error) {
    console.error('خطأ في إضافة المهمة:', error);
    showNotification('حدث خطأ أثناء إضافة المهمة', 'error');
  }
}

// ========== تحديث مهمة ==========
async function updateTask(taskId, updates) {
  try {
    // تحديث المهمة في المصفوفة المحلية
    const taskIndex = tasks.findIndex(task => task.id === taskId);
    if (taskIndex === -1) {
      throw new Error('المهمة غير موجودة');
    }
    
    const updatedTask = { ...tasks[taskIndex], ...updates, updatedAt: new Date().toISOString() };
    tasks[taskIndex] = updatedTask;
    
    if (navigator.onLine) {
      // تحديث المهمة في IndexedDB
      await db.updateTask(taskId, updatedTask);
    } else {
      // تخزين الإجراء للمزامنة لاحقًا
      offlineManager.addPendingAction('update', updatedTask);
    }
    
    // تحديث واجهة المستخدم
    renderTasks();
    updateStats();
  } catch (error) {
    console.error('خطأ في تحديث المهمة:', error);
    showNotification('حدث خطأ أثناء تحديث المهمة', 'error');
  }
}

// ========== حذف مهمة ==========
async function deleteTask(taskId) {
  try {
    // حذف المهمة من المصفوفة المحلية
    const taskIndex = tasks.findIndex(task => task.id === taskId);
    if (taskIndex === -1) {
      throw new Error('المهمة غير موجودة');
    }
    
    const deletedTask = tasks[taskIndex];
    tasks.splice(taskIndex, 1);
    
    if (navigator.onLine) {
      // حذف المهمة من IndexedDB
      await db.deleteTask(taskId);
    } else {
      // تخزين الإجراء للمزامنة لاحقًا
      offlineManager.addPendingAction('delete', { id: taskId });
    }
    
    // تحديث واجهة المستخدم
    renderTasks();
    updateStats();
    
    // إذا كان خيار التراجع عن الحذف مفعل
    if (settings.undoDelete) {
      showUndoNotification(deletedTask);
    } else {
      showNotification('تم حذف المهمة بنجاح', 'success');
    }
  } catch (error) {
    console.error('خطأ في حذف المهمة:', error);
    showNotification('حدث خطأ أثناء حذف المهمة', 'error');
  }
}

// ========== عرض إشعار التراجع عن الحذف ==========
function showUndoNotification(deletedTask) {
  const notification = document.createElement('div');
  notification.className = 'notification undo-notification';
  notification.innerHTML = `
    <div class="notification-content">
      <i class="fas fa-trash-alt"></i>
      <span>تم حذف المهمة</span>
    </div>
    <button class="undo-btn">تراجع</button>
  `;
  
  document.body.appendChild(notification);
  
  // إظهار الإشعار
  setTimeout(() => {
    notification.classList.add('show');
  }, 10);
  
  // إزالة الإشعار بعد 5 ثوانٍ
  const timeout = setTimeout(() => {
    notification.classList.remove('show');
    setTimeout(() => {
      notification.remove();
    }, 300);
  }, 5000);
  
  // زر التراجع
  const undoBtn = notification.querySelector('.undo-btn');
  undoBtn.addEventListener('click', async () => {
    clearTimeout(timeout);
    notification.classList.remove('show');
    
    try {
      if (navigator.onLine) {
        // إعادة إضافة المهمة إلى IndexedDB
        await db.addTask(deletedTask);
      } else {
        // تخزين الإجراء للمزامنة لاحقًا
        offlineManager.addPendingAction('add', deletedTask);
      }
      
      // إعادة إضافة المهمة إلى المصفوفة المحلية
      tasks.push(deletedTask);
      
      // تحديث واجهة المستخدم
      renderTasks();
      updateStats();
      
      showNotification('تم استعادة المهمة بنجاح', 'success');
    } catch (error) {
      console.error('خطأ في استعادة المهمة:', error);
      showNotification('حدث خطأ أثناء استعادة المهمة', 'error');
    }
    
    setTimeout(() => {
      notification.remove();
    }, 300);
  });
}

// ========== تبديل حالة إكمال المهمة ==========
async function toggleTaskCompletion(taskId) {
  try {
    // البحث عن المهمة
    const taskIndex = tasks.findIndex(task => task.id === taskId);
    if (taskIndex === -1) {
      throw new Error('المهمة غير موجودة');
    }
    
    // تبديل حالة الإكمال
    const updatedTask = { 
      ...tasks[taskIndex], 
      completed: !tasks[taskIndex].completed,
      updatedAt: new Date().toISOString()
    };
    tasks[taskIndex] = updatedTask;
    
    // تحديث العنصر في واجهة المستخدم
    const taskElement = document.querySelector(`[data-task-id="${taskId}"]`);
    if (taskElement) {
      taskElement.classList.toggle('completed', updatedTask.completed);
      
      // تحديث أيقونة الإكمال
      const checkbox = taskElement.querySelector('.task-checkbox');
      if (checkbox) {
        checkbox.innerHTML = updatedTask.completed ? 
          '<i class="fas fa-check-circle"></i>' : 
          '<i class="far fa-circle"></i>';
      }
      
      // إضافة تأثير الرسوم المتحركة
      taskElement.classList.add('task-update-animation');
      setTimeout(() => {
        taskElement.classList.remove('task-update-animation');
      }, 500);
    }
    
    if (navigator.onLine) {
      // تحديث المهمة في IndexedDB
      await db.updateTask(taskId, updatedTask);
    } else {
      // تخزين الإجراء للمزامنة لاحقًا
      offlineManager.addPendingAction('update', updatedTask);
    }
    
    // تحديث الإحصائيات
    updateStats();
    
    // عرض إشعار
    const message = updatedTask.completed ? 'تم إكمال المهمة بنجاح' : 'تم إلغاء إكمال المهمة';
    showNotification(message, 'success');
  } catch (error) {
    console.error('خطأ في تبديل حالة إكمال المهمة:', error);
    showNotification('حدث خطأ أثناء تحديث حالة المهمة', 'error');
  }
}

// ========== تحرير المهمة المحسن ==========
function editTask(taskId) {
  const task = tasks.find(t => t.id === taskId);
  if (!task) {
    showNotification('المهمة غير موجودة', 'error');
    return;
  }

  // فتح نافذة التعديل
  openEditTaskModal(task);
}

// فتح نافذة تعديل المهمة
function openEditTaskModal(task) {
  const modal = document.getElementById('editTaskModal');
  const form = document.getElementById('editTaskForm');

  // ملء النموذج ببيانات المهمة
  document.getElementById('editTaskText').value = task.text;
  document.getElementById('editTaskCategory').value = task.category || 'عمل';
  document.getElementById('editTaskPriority').value = task.priority || 'medium';
  document.getElementById('editTaskCompleted').value = task.completed.toString();
  document.getElementById('editTaskDueDate').value = task.dueDate || '';
  document.getElementById('editTaskNotes').value = task.notes || '';

  // إظهار النافذة
  modal.style.display = 'flex';
  setTimeout(() => {
    modal.classList.add('show');
  }, 10);

  // التركيز على حقل النص
  document.getElementById('editTaskText').focus();

  // حفظ معرف المهمة للاستخدام عند الحفظ
  form.dataset.taskId = task.id;
}

// إغلاق نافذة تعديل المهمة
function closeEditTaskModal() {
  const modal = document.getElementById('editTaskModal');
  modal.classList.remove('show');
  setTimeout(() => {
    modal.style.display = 'none';
  }, 300);
}

// حفظ تعديلات المهمة
async function saveEditTask(event) {
  event.preventDefault();

  const form = document.getElementById('editTaskForm');
  const taskId = form.dataset.taskId;

  // جمع البيانات من النموذج
  const updatedData = {
    text: document.getElementById('editTaskText').value.trim(),
    category: document.getElementById('editTaskCategory').value,
    priority: document.getElementById('editTaskPriority').value,
    completed: document.getElementById('editTaskCompleted').value === 'true',
    dueDate: document.getElementById('editTaskDueDate').value,
    notes: document.getElementById('editTaskNotes').value.trim()
  };

  // التحقق من صحة البيانات
  if (!isValidText(updatedData.text)) {
    showNotification('الرجاء إدخال نص المهمة', 'error');
    document.getElementById('editTaskText').focus();
    return;
  }

  try {
    // تحديث المهمة
    await updateTask(taskId, updatedData);

    // إغلاق النافذة
    closeEditTaskModal();

    // إعادة عرض المهام
    renderTasks();
    updateStats();

    showNotification('تم تحديث المهمة بنجاح', 'success');
  } catch (error) {
    console.error('خطأ في تحديث المهمة:', error);
    showNotification('حدث خطأ أثناء تحديث المهمة', 'error');
  }
}

// ========== عرض تأكيد الحذف المخصص ==========
function showCustomDeleteConfirmation(taskId) {
  // إذا كان خيار تأكيد الحذف غير مفعل
  if (!settings.confirmDelete) {
    deleteTask(taskId);
    return;
  }
  
  // البحث عن المهمة
  const task = tasks.find(task => task.id === taskId);
  if (!task) return;
  
  // إنشاء نافذة التأكيد
  const modal = document.createElement('div');
  modal.className = 'custom-confirmation-modal';
  modal.innerHTML = `
    <div class="confirmation-content">
      <div class="confirmation-header">
        <i class="fas fa-trash-alt"></i>
        <h3>تأكيد الحذف</h3>
      </div>
      <p>هل أنت متأكد من حذف المهمة التالية؟</p>
      <div class="task-preview">
        <span class="task-preview-text">${task.text}</span>
      </div>
      <div class="confirmation-actions">
        <button class="cancel-btn">إلغاء</button>
        <button class="confirm-btn">حذف</button>
      </div>
    </div>
  `;
  
  document.body.appendChild(modal);
  
  // إظهار النافذة
  setTimeout(() => {
    modal.classList.add('show');
  }, 10);
  
  // إضافة مستمعي الأحداث
  const confirmBtn = modal.querySelector('.confirm-btn');
  const cancelBtn = modal.querySelector('.cancel-btn');
  
  // زر التأكيد
  confirmBtn.addEventListener('click', () => {
    modal.classList.remove('show');
    setTimeout(() => {
      modal.remove();
      deleteTask(taskId);
    }, 300);
  });
  
  // زر الإلغاء
  cancelBtn.addEventListener('click', () => {
    modal.classList.remove('show');
    setTimeout(() => {
      modal.remove();
    }, 300);
  });
  
  // إغلاق النافذة عند النقر خارجها
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.classList.remove('show');
      setTimeout(() => {
        modal.remove();
      }, 300);
    }
  });
  
  // إغلاق النافذة عند الضغط على زر Escape
  document.addEventListener('keydown', function escHandler(e) {
    if (e.key === 'Escape') {
      modal.classList.remove('show');
      setTimeout(() => {
        modal.remove();
      }, 300);
      document.removeEventListener('keydown', escHandler);
    }
  });
}

// ========== تبديل وضع الألوان الداكنة ==========
function toggleDarkMode() {
  const isDarkMode = document.body.classList.toggle('dark-theme');
  document.documentElement.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');
  
  // تحديث أيقونة الزر
  const themeIcon = document.querySelector('#themeToggle i');
  const sidebarThemeIcon = document.querySelector('#themeToggleSidebar i');
  
  if (themeIcon) {
    themeIcon.className = isDarkMode ? 'fas fa-sun' : 'fas fa-moon';
  }
  
  if (sidebarThemeIcon) {
    sidebarThemeIcon.className = isDarkMode ? 'fas fa-sun' : 'fas fa-moon';
  }
  
  // تحديث الإعدادات
  settings.theme = isDarkMode ? 'dark' : 'light';
  saveSettings();
  
  // عرض إشعار
  const message = isDarkMode ? 'تم تفعيل الوضع الليلي' : 'تم تفعيل الوضع النهاري';
  showNotification(message, 'info');
}

// ========== تحديث الإحصائيات ==========
function updateStats() {
  const totalTasks = tasks.length;
  const completedTasks = tasks.filter(task => task.completed).length;
  const activeTasks = totalTasks - completedTasks;
  const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

  // تحديث عناصر الإحصائيات الرئيسية
  const statsElements = {
    totalTasks: document.getElementById('totalTasks'),
    completedTasks: document.getElementById('completedTasks'),
    activeTasks: document.getElementById('activeTasks'),
    completionRate: document.getElementById('completionRate'),
    progressBar: document.querySelector('.progress-bar-fill')
  };

  // تحديث النصوص مع تأثيرات الرسوم المتحركة
  updateStatsWithAnimation(statsElements.totalTasks, totalTasks);
  updateStatsWithAnimation(statsElements.completedTasks, completedTasks);
  updateStatsWithAnimation(statsElements.activeTasks, activeTasks);
  updateStatsWithAnimation(statsElements.completionRate, completionRate, '%');

  // تحديث شريط التقدم
  if (statsElements.progressBar) {
    statsElements.progressBar.style.width = `${completionRate}%`;
    statsElements.progressBar.setAttribute('aria-valuenow', completionRate);
  }

  // تحديث إحصائيات القائمة الجانبية
  updateSidebarStats(totalTasks, completedTasks, activeTasks, completionRate);
}

// تحديث إحصائيات القائمة الجانبية
function updateSidebarStats(totalTasks, completedTasks, pendingTasks, completionRate) {
  const sidebarElements = {
    totalTasks: document.getElementById('sidebarTotalTasks'),
    completedTasks: document.getElementById('sidebarCompletedTasks'),
    pendingTasks: document.getElementById('sidebarPendingTasks'),
    completionRate: document.getElementById('sidebarCompletionRate')
  };

  // تحديث النصوص مع تأثيرات بصرية
  if (sidebarElements.totalTasks) {
    updateStatsWithAnimation(sidebarElements.totalTasks, totalTasks);
  }
  if (sidebarElements.completedTasks) {
    updateStatsWithAnimation(sidebarElements.completedTasks, completedTasks);
  }
  if (sidebarElements.pendingTasks) {
    updateStatsWithAnimation(sidebarElements.pendingTasks, pendingTasks);
  }
  if (sidebarElements.completionRate) {
    updateStatsWithAnimation(sidebarElements.completionRate, completionRate, '%');
  }
}

// تحديث الإحصائيات مع تأثيرات الرسوم المتحركة
function updateStatsWithAnimation(element, value, suffix = '') {
  if (!element) return;
  
  const currentValue = parseInt(element.textContent);
  if (isNaN(currentValue)) {
    element.textContent = `${value}${suffix}`;
    return;
  }
  
  // إضافة تأثير الرسوم المتحركة
  element.classList.add('stats-update-animation');
  
  // تحديث القيمة
  element.textContent = `${value}${suffix}`;
  
  // إزالة تأثير الرسوم المتحركة بعد انتهاء المدة
  setTimeout(() => {
    element.classList.remove('stats-update-animation');
  }, 500);
}

// ========== عرض المهام ==========
function renderTasks() {
  // قياس أداء عرض المهام
  const startTime = performance.now();
  updateLoadingMessage('جاري تحديث واجهة المستخدم...');
  
  // التأكد من وجود عناصر العرض
  if (!elements.taskList || !elements.taskGrid) return;
  
  // تصفية المهام حسب الفلتر الحالي - تحسين الأداء باستخدام مرجع واحد للمهام
  let filteredTasks = [...tasks]; // نسخ المصفوفة لتجنب تعديل المصفوفة الأصلية
  
  // تطبيق جميع الفلاتر في مرور واحد لتحسين الأداء
  const lowerSearchQuery = searchQuery ? searchQuery.toLowerCase() : '';
  const searchInTextOnly = settings.searchScope === 'text';
  
  filteredTasks = filteredTasks.filter(task => {
    // فلتر الحالة
    if (currentFilter === 'active' && task.completed) return false;
    if (currentFilter === 'completed' && !task.completed) return false;
    
    // فلتر القسم
    if (currentCategoryFilter !== 'all' && task.category !== currentCategoryFilter) return false;
    
    // فلتر البحث
    if (searchQuery) {
      const textMatch = task.text.toLowerCase().includes(lowerSearchQuery);
      if (searchInTextOnly) return textMatch;
      
      const categoryMatch = task.category.toLowerCase().includes(lowerSearchQuery);
      return textMatch || categoryMatch;
    }
    
    return true;
  });
  
  // عرض حالة الفراغ إذا لم تكن هناك مهام
  if (filteredTasks.length === 0) {
    showEmptyState();
  } else {
    hideEmptyState();
  }
  
  // استخدام DocumentFragment لتحسين الأداء عند إضافة عناصر متعددة
  const listFragment = document.createDocumentFragment();
  const gridFragment = document.createDocumentFragment();
  
  // مسح العناصر الحالية
  elements.taskList.innerHTML = '';
  elements.taskGrid.innerHTML = '';
  
  // إنشاء عناصر المهام بكفاءة
  filteredTasks.forEach(task => {
    // إنشاء عنصر المهمة في القائمة
    const listItem = createTaskElement(task, 'list');
    listFragment.appendChild(listItem);
    
    // إنشاء عنصر المهمة في الشبكة
    const gridItem = createTaskElement(task, 'grid');
    gridFragment.appendChild(gridItem);
  });
  
  // إضافة جميع العناصر دفعة واحدة لتقليل عمليات إعادة الرسم
  elements.taskList.appendChild(listFragment);
  elements.taskGrid.appendChild(gridFragment);
  
  // تحديث وضع العرض
  updateViewMode();

  // تحديث الإحصائيات
  updateStats();

  // قياس وقت الانتهاء وتسجيله
  const endTime = performance.now();
  console.log(`تم عرض ${filteredTasks.length} مهمة في ${(endTime - startTime).toFixed(2)} مللي ثانية`);
}

// إنشاء عنصر المهمة
// كاش لعناصر الأولوية لتحسين الأداء
const priorityCache = {
  low: { color: 'var(--priority-low-color)', text: 'منخفضة' },
  medium: { color: 'var(--priority-medium-color)', text: 'متوسطة' },
  high: { color: 'var(--priority-high-color)', text: 'عالية' },
  urgent: { color: 'var(--priority-urgent-color)', text: 'عاجل' }
};

function createTaskElement(task, viewType) {
  // استخدام تقنيات DOM API بدلاً من innerHTML لتحسين الأداء
  const taskElement = document.createElement('div');
  taskElement.className = `task-item ${viewType}-item ${task.completed ? 'completed' : ''}`;
  taskElement.setAttribute('data-task-id', task.id);
  taskElement.setAttribute('data-category', task.category);
  taskElement.setAttribute('data-priority', task.priority);
  
  // الحصول على معلومات الأولوية من الكاش
  const priority = priorityCache[task.priority] || { color: '', text: '' };
  
  // إنشاء عنصر مربع الاختيار
  const checkbox = document.createElement('div');
  checkbox.className = 'task-checkbox';
  checkbox.title = task.completed ? 'إلغاء الإكمال' : 'إكمال المهمة';
  
  const checkboxIcon = document.createElement('i');
  checkboxIcon.className = task.completed ? 'fas fa-check-circle' : 'far fa-circle';
  checkbox.appendChild(checkboxIcon);
  
  // إنشاء محتوى المهمة
  const content = document.createElement('div');
  content.className = 'task-content';
  
  const taskText = document.createElement('div');
  taskText.className = 'task-text';
  taskText.textContent = task.text;
  content.appendChild(taskText);
  
  const taskMeta = document.createElement('div');
  taskMeta.className = 'task-meta';
  
  // إضافة القسم
  const categorySpan = document.createElement('span');
  categorySpan.className = 'task-category';
  categorySpan.title = 'القسم';
  
  const categoryIcon = document.createElement('i');
  categoryIcon.className = 'fas fa-folder';
  categorySpan.appendChild(categoryIcon);
  categorySpan.appendChild(document.createTextNode(' ' + task.category));
  
  // إضافة الأولوية
  const prioritySpan = document.createElement('span');
  prioritySpan.className = 'task-priority';
  prioritySpan.title = 'الأولوية';
  prioritySpan.style.color = priority.color;
  
  const priorityIcon = document.createElement('i');
  priorityIcon.className = 'fas fa-flag';
  prioritySpan.appendChild(priorityIcon);
  prioritySpan.appendChild(document.createTextNode(' ' + priority.text));
  
  // إضافة التاريخ
  const dateSpan = document.createElement('span');
  dateSpan.className = 'task-date';
  dateSpan.title = 'تاريخ الإنشاء';
  
  const dateIcon = document.createElement('i');
  dateIcon.className = 'far fa-calendar-alt';
  dateSpan.appendChild(dateIcon);
  dateSpan.appendChild(document.createTextNode(' ' + formatDate(task.createdAt)));
  
  // إضافة العناصر الفرعية إلى taskMeta
  taskMeta.appendChild(categorySpan);
  taskMeta.appendChild(prioritySpan);
  taskMeta.appendChild(dateSpan);
  content.appendChild(taskMeta);
  
  // إنشاء أزرار الإجراءات
  const actions = document.createElement('div');
  actions.className = 'task-actions';
  
  // إنشاء زر التعديل المتقدم مع قائمة منسدلة
  const editContainer = document.createElement('div');
  editContainer.className = 'edit-dropdown-container';

  const editBtn = document.createElement('button');
  editBtn.className = 'edit-task-btn advanced-edit-btn';
  editBtn.title = 'خيارات التعديل';

  const editIcon = document.createElement('i');
  editIcon.className = 'fas fa-edit';
  const dropdownArrow = document.createElement('i');
  dropdownArrow.className = 'fas fa-chevron-down edit-dropdown-arrow';

  editBtn.appendChild(editIcon);
  editBtn.appendChild(dropdownArrow);

  // إنشاء القائمة المنسدلة للتعديل
  const editDropdown = document.createElement('div');
  editDropdown.className = 'edit-dropdown-menu';

  // خيارات التعديل
  const editOptions = [
    { icon: 'fas fa-edit', text: 'تعديل سريع', action: 'quick-edit' },
    { icon: 'fas fa-cogs', text: 'تعديل متقدم', action: 'advanced-edit' },
    { icon: 'fas fa-copy', text: 'نسخ المهمة', action: 'duplicate' },
    { icon: 'fas fa-flag', text: 'تغيير الأولوية', action: 'priority' },
    { icon: 'fas fa-tag', text: 'تغيير التصنيف', action: 'category' },
    { icon: 'fas fa-calendar', text: 'تحديد موعد', action: 'due-date' }
  ];

  editOptions.forEach(option => {
    const optionBtn = document.createElement('button');
    optionBtn.className = 'edit-dropdown-item';
    optionBtn.dataset.action = option.action;

    const optionIcon = document.createElement('i');
    optionIcon.className = option.icon;

    const optionText = document.createElement('span');
    optionText.textContent = option.text;

    optionBtn.appendChild(optionIcon);
    optionBtn.appendChild(optionText);

    editDropdown.appendChild(optionBtn);
  });

  editContainer.appendChild(editBtn);
  editContainer.appendChild(editDropdown);
  
  const deleteBtn = document.createElement('button');
  deleteBtn.className = 'delete-task-btn';
  deleteBtn.title = 'حذف المهمة';
  const deleteIcon = document.createElement('i');
  deleteIcon.className = 'fas fa-trash-alt';
  deleteBtn.appendChild(deleteIcon);
  
  actions.appendChild(editContainer);
  actions.appendChild(deleteBtn);
  
  // إضافة جميع العناصر الرئيسية إلى taskElement
  taskElement.appendChild(checkbox);
  taskElement.appendChild(content);
  taskElement.appendChild(actions);
  
  // إضافة مستمعي الأحداث
  checkbox.addEventListener('click', () => toggleTaskCompletion(task.id));

  // مستمع حدث زر التعديل المتقدم
  editBtn.addEventListener('click', (e) => {
    e.stopPropagation();
    toggleEditDropdown(editContainer);
  });

  // مستمعي أحداث خيارات التعديل
  editDropdown.querySelectorAll('.edit-dropdown-item').forEach(item => {
    item.addEventListener('click', (e) => {
      e.stopPropagation();
      const action = item.dataset.action;
      handleEditAction(task.id, action);
      closeAllEditDropdowns();
    });
  });

  deleteBtn.addEventListener('click', () => showCustomDeleteConfirmation(task.id));
  
  return taskElement;
}

// كاش لرسائل حالة الفراغ لتحسين الأداء
const emptyStateCache = {
  search: { message: 'لا توجد نتائج مطابقة لبحثك', icon: 'fa-search' },
  active: { message: 'لا توجد مهام نشطة', icon: 'fa-spinner' },
  completed: { message: 'لا توجد مهام مكتملة', icon: 'fa-check-circle' },
  category: { message: 'لا توجد مهام في قسم "{category}"', icon: 'fa-folder-open' },
  default: { message: 'لا توجد مهام، أضف مهمة جديدة!', icon: 'fa-tasks' }
};

// عرض حالة الفراغ
function showEmptyState() {
  if (!elements.emptyState) return;
  
  // قياس أداء عرض حالة الفراغ
  const startTime = performance.now();
  
  elements.emptyState.style.display = 'flex';
  
  // تحديد نوع الرسالة المناسبة من الكاش
  let emptyState;
  
  if (searchQuery) {
    emptyState = emptyStateCache.search;
  } else if (currentFilter === 'active') {
    emptyState = emptyStateCache.active;
  } else if (currentFilter === 'completed') {
    emptyState = emptyStateCache.completed;
  } else if (currentCategoryFilter !== 'all') {
    emptyState = emptyStateCache.category;
    // استبدال الفئة في الرسالة
    emptyState = {
      ...emptyState,
      message: emptyState.message.replace('{category}', currentCategoryFilter)
    };
  } else {
    emptyState = emptyStateCache.default;
  }
  
  // استخدام DOM API بدلاً من innerHTML لتحسين الأداء
  // مسح المحتوى الحالي
  elements.emptyState.innerHTML = '';
  
  // إنشاء أيقونة الحالة الفارغة
  const iconDiv = document.createElement('div');
  iconDiv.className = 'empty-icon';
  
  const icon = document.createElement('i');
  icon.className = `fas ${emptyState.icon}`;
  iconDiv.appendChild(icon);
  
  // إنشاء رسالة الحالة الفارغة
  const message = document.createElement('p');
  message.className = 'empty-text';
  message.textContent = emptyState.message;
  
  // إضافة العناصر إلى حاوية الحالة الفارغة
  elements.emptyState.appendChild(iconDiv);
  elements.emptyState.appendChild(message);
  
  // إضافة زر إضافة مهمة إذا كان في الحالة الافتراضية
  if (!searchQuery && currentFilter === 'all' && currentCategoryFilter === 'all') {
    const addButton = document.createElement('button');
    addButton.id = 'emptyAddTaskBtn';
    addButton.className = 'empty-add-btn';
    
    const plusIcon = document.createElement('i');
    plusIcon.className = 'fas fa-plus';
    addButton.appendChild(plusIcon);
    addButton.appendChild(document.createTextNode(' إضافة مهمة'));
    
    elements.emptyState.appendChild(addButton);
    
    // إضافة مستمع حدث لزر إضافة المهمة
    addButton.addEventListener('click', () => {
      elements.taskInput.focus();
    });
  }
  
  // قياس وقت الانتهاء وتسجيله
  const endTime = performance.now();
  console.log(`تم عرض حالة الفراغ في ${(endTime - startTime).toFixed(2)} مللي ثانية`);
}

// إخفاء حالة الفراغ
function hideEmptyState() {
  if (!elements.emptyState) return;
  elements.emptyState.style.display = 'none';
}

// تحديث وضع العرض
function updateViewMode() {
  // قياس أداء تحديث وضع العرض
  const startTime = performance.now();
  
  if (!elements.listView || !elements.gridView) return;
  
  // تحديث وضع العرض بطريقة أكثر كفاءة
  const isList = currentView === 'list';
  
  // استخدام classList.add/remove بدلاً من تغيير style.display مباشرة
  // هذا يسمح بتحسين الأداء عن طريق تقليل عمليات إعادة الرسم
  if (isList) {
    elements.listView.classList.add('active-view');
    elements.gridView.classList.remove('active-view');
  } else {
    elements.listView.classList.remove('active-view');
    elements.gridView.classList.add('active-view');
  }
  
  // تحديث أزرار تبديل العرض - تحسين باستخدام كاش للعناصر
  const viewButtons = document.querySelectorAll('.view-toggle-btn');
  if (viewButtons.length > 0) {
    for (let i = 0; i < viewButtons.length; i++) {
      const btn = viewButtons[i];
      const isActive = btn.getAttribute('data-view') === currentView;
      
      // استخدام classList.toggle مع القيمة المحددة لتجنب عمليات DOM غير الضرورية
      if (btn.classList.contains('active') !== isActive) {
        btn.classList.toggle('active');
      }
    }
  }
  
  // حفظ وضع العرض في localStorage - تحسين باستخدام التخزين المؤقت
  if (localStorage.getItem('viewMode') !== currentView) {
    localStorage.setItem('viewMode', currentView);
  }
  
  // قياس وقت الانتهاء وتسجيله
  const endTime = performance.now();
  console.log(`تم تحديث وضع العرض في ${(endTime - startTime).toFixed(2)} مللي ثانية`);
}

// ========== تهيئة الأحداث ==========
function initEvents() {
  // تهيئة العناصر
  elements.taskInput = document.getElementById('taskInput');
  elements.addTaskBtn = document.getElementById('addTaskBtn');
  elements.taskList = document.getElementById('taskList');
  elements.taskGrid = document.getElementById('taskGrid');
  elements.taskCount = document.getElementById('taskCount');
  elements.clearAllBtn = document.getElementById('clearAllBtn');
  elements.clearCompletedBtn = document.getElementById('clearCompletedBtn');
  elements.themeToggle = document.getElementById('themeToggle');
  elements.filterButtons = document.querySelectorAll('.nav-btn[data-filter]');
  elements.viewToggleButtons = document.querySelectorAll('.view-toggle-btn');
  elements.emptyState = document.getElementById('emptyState');
  elements.listView = document.getElementById('listView');
  elements.gridView = document.getElementById('gridView');
  elements.categoryInput = document.getElementById('taskCategory');
  elements.categoryFilterContainer = document.getElementById('categoryFilterContainer');
  elements.searchInput = document.getElementById('searchInput');
  elements.statsBar = document.getElementById('statsBar');
  
  // إضافة مستمعي الأحداث
  if (elements.addTaskBtn) {
    elements.addTaskBtn.addEventListener('click', addTask);
  }
  
  if (elements.taskInput) {
    elements.taskInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        addTask();
      }
    });
  }
  
  if (elements.themeToggle) {
    elements.themeToggle.addEventListener('click', toggleDarkMode);
  }
  
  const themeToggleSidebar = document.getElementById('themeToggleSidebar');
  if (themeToggleSidebar) {
    themeToggleSidebar.addEventListener('click', toggleDarkMode);
  }
  
  // أزرار الفلتر
  elements.filterButtons.forEach(btn => {
    btn.addEventListener('click', () => {
      // إزالة الفئة النشطة من جميع الأزرار
      elements.filterButtons.forEach(b => b.classList.remove('active'));
      // إضافة الفئة النشطة للزر المحدد
      btn.classList.add('active');
      // تحديث الفلتر الحالي
      currentFilter = btn.getAttribute('data-filter');
      // إعادة عرض المهام
      renderTasks();
    });
  });
  
  // أزرار تبديل العرض
  elements.viewToggleButtons.forEach(btn => {
    btn.addEventListener('click', () => {
      // إزالة الفئة النشطة من جميع الأزرار
      elements.viewToggleButtons.forEach(b => b.classList.remove('active'));
      // إضافة الفئة النشطة للزر المحدد
      btn.classList.add('active');
      // تحديث وضع العرض الحالي
      currentView = btn.getAttribute('data-view');
      // تحديث وضع العرض
      updateViewMode();
    });
  });
  
  // أزرار فلتر القسم
  document.querySelectorAll('.cat-btn').forEach(btn => {
    btn.addEventListener('click', () => {
      // إزالة الفئة النشطة من جميع الأزرار
      document.querySelectorAll('.cat-btn').forEach(b => b.classList.remove('active'));
      // إضافة الفئة النشطة للزر المحدد
      btn.classList.add('active');
      // تحديث فلتر القسم الحالي
      currentCategoryFilter = btn.getAttribute('data-category');
      // إعادة عرض المهام
      renderTasks();
    });
  });
  
  // حقل البحث
  if (elements.searchInput) {
    const searchHandler = debounce(() => {
      searchQuery = elements.searchInput.value.trim();
      renderTasks();
    }, 300);
    
    elements.searchInput.addEventListener('input', () => {
      if (settings.instantSearch) {
        searchHandler();
      }
    });
    
    elements.searchInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        searchQuery = elements.searchInput.value.trim();
        renderTasks();
      }
    });
  }
  
  // زر البحث
  const searchBtn = document.getElementById('searchBtn');
  if (searchBtn) {
    searchBtn.addEventListener('click', () => {
      if (elements.searchInput) {
        searchQuery = elements.searchInput.value.trim();
        renderTasks();
      }
    });
  }
  
  // زر مسح البحث
  const clearSearchBtn = document.getElementById('clearSearchBtn');
  if (clearSearchBtn) {
    clearSearchBtn.addEventListener('click', () => {
      if (elements.searchInput) {
        elements.searchInput.value = '';
        searchQuery = '';
        renderTasks();
      }
    });
  }
  
  // زر فتح/إغلاق القائمة الجانبية
  const menuToggle = document.getElementById('menuToggle');
  const sideMenu = document.getElementById('sideMenu');
  const closeSideMenu = document.getElementById('closeSideMenu');
  const sidebarOverlay = document.getElementById('sidebarOverlay');
  
  if (menuToggle && sideMenu && closeSideMenu && sidebarOverlay) {
    menuToggle.addEventListener('click', () => {
      sideMenu.classList.add('open');
      sidebarOverlay.classList.add('visible');
      document.body.classList.add('sidebar-open');
    });
    
    closeSideMenu.addEventListener('click', () => {
      sideMenu.classList.remove('open');
      sidebarOverlay.classList.remove('visible');
      document.body.classList.remove('sidebar-open');
    });
    
    sidebarOverlay.addEventListener('click', () => {
      sideMenu.classList.remove('open');
      sidebarOverlay.classList.remove('visible');
      document.body.classList.remove('sidebar-open');
    });
  }
  
  // زر فتح/إغلاق البحث من القائمة الجانبية
  const searchTaskBtn = document.getElementById('searchTaskBtn');
  const searchBox = document.getElementById('searchBox');
  const closeSearchBtn = document.getElementById('closeSearchBtn');

  if (searchTaskBtn) {
    searchTaskBtn.addEventListener('click', () => {
      openSearchBox();
    });
  }

  if (closeSearchBtn) {
    closeSearchBtn.addEventListener('click', () => {
      closeSearchBox();
    });
  }

  // دالة إغلاق صندوق البحث
  function closeSearchBox() {
    if (searchBox) {
      searchBox.classList.remove('open');
      setTimeout(() => {
        searchBox.style.display = 'none';
      }, 300);
    }
  }

  // زر البحث الرئيسي في الصفحة - يستخدم نفس صندوق البحث
  const mainSearchBtn = document.getElementById('mainSearchBtn');
  if (mainSearchBtn) {
    mainSearchBtn.addEventListener('click', () => {
      openSearchBox();
    });
  }

  // دالة فتح صندوق البحث (موحدة)
  function openSearchBox() {
    if (searchBox) {
      searchBox.style.display = 'block';
      setTimeout(() => {
        searchBox.classList.add('open');
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
          searchInput.focus();
        }
      }, 10);
    }
  }
  
  // زر فتح/إغلاق الإعدادات
  const settingsMenu = document.getElementById('settingsMenu');
  
  if (settingsMenu && settingsModal && closeSettingsModal) {
    settingsMenu.addEventListener('click', () => {
      settingsModal.style.display = 'flex';
      setTimeout(() => {
        settingsModal.classList.add('open');
      }, 10);
    });
    
    closeSettingsModal.addEventListener('click', () => {
      settingsModal.classList.remove('open');
      setTimeout(() => {
        settingsModal.style.display = 'none';
      }, 300);
    });
  }
  
  // زر حفظ الإعدادات
  if (saveSettingsBtn) {
    saveSettingsBtn.addEventListener('click', () => {
      // تحديث الإعدادات من النموذج مع فحص وجود العناصر
      if (primaryColorSelect) settings.primaryColor = primaryColorSelect.value;
      if (themeSelect) settings.theme = themeSelect.value;
      if (notifSound) settings.notifSound = notifSound.checked;
      if (notifPosition) settings.notifPosition = notifPosition.value;
      if (notifDuration) settings.notifDuration = notifDuration.value;
      if (confirmDelete) settings.confirmDelete = confirmDelete.checked;
      if (undoDelete) settings.undoDelete = undoDelete.checked;
      if (defaultView) settings.defaultView = defaultView.value;
      if (addCompleted) settings.addCompleted = addCompleted.checked;
      if (instantSearch) settings.instantSearch = instantSearch.checked;
      if (searchScope) settings.searchScope = searchScope.value;
      if (fontSize) settings.fontSize = fontSize.value;
      if (roundedBorders) settings.roundedBorders = roundedBorders.checked;
      if (shadows) settings.shadows = shadows.checked;
      if (animations) settings.animations = animations.checked;
      settings.offlineMode = offlineMode.checked;
      
      // حفظ الإعدادات
      saveSettings();
      
      // تطبيق الإعدادات
      applySettings();
      
      // إغلاق النافذة
      settingsModal.classList.remove('open');
      setTimeout(() => {
        settingsModal.style.display = 'none';
      }, 300);
    });
  }
  
  // زر إلغاء الإعدادات
  if (cancelSettingsBtn) {
    cancelSettingsBtn.addEventListener('click', () => {
      // إعادة تحميل الإعدادات
      loadSettings();
      
      // إغلاق النافذة
      settingsModal.classList.remove('open');
      setTimeout(() => {
        settingsModal.style.display = 'none';
      }, 300);
    });
  }
  
  // زر إعادة تعيين الإعدادات
  if (resetSettingsBtn) {
    resetSettingsBtn.addEventListener('click', () => {
      if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
        // إعادة تعيين الإعدادات إلى القيم الافتراضية
        settings = {
          primaryColor: '#1976d2',
          theme: 'auto',
          notifSound: false,
          notifPosition: 'bottom',
          notifDuration: 'normal',
          confirmDelete: true,
          undoDelete: true,
          defaultView: 'list',
          addCompleted: false,
          instantSearch: true,
          searchScope: 'all',
          fontSize: 'medium',
          roundedBorders: true,
          shadows: true,
          animations: true,
          offlineMode: true
        };
        
        // حفظ الإعدادات
        saveSettings();
        
        // تطبيق الإعدادات
        applySettings();
        
        showNotification('تم إعادة تعيين الإعدادات بنجاح', 'success');
      }
    });
  }
  
  // زر حذف جميع المهام
  if (deleteAllTasksBtn) {
    deleteAllTasksBtn.addEventListener('click', async () => {
      if (confirm('هل أنت متأكد من حذف جميع المهام؟ لا يمكن التراجع عن هذا الإجراء!')) {
        try {
          // حذف جميع المهام من IndexedDB
          await db.deleteAllTasks();
          
          // مسح المصفوفة المحلية
          tasks = [];
          
          // تحديث واجهة المستخدم
          renderTasks();
          updateStats();
          
          showNotification('تم حذف جميع المهام بنجاح', 'success');
        } catch (error) {
          console.error('خطأ في حذف جميع المهام:', error);
          showNotification('حدث خطأ أثناء حذف المهام', 'error');
        }
      }
    });
  }
  
  // زر تصدير المهام
  if (exportTasksBtn) {
    exportTasksBtn.addEventListener('click', () => {
      try {
        // تحويل المهام إلى نص JSON
        const tasksJSON = JSON.stringify(tasks, null, 2);
        
        // إنشاء رابط تنزيل
        const blob = new Blob([tasksJSON], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        // إنشاء عنصر رابط وتنزيل الملف
        const a = document.createElement('a');
        a.href = url;
        a.download = `tdl_tasks_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        
        // تنظيف
        setTimeout(() => {
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }, 100);
        
        showNotification('تم تصدير المهام بنجاح', 'success');
      } catch (error) {
        console.error('خطأ في تصدير المهام:', error);
        showNotification('حدث خطأ أثناء تصدير المهام', 'error');
      }
    });
  }
  
  // زر استيراد المهام
  if (importTasksBtn) {
    importTasksBtn.addEventListener('click', () => {
      // إنشاء عنصر إدخال ملف مخفي
      const fileInput = document.createElement('input');
      fileInput.type = 'file';
      fileInput.accept = '.json';
      fileInput.style.display = 'none';
      document.body.appendChild(fileInput);
      
      // محاكاة النقر على عنصر الإدخال
      fileInput.click();
      
      // إضافة مستمع حدث لتغيير الملف
      fileInput.addEventListener('change', async (e) => {
        if (e.target.files.length === 0) return;
        
        const file = e.target.files[0];
        const reader = new FileReader();
        
        reader.onload = async (event) => {
          try {
            // تحليل محتوى الملف
            const importedTasks = JSON.parse(event.target.result);
            
            if (!Array.isArray(importedTasks)) {
              throw new Error('تنسيق الملف غير صالح');
            }
            
            // التأكد من أن المهام المستوردة صالحة
            const validTasks = importedTasks.filter(task => {
              return (
                task && 
                typeof task === 'object' && 
                typeof task.id === 'string' && 
                typeof task.text === 'string' && 
                typeof task.completed === 'boolean'
              );
            });
            
            if (validTasks.length === 0) {
              throw new Error('لم يتم العثور على مهام صالحة في الملف');
            }
            
            // سؤال المستخدم عن كيفية الاستيراد
            const importMode = confirm(
              'هل تريد استبدال جميع المهام الحالية بالمهام المستوردة؟\n' +
              'اضغط "موافق" للاستبدال، أو "إلغاء" للإضافة إلى المهام الحالية.'
            );
            
            if (importMode) {
              // حذف جميع المهام الحالية
              await db.deleteAllTasks();
              tasks = [];
            }
            
            // إضافة المهام المستوردة
            for (const task of validTasks) {
              // التأكد من عدم وجود تكرار في المعرفات
              if (!tasks.some(t => t.id === task.id)) {
                await db.addTask(task);
                tasks.push(task);
              }
            }
            
            // تحديث واجهة المستخدم
            renderTasks();
            updateStats();
            
            showNotification(`تم استيراد ${validTasks.length} مهمة بنجاح`, 'success');
          } catch (error) {
            console.error('خطأ في استيراد المهام:', error);
            showNotification(`حدث خطأ أثناء استيراد المهام: ${error.message}`, 'error');
          }
        };
        
        reader.onerror = () => {
          showNotification('حدث خطأ أثناء قراءة الملف', 'error');
        };
        
        // قراءة الملف كنص
        reader.readAsText(file);
        
        // تنظيف
        document.body.removeChild(fileInput);
      });
    });
  }
  
  // زر تسجيل الخروج
  const logoutMenu = document.getElementById('logoutMenu');
  if (logoutMenu) {
    logoutMenu.addEventListener('click', () => {
      if (confirm('هل تريد تسجيل الخروج؟')) {
        // مسح البيانات المحلية
        localStorage.clear();

        // إعادة تحميل الصفحة
        window.location.reload();
      }
    });
  }

  // ========== أحداث نافذة تعديل المهمة ==========
  const editTaskModal = document.getElementById('editTaskModal');
  const closeEditTaskModal = document.getElementById('closeEditTaskModal');
  const editTaskForm = document.getElementById('editTaskForm');
  const cancelEditTask = document.getElementById('cancelEditTask');

  // إغلاق النافذة عند النقر على زر الإغلاق
  if (closeEditTaskModal) {
    closeEditTaskModal.addEventListener('click', closeEditTaskModal);
  }

  // إغلاق النافذة عند النقر على زر الإلغاء
  if (cancelEditTask) {
    cancelEditTask.addEventListener('click', closeEditTaskModal);
  }

  // إغلاق النافذة عند النقر خارجها
  if (editTaskModal) {
    editTaskModal.addEventListener('click', (e) => {
      if (e.target === editTaskModal) {
        closeEditTaskModal();
      }
    });
  }

  // حفظ التعديلات عند إرسال النموذج
  if (editTaskForm) {
    editTaskForm.addEventListener('submit', saveEditTask);
  }

  // إغلاق النافذة عند الضغط على Escape
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && editTaskModal && editTaskModal.classList.contains('show')) {
      closeEditTaskModal();
    }
  });

  // ========== أحداث أزرار القائمة الجانبية الجديدة ==========
  const clearCompletedBtn = document.getElementById('clearCompletedBtn');

  if (clearCompletedBtn) {
    clearCompletedBtn.addEventListener('click', () => {
      clearCompletedTasks();
    });
  }

  // ========== إعداد القوائم المنسدلة ==========
  initDropdownMenus();
}

// ========== تهيئة Service Worker ==========
function initServiceWorker() {
  // التحقق من دعم Service Worker
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
      // تسجيل Service Worker
      navigator.serviceWorker.register('/js/service-worker.js')
        .then(registration => {
          console.log('Service Worker تم تسجيله بنجاح:', registration.scope);
          
          // التحقق من وجود تحديثات
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                // يوجد تحديث جديد
                showUpdateNotification();
              }
            });
          });
        })
        .catch(error => {
          console.error('فشل تسجيل Service Worker:', error);
        });
      
      // التعامل مع تحديث الصفحة عند تغيير Service Worker
      let refreshing = false;
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        if (!refreshing) {
          refreshing = true;
          window.location.reload();
        }
      });
    });
  }
}

// عرض إشعار التحديث
function showUpdateNotification() {
  const notification = document.createElement('div');
  notification.className = 'notification update-notification';
  notification.innerHTML = `
    <div class="notification-content">
      <i class="fas fa-sync-alt"></i>
      <span>يوجد تحديث جديد للتطبيق</span>
    </div>
    <button class="update-btn">تحديث الآن</button>
  `;
  
  document.body.appendChild(notification);
  
  // إظهار الإشعار
  setTimeout(() => {
    notification.classList.add('show');
  }, 10);
  
  // زر التحديث
  const updateBtn = notification.querySelector('.update-btn');
  updateBtn.addEventListener('click', () => {
    notification.classList.remove('show');
    
    // إرسال رسالة إلى Service Worker لتخطي الانتظار
    if (navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({ action: 'SKIP_WAITING' });
    }
    
    setTimeout(() => {
      notification.remove();
    }, 300);
  });
}

// ========== إعداد القوائم المنسدلة ==========
function initDropdownMenus() {
  // إعداد قائمة التصدير
  const exportBtn = document.getElementById('exportDataBtn');
  const exportDropdown = document.getElementById('exportDropdown');

  if (exportBtn && exportDropdown) {
    exportBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      toggleDropdown('exportDataBtn', 'exportDropdown');
    });

    // إضافة مستمعي الأحداث لعناصر القائمة
    exportDropdown.querySelectorAll('.dropdown-item').forEach(item => {
      item.addEventListener('click', (e) => {
        e.stopPropagation();
        const format = item.dataset.format;
        exportTasks(format);
        closeAllDropdowns();
      });
    });
  }

  // إعداد قائمة الاستيراد
  const importBtn = document.getElementById('importDataBtn');
  const importDropdown = document.getElementById('importDropdown');

  if (importBtn && importDropdown) {
    importBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      toggleDropdown('importDataBtn', 'importDropdown');
    });

    importDropdown.querySelectorAll('.dropdown-item').forEach(item => {
      item.addEventListener('click', (e) => {
        e.stopPropagation();
        const format = item.dataset.format;
        importTasks(format);
        closeAllDropdowns();
      });
    });
  }

  // إعداد قائمة النسخ الاحتياطي
  const backupBtn = document.getElementById('backupBtn');
  const backupDropdown = document.getElementById('backupDropdown');

  if (backupBtn && backupDropdown) {
    backupBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      toggleDropdown('backupBtn', 'backupDropdown');
    });

    backupDropdown.querySelectorAll('.dropdown-item').forEach(item => {
      item.addEventListener('click', (e) => {
        e.stopPropagation();
        const format = item.dataset.format;
        createBackup(format);
        closeAllDropdowns();
      });
    });
  }

  // إغلاق القوائم عند النقر خارجها
  document.addEventListener('click', () => {
    closeAllDropdowns();
    closeAllEditDropdowns();
  });
}

// تبديل حالة القائمة المنسدلة
function toggleDropdown(btnId, dropdownId) {
  const container = document.getElementById(btnId).closest('.dropdown-container');
  const isActive = container.classList.contains('active');

  // إغلاق جميع القوائم الأخرى
  closeAllDropdowns();

  // تبديل القائمة الحالية
  if (!isActive) {
    container.classList.add('active');
  }
}

// إغلاق جميع القوائم المنسدلة
function closeAllDropdowns() {
  document.querySelectorAll('.dropdown-container').forEach(container => {
    container.classList.remove('active');
  });
}

// ========== إدارة قوائم التعديل المنسدلة ==========

// تبديل قائمة التعديل المنسدلة
function toggleEditDropdown(container) {
  const isActive = container.classList.contains('active');

  // إغلاق جميع قوائم التعديل الأخرى
  closeAllEditDropdowns();

  // تبديل القائمة الحالية
  if (!isActive) {
    container.classList.add('active');
  }
}

// إغلاق جميع قوائم التعديل المنسدلة
function closeAllEditDropdowns() {
  document.querySelectorAll('.edit-dropdown-container').forEach(container => {
    container.classList.remove('active');
  });
}

// معالجة إجراءات التعديل المختلفة
function handleEditAction(taskId, action) {
  const task = tasks.find(t => t.id === taskId);
  if (!task) return;

  switch (action) {
    case 'quick-edit':
      startQuickEdit(taskId);
      break;
    case 'advanced-edit':
      editTask(taskId); // استخدام النافذة المتقدمة الموجودة
      break;
    case 'duplicate':
      duplicateTask(taskId);
      break;
    case 'priority':
      changePriority(taskId);
      break;
    case 'category':
      changeCategory(taskId);
      break;
    case 'due-date':
      changeDueDate(taskId);
      break;
    default:
      console.warn('إجراء غير معروف:', action);
  }
}

// ========== دوال إجراءات التعديل ==========

// تعديل سريع للنص فقط
function startQuickEdit(taskId) {
  const taskElement = document.querySelector(`[data-task-id="${taskId}"]`);
  if (!taskElement) return;

  const taskTextElement = taskElement.querySelector('.task-text');
  if (!taskTextElement) return;

  const originalText = taskTextElement.textContent;

  // إنشاء حقل إدخال سريع
  const input = document.createElement('input');
  input.type = 'text';
  input.value = originalText;
  input.className = 'quick-edit-input';

  // استبدال النص بحقل الإدخال
  taskTextElement.innerHTML = '';
  taskTextElement.appendChild(input);
  input.focus();
  input.select();

  // حفظ عند الضغط على Enter
  input.addEventListener('keydown', async (e) => {
    if (e.key === 'Enter') {
      const newText = input.value.trim();
      if (newText && newText !== originalText) {
        try {
          await updateTask(taskId, { text: newText });
          taskTextElement.textContent = newText;
          showNotification('تم تحديث المهمة', 'success');
        } catch (error) {
          taskTextElement.textContent = originalText;
          showNotification('خطأ في تحديث المهمة', 'error');
        }
      } else {
        taskTextElement.textContent = originalText;
      }
    } else if (e.key === 'Escape') {
      taskTextElement.textContent = originalText;
    }
  });

  // إلغاء عند فقدان التركيز
  input.addEventListener('blur', () => {
    taskTextElement.textContent = originalText;
  });
}

// نسخ المهمة
async function duplicateTask(taskId) {
  const task = tasks.find(t => t.id === taskId);
  if (!task) return;

  const newTask = {
    ...task,
    id: Date.now().toString() + Math.random().toString(36).substring(2, 9),
    text: task.text + ' (نسخة)',
    completed: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  try {
    await addTask(newTask.text, newTask.category, newTask.priority);
    showNotification('تم نسخ المهمة بنجاح', 'success');
  } catch (error) {
    showNotification('خطأ في نسخ المهمة', 'error');
  }
}

// تغيير الأولوية
async function changePriority(taskId) {
  const priorities = [
    { value: 'low', text: 'منخفضة', color: '#4caf50' },
    { value: 'medium', text: 'متوسطة', color: '#ff9800' },
    { value: 'high', text: 'عالية', color: '#f44336' },
    { value: 'urgent', text: 'عاجل', color: '#9c27b0' }
  ];

  const task = tasks.find(t => t.id === taskId);
  const currentPriority = task?.priority || 'medium';

  const priorityOptions = priorities.map(p =>
    `${p.value === currentPriority ? '✓' : ' '} ${p.text}`
  ).join('\n');

  const newPriority = prompt(
    `اختر الأولوية الجديدة:\n\n${priorityOptions}\n\nأدخل: low, medium, high, أو urgent`,
    currentPriority
  );

  if (newPriority && priorities.some(p => p.value === newPriority)) {
    try {
      await updateTask(taskId, { priority: newPriority });
      renderTasks();
      showNotification('تم تحديث الأولوية', 'success');
    } catch (error) {
      showNotification('خطأ في تحديث الأولوية', 'error');
    }
  }
}

// تغيير التصنيف
async function changeCategory(taskId) {
  const categories = ['عمل', 'شخصي', 'دراسة', 'أخرى'];
  const task = tasks.find(t => t.id === taskId);
  const currentCategory = task?.category || 'عمل';

  const categoryOptions = categories.map(c =>
    `${c === currentCategory ? '✓' : ' '} ${c}`
  ).join('\n');

  const newCategory = prompt(
    `اختر التصنيف الجديد:\n\n${categoryOptions}\n\nأدخل: عمل، شخصي، دراسة، أو أخرى`,
    currentCategory
  );

  if (newCategory && categories.includes(newCategory)) {
    try {
      await updateTask(taskId, { category: newCategory });
      renderTasks();
      showNotification('تم تحديث التصنيف', 'success');
    } catch (error) {
      showNotification('خطأ في تحديث التصنيف', 'error');
    }
  }
}

// تحديد موعد الاستحقاق
async function changeDueDate(taskId) {
  const task = tasks.find(t => t.id === taskId);
  const currentDate = task?.dueDate || '';

  const newDate = prompt(
    'أدخل تاريخ الاستحقاق (YYYY-MM-DD) أو اتركه فارغاً لإزالة التاريخ:',
    currentDate
  );

  if (newDate !== null) {
    try {
      await updateTask(taskId, { dueDate: newDate });
      renderTasks();
      showNotification(newDate ? 'تم تحديد موعد الاستحقاق' : 'تم إزالة موعد الاستحقاق', 'success');
    } catch (error) {
      showNotification('خطأ في تحديث موعد الاستحقاق', 'error');
    }
  }
}

// ========== وظائف التصدير والاستيراد المحسنة ==========

// تصدير المهام بصيغ متعددة
function exportTasks(format = 'json') {
  try {
    const timestamp = new Date().toISOString().split('T')[0];
    let filename, content, mimeType;

    switch (format) {
      case 'json':
        const exportData = {
          tasks: tasks,
          settings: settings,
          exportDate: new Date().toISOString(),
          version: '1.0'
        };
        content = JSON.stringify(exportData, null, 2);
        filename = `tdl-export-${timestamp}.json`;
        mimeType = 'application/json';
        break;

      case 'csv':
        content = convertTasksToCSV(tasks);
        filename = `tdl-export-${timestamp}.csv`;
        mimeType = 'text/csv';
        break;

      case 'txt':
        content = convertTasksToTXT(tasks);
        filename = `tdl-export-${timestamp}.txt`;
        mimeType = 'text/plain';
        break;

      default:
        throw new Error('صيغة غير مدعومة');
    }

    const dataBlob = new Blob([content], { type: mimeType });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = filename;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showNotification(`تم تصدير البيانات بصيغة ${format.toUpperCase()} بنجاح`, 'success');
  } catch (error) {
    console.error('خطأ في تصدير البيانات:', error);
    showNotification('حدث خطأ أثناء تصدير البيانات', 'error');
  }
}

// دوال التحويل للصيغ المختلفة
function convertTasksToCSV(tasks) {
  const headers = ['ID', 'النص', 'مكتملة', 'التصنيف', 'الأولوية', 'تاريخ الإنشاء', 'تاريخ الاستحقاق', 'الملاحظات'];
  const csvContent = [
    headers.join(','),
    ...tasks.map(task => [
      task.id,
      `"${task.text.replace(/"/g, '""')}"`,
      task.completed ? 'نعم' : 'لا',
      task.category || 'عام',
      task.priority || 'متوسطة',
      new Date(task.createdAt).toLocaleDateString('ar-SA'),
      task.dueDate ? new Date(task.dueDate).toLocaleDateString('ar-SA') : '',
      `"${(task.notes || '').replace(/"/g, '""')}"`
    ].join(','))
  ].join('\n');

  return '\uFEFF' + csvContent; // إضافة BOM للدعم العربي
}

function convertTasksToTXT(tasks) {
  const content = [
    '='.repeat(50),
    'قائمة المهام - TDL V1.0',
    `تاريخ التصدير: ${new Date().toLocaleDateString('ar-SA')}`,
    `عدد المهام: ${tasks.length}`,
    '='.repeat(50),
    '',
    ...tasks.map((task, index) => [
      `${index + 1}. ${task.text}`,
      `   الحالة: ${task.completed ? '✅ مكتملة' : '⏳ معلقة'}`,
      `   التصنيف: ${task.category || 'عام'}`,
      `   الأولوية: ${task.priority || 'متوسطة'}`,
      `   تاريخ الإنشاء: ${new Date(task.createdAt).toLocaleDateString('ar-SA')}`,
      task.dueDate ? `   تاريخ الاستحقاق: ${new Date(task.dueDate).toLocaleDateString('ar-SA')}` : '',
      task.notes ? `   ملاحظات: ${task.notes}` : '',
      '-'.repeat(30)
    ].filter(line => line).join('\n')).join('\n\n')
  ].join('\n');

  return content;
}

// استيراد المهام بصيغ متعددة
function importTasks(format = 'json') {
  const fileInput = document.createElement('input');
  fileInput.type = 'file';
  fileInput.style.display = 'none';

  // تحديد أنواع الملفات المقبولة حسب الصيغة
  switch (format) {
    case 'json':
      fileInput.accept = '.json';
      break;
    case 'csv':
      fileInput.accept = '.csv';
      break;
    case 'txt':
      fileInput.accept = '.txt';
      break;
    default:
      fileInput.accept = '.json,.csv,.txt';
  }

  fileInput.addEventListener('change', (e) => {
    const file = e.target.files[0];
    if (file) {
      processImportFile(file, format);
    }
  });

  document.body.appendChild(fileInput);
  fileInput.click();
  document.body.removeChild(fileInput);
}

// معالجة ملف الاستيراد
function processImportFile(file, format) {
  const reader = new FileReader();

  reader.onload = async (e) => {
    try {
      let importedTasks = [];
      const content = e.target.result;

      switch (format) {
        case 'json':
          importedTasks = parseJSONImport(content);
          break;
        case 'csv':
          importedTasks = parseCSVImport(content);
          break;
        case 'txt':
          importedTasks = parseTXTImport(content);
          break;
        default:
          throw new Error('صيغة غير مدعومة');
      }

      if (importedTasks.length === 0) {
        throw new Error('لم يتم العثور على مهام صالحة في الملف');
      }

      // تأكيد الاستيراد
      const confirmImport = confirm(
        `هل تريد استيراد ${importedTasks.length} مهمة من ملف ${format.toUpperCase()}؟\n` +
        `سيتم إضافة المهام إلى القائمة الحالية.`
      );

      if (!confirmImport) return;

      // إضافة المهام المستوردة
      for (const taskData of importedTasks) {
        const newTask = {
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          text: taskData.text,
          completed: taskData.completed || false,
          category: taskData.category || 'عام',
          priority: taskData.priority || 'medium',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          dueDate: taskData.dueDate || '',
          notes: taskData.notes || ''
        };

        tasks.push(newTask);
        await db.addTask(newTask);
      }

      // إعادة عرض المهام
      renderTasks();
      updateStats();

      showNotification(`تم استيراد ${importedTasks.length} مهمة بنجاح من ملف ${format.toUpperCase()}`, 'success');

    } catch (error) {
      console.error('خطأ في استيراد البيانات:', error);
      showNotification('حدث خطأ أثناء استيراد البيانات: ' + error.message, 'error');
    }
  };

  reader.onerror = () => {
    showNotification('حدث خطأ أثناء قراءة الملف', 'error');
  };

  reader.readAsText(file);
}

// دوال تحليل الملفات المختلفة
function parseJSONImport(content) {
  const data = JSON.parse(content);

  // إذا كان الملف يحتوي على بنية TDL كاملة
  if (data.tasks && Array.isArray(data.tasks)) {
    return data.tasks;
  }

  // إذا كان الملف مصفوفة مهام مباشرة
  if (Array.isArray(data)) {
    return data;
  }

  throw new Error('تنسيق JSON غير صالح');
}

function parseCSVImport(content) {
  const lines = content.split('\n').filter(line => line.trim());
  if (lines.length < 2) {
    throw new Error('ملف CSV فارغ أو غير صالح');
  }

  // تجاهل السطر الأول (العناوين)
  const dataLines = lines.slice(1);
  const tasks = [];

  for (const line of dataLines) {
    const columns = parseCSVLine(line);
    if (columns.length >= 2) {
      tasks.push({
        text: columns[1] ? columns[1].replace(/^"|"$/g, '').replace(/""/g, '"') : '',
        completed: columns[2] === 'نعم' || columns[2] === 'true',
        category: columns[3] || 'عام',
        priority: columns[4] || 'medium',
        dueDate: columns[6] || '',
        notes: columns[7] ? columns[7].replace(/^"|"$/g, '').replace(/""/g, '"') : ''
      });
    }
  }

  return tasks;
}

function parseCSVLine(line) {
  const result = [];
  let current = '';
  let inQuotes = false;

  for (let i = 0; i < line.length; i++) {
    const char = line[i];

    if (char === '"') {
      if (inQuotes && line[i + 1] === '"') {
        current += '"';
        i++; // تجاهل الاقتباس المزدوج التالي
      } else {
        inQuotes = !inQuotes;
      }
    } else if (char === ',' && !inQuotes) {
      result.push(current);
      current = '';
    } else {
      current += char;
    }
  }

  result.push(current);
  return result;
}

function parseTXTImport(content) {
  const lines = content.split('\n');
  const tasks = [];
  let currentTask = null;

  for (const line of lines) {
    const trimmedLine = line.trim();

    // تجاهل الخطوط الفارغة والفواصل
    if (!trimmedLine || trimmedLine.startsWith('=') || trimmedLine.startsWith('-')) {
      continue;
    }

    // تجاهل خطوط المعلومات العامة
    if (trimmedLine.includes('قائمة المهام') ||
        trimmedLine.includes('تاريخ التصدير') ||
        trimmedLine.includes('عدد المهام')) {
      continue;
    }

    // البحث عن بداية مهمة جديدة (رقم. نص)
    const taskMatch = trimmedLine.match(/^(\d+)\.\s*(.+)$/);
    if (taskMatch) {
      // حفظ المهمة السابقة إذا كانت موجودة
      if (currentTask) {
        tasks.push(currentTask);
      }

      // بدء مهمة جديدة
      currentTask = {
        text: taskMatch[2],
        completed: false,
        category: 'عام',
        priority: 'medium',
        dueDate: '',
        notes: ''
      };
    } else if (currentTask) {
      // معالجة خصائص المهمة
      if (trimmedLine.includes('الحالة:')) {
        currentTask.completed = trimmedLine.includes('مكتملة');
      } else if (trimmedLine.includes('التصنيف:')) {
        currentTask.category = trimmedLine.split(':')[1]?.trim() || 'عام';
      } else if (trimmedLine.includes('الأولوية:')) {
        currentTask.priority = trimmedLine.split(':')[1]?.trim() || 'medium';
      } else if (trimmedLine.includes('ملاحظات:')) {
        currentTask.notes = trimmedLine.split(':')[1]?.trim() || '';
      }
    }
  }

  // إضافة المهمة الأخيرة
  if (currentTask) {
    tasks.push(currentTask);
  }

  return tasks;
}

// مسح المهام المكتملة (محسن وديناميكي)
async function clearCompletedTasks() {
  // الحصول على المهام المكتملة الحالية
  const completedTasks = tasks.filter(task => task.completed);

  if (completedTasks.length === 0) {
    showNotification('لا توجد مهام مكتملة لحذفها', 'info');
    return;
  }

  // رسالة تأكيد مفصلة
  const confirmMessage = `هل تريد حذف ${completedTasks.length} مهمة مكتملة؟\n\n` +
    `المهام التي سيتم حذفها:\n` +
    completedTasks.slice(0, 3).map(task => `• ${task.text}`).join('\n') +
    (completedTasks.length > 3 ? `\n... و ${completedTasks.length - 3} مهمة أخرى` : '') +
    `\n\nهذا الإجراء لا يمكن التراجع عنه.`;

  const confirmDelete = confirm(confirmMessage);
  if (!confirmDelete) return;

  try {
    // إظهار رسالة تحميل
    showNotification('جاري حذف المهام المكتملة...', 'info');

    // حذف المهام من قاعدة البيانات والمصفوفة
    for (const task of completedTasks) {
      await db.deleteTask(task.id);
      const taskIndex = tasks.findIndex(t => t.id === task.id);
      if (taskIndex !== -1) {
        tasks.splice(taskIndex, 1);
      }
    }

    // إعادة عرض المهام وتحديث الإحصائيات
    renderTasks();
    updateStats();

    showNotification(`تم حذف ${completedTasks.length} مهمة مكتملة بنجاح`, 'success');
  } catch (error) {
    console.error('خطأ في حذف المهام المكتملة:', error);
    showNotification('حدث خطأ أثناء حذف المهام المكتملة', 'error');
  }
}

// إنشاء نسخة احتياطية بصيغ متعددة
function createBackup(format = 'json') {
  try {
    const timestamp = new Date().toISOString().split('T')[0];
    let filename, content, mimeType;

    const backupData = {
      tasks: tasks,
      settings: settings,
      backupDate: new Date().toISOString(),
      version: '1.0',
      type: 'auto-backup'
    };

    switch (format) {
      case 'json':
        content = JSON.stringify(backupData, null, 2);
        filename = `tdl-backup-${timestamp}.json`;
        mimeType = 'application/json';

        // حفظ النسخة الاحتياطية في التخزين المحلي أيضاً
        localStorage.setItem('tdl-backup', JSON.stringify(backupData));
        break;

      case 'csv':
        content = convertTasksToCSV(tasks);
        filename = `tdl-backup-${timestamp}.csv`;
        mimeType = 'text/csv';
        break;

      case 'txt':
        content = convertTasksToTXT(tasks);
        filename = `tdl-backup-${timestamp}.txt`;
        mimeType = 'text/plain';
        break;

      default:
        throw new Error('صيغة غير مدعومة');
    }

    const dataBlob = new Blob([content], { type: mimeType });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = filename;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showNotification(`تم إنشاء النسخة الاحتياطية بصيغة ${format.toUpperCase()} بنجاح`, 'success');
  } catch (error) {
    console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
    showNotification('حدث خطأ أثناء إنشاء النسخة الاحتياطية', 'error');
  }
}

// ========== تهيئة التطبيق ==========
// استدعاء دالة التهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  // تهيئة Service Worker
  initServiceWorker();
  
  // تهيئة التطبيق
  initApp();
});

// تصدير الدوال والمتغيرات اللازمة
export {
  initApp,
  addTask,
  updateTask,
  deleteTask,
  toggleTaskCompletion,
  editTask,
  toggleDarkMode,
  showCustomDeleteConfirmation,
  exportTasks,
  importTasks,
  clearCompletedTasks,
  createBackup
};