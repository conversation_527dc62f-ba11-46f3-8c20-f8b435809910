{"name": "tdl-advanced-todo", "version": "1.0.0", "description": "تطبيق مهام متقدم مع واجهة عصرية وميزات متقدمة", "main": "index.html", "scripts": {"start": "python -m http.server 8000", "dev": "python -m http.server 8000", "serve": "python -m http.server 8000"}, "keywords": ["todo", "tasks", "productivity", "arabic", "rtl", "modern-ui", "dark-mode", "responsive"], "author": "TDL Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tdl-team/advanced-todo-app.git"}, "bugs": {"url": "https://github.com/tdl-team/advanced-todo-app/issues"}, "homepage": "https://github.com/tdl-team/advanced-todo-app#readme", "dependencies": {}, "devDependencies": {}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "engines": {"node": ">=14.0.0"}}