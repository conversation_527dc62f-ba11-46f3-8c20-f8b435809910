<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - تطبيق المهام المتقدم</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 25%, #232946 50%, #1a1f2e 75%, #0f1419 100%);
            font-family: 'Cairo', sans-serif;
            position: relative;
            z-index: 0;
            overflow: hidden;
        }

        /* خلفية متحركة متقدمة */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 15% 85%, rgba(102, 126, 234, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 85% 15%, rgba(118, 75, 162, 0.12) 0%, transparent 50%),
                radial-gradient(circle at 45% 45%, rgba(120, 219, 255, 0.10) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255, 107, 107, 0.08) 0%, transparent 50%);
            animation: backgroundShift 25s ease-in-out infinite;
            z-index: 1;
        }

        @keyframes backgroundShift {
            0%, 100% { 
                transform: translate(0, 0) rotate(0deg) scale(1); 
                opacity: 1;
            }
            25% { 
                transform: translate(-15px, -15px) rotate(2deg) scale(1.02); 
                opacity: 0.8;
            }
            50% { 
                transform: translate(15px, -8px) rotate(-2deg) scale(0.98); 
                opacity: 1;
            }
            75% { 
                transform: translate(-8px, 15px) rotate(1deg) scale(1.01); 
                opacity: 0.9;
            }
        }

        /* جزيئات متحركة متقدمة */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 2;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 3px;
            height: 3px;
            background: linear-gradient(45deg, rgba(102, 126, 234, 0.8), rgba(120, 219, 255, 0.6));
            border-radius: 50%;
            animation: float 8s ease-in-out infinite;
            z-index: 2;
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
            pointer-events: none;
        }

        .particle:nth-child(1) { left: 10%; animation-delay: 0s; animation-duration: 7s; }
        .particle:nth-child(2) { left: 20%; animation-delay: 1s; animation-duration: 9s; }
        .particle:nth-child(3) { left: 30%; animation-delay: 2s; animation-duration: 6s; }
        .particle:nth-child(4) { left: 40%; animation-delay: 3s; animation-duration: 8s; }
        .particle:nth-child(5) { left: 50%; animation-delay: 4s; animation-duration: 10s; }
        .particle:nth-child(6) { left: 60%; animation-delay: 5s; animation-duration: 7s; }
        .particle:nth-child(7) { left: 70%; animation-delay: 0s; animation-duration: 9s; }
        .particle:nth-child(8) { left: 80%; animation-delay: 1s; animation-duration: 6s; }
        .particle:nth-child(9) { left: 90%; animation-delay: 2s; animation-duration: 8s; }
        .particle:nth-child(10) { left: 15%; animation-delay: 3s; animation-duration: 7s; }
        .particle:nth-child(11) { left: 25%; animation-delay: 4s; animation-duration: 9s; }
        .particle:nth-child(12) { left: 35%; animation-delay: 5s; animation-duration: 6s; }

        @keyframes float {
            0%, 100% { 
                transform: translateY(100vh) rotate(0deg) scale(0.5); 
                opacity: 0; 
            }
            10% { 
                opacity: 1; 
                transform: translateY(90vh) rotate(36deg) scale(1);
            }
            90% { 
                opacity: 1; 
                transform: translateY(10vh) rotate(324deg) scale(1);
            }
            100% { 
                transform: translateY(-100px) rotate(360deg) scale(0.5); 
                opacity: 0; 
            }
        }

        /* تأثيرات إضافية للخلفية */
        .background-effects {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            pointer-events: none;
        }

        .bg-circle {
            position: absolute;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
            animation: bgFloat 20s ease-in-out infinite;
            pointer-events: none;
        }

        .bg-circle:nth-child(1) {
            width: 300px;
            height: 300px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .bg-circle:nth-child(2) {
            width: 200px;
            height: 200px;
            top: 60%;
            right: 15%;
            animation-delay: 5s;
        }

        .bg-circle:nth-child(3) {
            width: 250px;
            height: 250px;
            bottom: 20%;
            left: 20%;
            animation-delay: 10s;
        }

        @keyframes bgFloat {
            0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.3; }
            50% { transform: translate(30px, -30px) scale(1.1); opacity: 0.6; }
        }

        /* تأثيرات إضافية للخلفية */
        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            pointer-events: none;
        }

        .floating-shape {
            position: absolute;
            opacity: 0.1;
            animation: shapeFloat 15s ease-in-out infinite;
        }

        .floating-shape:nth-child(1) {
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 20px;
            top: 20%;
            left: 5%;
            animation-delay: 0s;
        }

        .floating-shape:nth-child(2) {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #78dce8, #667eea);
            border-radius: 50%;
            top: 70%;
            right: 10%;
            animation-delay: 5s;
        }

        .floating-shape:nth-child(3) {
            width: 120px;
            height: 60px;
            background: linear-gradient(45deg, #f093fb, #f5576c);
            border-radius: 30px;
            bottom: 30%;
            left: 15%;
            animation-delay: 10s;
        }

        @keyframes shapeFloat {
            0%, 100% { transform: translate(0, 0) rotate(0deg); opacity: 0.1; }
            50% { transform: translate(20px, -20px) rotate(180deg); opacity: 0.3; }
        }

        .auth-container {
            width: 100%;
            max-width: 480px;
            background: rgba(26, 31, 46, 0.85);
            backdrop-filter: blur(32px) saturate(180%);
            -webkit-backdrop-filter: blur(32px) saturate(180%);
            padding: 2.5rem 2rem;
            border-radius: 32px;
            box-shadow: 
                0 20px 60px 0 rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(102, 126, 234, 0.15),
                0 8px 32px rgba(102, 126, 234, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 1000;
            transform: translateY(30px);
            opacity: 0;
            animation: slideUp 1s cubic-bezier(0.4, 0, 0.2, 1) forwards;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(102, 126, 234, 0.2);
            margin: 2rem;
        }

        @keyframes slideUp {
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .auth-container:focus-within, .auth-container:hover {
            box-shadow: 
                0 25px 80px 0 rgba(0, 0, 0, 0.5),
                0 0 0 2px rgba(102, 126, 234, 0.25),
                0 12px 40px rgba(102, 126, 234, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.15);
            transform: translateY(-5px);
        }

        .auth-header {
            text-align: center;
            margin-bottom: 2rem;
            position: relative;
            z-index: 1001;
        }

        .auth-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #232946 100%);
            border-radius: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2.2rem;
            color: #fff;
            box-shadow: 
                0 12px 40px rgba(102, 126, 234, 0.3),
                0 0 0 1px rgba(102, 126, 234, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            animation: logoFloat 4s ease-in-out infinite;
            border: 2px solid rgba(102, 126, 234, 0.3);
            position: relative;
            z-index: 1001;
            transition: all 0.3s ease;
        }

        .auth-logo:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 
                0 20px 60px rgba(102, 126, 234, 0.4),
                0 0 0 2px rgba(102, 126, 234, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .auth-logo i {
            text-shadow: 0 2px 12px rgba(102, 126, 234, 0.6);
            filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.3));
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-8px) rotate(2deg); }
        }

        .auth-title {
            font-size: 2rem;
            font-weight: 800;
            color: #f8fafc;
            margin-bottom: 0.5rem;
            letter-spacing: 0.5px;
            position: relative;
            z-index: 1001;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .auth-subtitle {
            color: #94a3b8;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.6;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 1001;
        }
        
        .form-group label {
            color: #cbd5e1;
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            font-size: 1rem;
            letter-spacing: 0.3px;
        }

        .input-container {
            position: relative;
            z-index: 1001;
        }
        
        .input-container i {
            color: #667eea;
            opacity: 0.9;
            filter: drop-shadow(0 2px 6px rgba(102, 126, 234, 0.3));
            position: absolute;
            right: 1.2rem;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1002;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }

        .form-input {
            width: 100%;
            padding: 1rem 3rem 1rem 1rem;
            border: 2px solid rgba(51, 65, 85, 0.6);
            border-radius: 18px;
            font-size: 1rem;
            background: rgba(30, 41, 59, 0.8);
            color: #f1f5f9;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-family: 'Cairo', sans-serif;
            box-shadow: 
                0 4px 16px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.05);
            position: relative;
            z-index: 1001;
            backdrop-filter: blur(8px);
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 
                0 0 0 4px rgba(102, 126, 234, 0.15),
                0 8px 24px rgba(102, 126, 234, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            background: rgba(30, 41, 59, 0.95);
            color: #fff;
            transform: translateY(-2px);
        }

        .form-input:focus + i {
            color: #818cf8;
            transform: translateY(-50%) scale(1.1);
        }

        .form-input::placeholder {
            color: #64748b;
            font-weight: 400;
        }

        .auth-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
            color: #fff;
            font-weight: 700;
            font-size: 1rem;
            border-radius: 18px;
            box-shadow: 
                0 8px 32px rgba(102, 126, 234, 0.3),
                0 2px 0 rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border: none;
            position: relative;
            z-index: 1001;
            width: 100%;
            padding: 1rem;
            margin-top: 1rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-family: 'Cairo', sans-serif;
            letter-spacing: 0.5px;
            overflow: hidden;
        }

        .auth-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .auth-btn:hover::before {
            left: 100%;
        }

        .auth-btn:hover, .auth-btn:focus {
            background: linear-gradient(135deg, #818cf8 0%, #8b5cf6 50%, #818cf8 100%);
            color: #fff;
            box-shadow: 
                0 12px 40px rgba(102, 126, 234, 0.4),
                0 4px 0 rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            transform: translateY(-3px);
        }

        .auth-btn:active {
            transform: translateY(-1px);
            box-shadow: 
                0 6px 20px rgba(102, 126, 234, 0.3),
                0 2px 0 rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .auth-switch {
            border-top: 2px solid rgba(51, 65, 85, 0.4);
            padding-top: 1.5rem;
            margin-top: 1.5rem;
            text-align: center;
            position: relative;
            z-index: 1001;
        }

        .auth-switch span {
            color: #94a3b8;
            font-size: 1rem;
            margin-left: 0.5rem;
        }

        .switch-btn {
            color: #667eea;
            background: none;
            border: none;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            padding: 0.5rem 1rem;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            position: relative;
            overflow: hidden;
        }

        .switch-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .switch-btn:hover::before {
            opacity: 1;
        }

        .switch-btn:hover {
            color: #818cf8;
            transform: translateY(-2px);
        }

        .switch-btn:active {
            transform: translateY(0);
        }

        /* تأثيرات إضافية للتفاعل */
        .form-input:valid {
            border-color: rgba(34, 197, 94, 0.5);
        }

        .form-input:invalid:not(:placeholder-shown) {
            border-color: rgba(239, 68, 68, 0.5);
        }

        /* تأثيرات التحميل */
        .loading-effect {
            position: relative;
            overflow: hidden;
        }

        .loading-effect::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* تأثيرات النبض */
        .pulse-effect {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* تأثيرات التوهج */
        .glow-effect {
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
        }

        .glow-effect:hover {
            box-shadow: 0 0 30px rgba(102, 126, 234, 0.8);
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .auth-container {
                margin: 1rem;
                padding: 2rem 1.5rem;
                border-radius: 24px;
                max-width: calc(100vw - 2rem);
            }

            .auth-logo {
                width: 70px;
                height: 70px;
                font-size: 2rem;
            }

            .auth-title {
                font-size: 1.8rem;
            }

            .auth-subtitle {
                font-size: 0.95rem;
            }

            .form-input {
                padding: 0.9rem 2.8rem 0.9rem 0.9rem;
                font-size: 0.95rem;
            }
        }

        /* تحسين الأداء */
        .auth-container * {
            will-change: transform, opacity;
        }

        /* تأثيرات إضافية للخلفية */
        .neon-glow {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            animation: neonPulse 3s ease-in-out infinite;
            z-index: 0;
            pointer-events: none;
        }

        @keyframes neonPulse {
            0%, 100% { 
                transform: translate(-50%, -50%) scale(1); 
                opacity: 0.3; 
            }
            50% { 
                transform: translate(-50%, -50%) scale(1.2); 
                opacity: 0.6; 
            }
        }

        /* تأثيرات النص المتوهج */
        .glow-text {
            text-shadow: 
                0 0 10px rgba(102, 126, 234, 0.5),
                0 0 20px rgba(102, 126, 234, 0.3),
                0 0 30px rgba(102, 126, 234, 0.1);
        }

        /* تأثيرات الحدود المتوهجة */
        .glow-border {
            border: 2px solid rgba(102, 126, 234, 0.3);
            box-shadow: 
                0 0 10px rgba(102, 126, 234, 0.3),
                inset 0 0 10px rgba(102, 126, 234, 0.1);
        }

        .glow-border:focus {
            border-color: rgba(102, 126, 234, 0.6);
            box-shadow: 
                0 0 20px rgba(102, 126, 234, 0.5),
                inset 0 0 20px rgba(102, 126, 234, 0.2);
        }
    </style>
</head>
<body>
    <!-- تأثيرات الخلفية -->
    <div class="background-effects">
        <div class="bg-circle"></div>
        <div class="bg-circle"></div>
        <div class="bg-circle"></div>
    </div>

    <!-- أشكال متحركة */
    <div class="floating-shapes">
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
    </div>

    <!-- جزيئات متحركة -->
    <div class="particles">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>

    <!-- تأثير النيون المتوهج -->
    <div class="neon-glow"></div>

    <div class="auth-container">
        <div class="auth-header">
            <div class="auth-logo pulse-effect">
                <i class="fas fa-tasks glow-text"></i>
            </div>
            <h1 class="auth-title glow-text" id="formTitle">تسجيل الدخول</h1>
            <p class="auth-subtitle">مرحباً بك في تطبيق المهام المتقدم</p>
        </div>

        <form id="loginForm" style="position: relative; z-index: 1001;">
            <div class="form-group">
                <label for="loginUsername">اسم المستخدم</label>
                <div class="input-container">
                    <input type="text" id="loginUsername" class="form-input glow-border" placeholder="أدخل اسم المستخدم" required>
                    <i class="fas fa-user"></i>
                </div>
            </div>
            <div class="form-group">
                <label for="loginPassword">كلمة المرور</label>
                <div class="input-container">
                    <input type="password" id="loginPassword" class="form-input glow-border" placeholder="أدخل كلمة المرور" required>
                    <i class="fas fa-lock"></i>
                </div>
            </div>
            <button type="submit" class="auth-btn glow-effect">
                <i class="fas fa-sign-in-alt"></i>
                تسجيل الدخول
            </button>
        </form>

        <form id="registerForm" style="display:none; position: relative; z-index: 1001;">
            <div class="form-group">
                <label for="registerUsername">اسم المستخدم</label>
                <div class="input-container">
                    <input type="text" id="registerUsername" class="form-input glow-border" placeholder="أدخل اسم المستخدم" required>
                    <i class="fas fa-user"></i>
                </div>
            </div>
            <div class="form-group">
                <label for="registerPassword">كلمة المرور</label>
                <div class="input-container">
                    <input type="password" id="registerPassword" class="form-input glow-border" placeholder="أدخل كلمة المرور" required>
                    <i class="fas fa-lock"></i>
                </div>
            </div>
            <div class="form-group">
                <label for="registerPasswordConfirm">تأكيد كلمة المرور</label>
                <div class="input-container">
                    <input type="password" id="registerPasswordConfirm" class="form-input glow-border" placeholder="أعد إدخال كلمة المرور" required>
                    <i class="fas fa-lock"></i>
                </div>
            </div>
            <button type="submit" class="auth-btn glow-effect">
                <i class="fas fa-user-plus"></i>
                إنشاء حساب
            </button>
        </form>

        <div class="auth-switch">
            <span id="switchText">ليس لديك حساب؟</span>
            <button id="switchBtn" class="switch-btn">إنشاء حساب</button>
        </div>

        <!-- معلومات حساب المدير -->
        <div class="admin-info" style="margin-top: 2rem; padding: 1.5rem; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 20px; border: 1px solid rgba(102, 126, 234, 0.3); position: relative; z-index: 1001;">
            <div style="text-align: center; margin-bottom: 1rem;">
                <i class="fas fa-crown" style="color: #fbbf24; font-size: 1.5rem; margin-bottom: 0.5rem;"></i>
                <h3 style="color: #e2e8f0; font-size: 1.2rem; margin: 0; font-weight: 700;">حساب المدير</h3>
            </div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                <div style="background: rgba(255, 255, 255, 0.05); padding: 1rem; border-radius: 12px; text-align: center;">
                    <div style="color: #94a3b8; font-size: 0.9rem; margin-bottom: 0.3rem;">اسم المستخدم</div>
                    <div style="color: #e2e8f0; font-weight: 600; font-size: 1.1rem;">kha</div>
                </div>
                <div style="background: rgba(255, 255, 255, 0.05); padding: 1rem; border-radius: 12px; text-align: center;">
                    <div style="color: #94a3b8; font-size: 0.9rem; margin-bottom: 0.3rem;">كلمة المرور</div>
                    <div style="color: #e2e8f0; font-weight: 600; font-size: 1.1rem;">kha/admin</div>
                </div>
            </div>
            <div style="text-align: center;">
                <button onclick="quickLogin()" style="background: linear-gradient(135deg, #fbbf24, #f59e0b); color: #1f2937; border: none; padding: 0.8rem 1.5rem; border-radius: 12px; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 0.9rem;">
                    <i class="fas fa-bolt" style="margin-left: 0.5rem;"></i>
                    دخول سريع كمدير
                </button>
            </div>
        </div>
    </div>

    <script type="module" src="js/auth-interface.js"></script>

    <script>
        // دالة الدخول السريع للمدير
        function quickLogin() {
            document.getElementById('loginUsername').value = 'kha';
            document.getElementById('loginPassword').value = 'kha/admin';

            // إضافة تأثير بصري
            const button = event.target;
            button.style.transform = 'scale(0.95)';
            button.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-left: 0.5rem;"></i>جاري تسجيل الدخول...';

            setTimeout(() => {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }, 500);
        }
    </script>
</body>
</html> 