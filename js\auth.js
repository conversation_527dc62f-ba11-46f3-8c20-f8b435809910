// auth.js - وحدة إدارة المصادقة للموقع
import { showNotification } from './utils.js';

// الحصول على المستخدمين من التخزين المحلي
function getUsers() {
    try {
        return JSON.parse(localStorage.getItem('users')) || [];
    } catch {
        return [];
    }
}

// حفظ المستخدمين في التخزين المحلي
function saveUsers(users) {
    localStorage.setItem('users', JSON.stringify(users));
}

// دالة مساعدة للتشفير البسيط لكلمة المرور (لأغراض العرض فقط، ليست للحماية الحقيقية)
function hash(password) {
    return btoa(password);
}

// التحقق من تسجيل الدخول
export function isLoggedIn() {
    return Boolean(localStorage.getItem('currentUser'));
}

// فرض وجود جلسة تسجيل دخول، وإعادة التوجيه لصفحة تسجيل الدخول إذا لم يكن المستخدم مسجلاً
export function requireAuth() {
    const onLoginPage = window.location.pathname.endsWith('login.html');
    if (!onLoginPage && !isLoggedIn()) {
        window.location.href = 'login.html';
    }
}

// تسجيل الخروج
export function logout() {
    localStorage.removeItem('currentUser');
    showNotification('تم تسجيل الخروج بنجاح', 'success', 'center');
    setTimeout(() => {
        window.location.href = 'login.html';
    }, 800);
}

// تأثيرات بصرية محسنة للنماذج
function addFormEffects() {
    const inputs = document.querySelectorAll('.form-input');
    const buttons = document.querySelectorAll('.auth-btn');
    
    // تأثيرات للحقول
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
        
        // تأثير الكتابة
        input.addEventListener('input', function() {
            if (this.value.length > 0) {
                this.style.borderColor = 'rgba(34, 197, 94, 0.5)';
            } else {
                this.style.borderColor = 'rgba(51, 65, 85, 0.6)';
            }
        });
    });
    
    // تأثيرات للأزرار
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px) scale(1.02)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
        
        button.addEventListener('mousedown', function() {
            this.style.transform = 'translateY(-1px) scale(0.98)';
        });
        
        button.addEventListener('mouseup', function() {
            this.style.transform = 'translateY(-3px) scale(1.02)';
        });
    });
}

// تحسين تجربة المستخدم مع الرسائل
function showEnhancedNotification(message, type = 'info', position = 'center') {
    // إضافة تأثيرات صوتية بصرية
    const container = document.querySelector('.auth-container');
    if (container) {
        container.style.animation = 'none';
        setTimeout(() => {
            container.style.animation = 'slideUp 0.3s ease-out';
        }, 10);
    }
    
    showNotification(message, type, position);
}

// معالجة تسجيل الدخول محسنة
function handleLogin(e) {
    e.preventDefault();
    const username = document.getElementById('loginUsername').value.trim();
    const password = document.getElementById('loginPassword').value;
    const submitBtn = e.target.querySelector('.auth-btn');

    if (!username || !password) {
        showEnhancedNotification('يرجى إدخال اسم المستخدم وكلمة المرور', 'warning', 'center');
        return;
    }

    // تأثير التحميل
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الدخول...';
    submitBtn.disabled = true;

    // محاكاة تأخير الشبكة
    setTimeout(() => {
        const users = getUsers();
        const user = users.find(u => u.username === username && u.password === hash(password));

        if (user) {
            localStorage.setItem('currentUser', username);
            submitBtn.innerHTML = '<i class="fas fa-check"></i> تم تسجيل الدخول بنجاح';
            submitBtn.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
            
            showEnhancedNotification('مرحباً بك مرة أخرى!', 'success', 'center');
            
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1000);
        } else {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            submitBtn.style.background = 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)';
            
            setTimeout(() => {
                submitBtn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%)';
            }, 2000);
            
            showEnhancedNotification('بيانات الدخول غير صحيحة، يرجى المحاولة مرة أخرى', 'error', 'center');
        }
    }, 800);
}

// معالجة إنشاء حساب جديد محسنة
function handleRegister(e) {
    e.preventDefault();
    const username = document.getElementById('registerUsername').value.trim();
    const password = document.getElementById('registerPassword').value;
    const confirm = document.getElementById('registerPasswordConfirm').value;
    const submitBtn = e.target.querySelector('.auth-btn');

    if (!username || !password) {
        showEnhancedNotification('جميع الحقول مطلوبة', 'warning', 'center');
        return;
    }

    if (password !== confirm) {
        showEnhancedNotification('كلمتا المرور غير متطابقتين', 'error', 'center');
        return;
    }

    if (password.length < 6) {
        showEnhancedNotification('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'warning', 'center');
        return;
    }

    // تأثير التحميل
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري إنشاء الحساب...';
    submitBtn.disabled = true;

    setTimeout(() => {
        const users = getUsers();
        if (users.some(u => u.username === username)) {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            showEnhancedNotification('اسم المستخدم موجود بالفعل، يرجى اختيار اسم آخر', 'error', 'center');
            return;
        }

        users.push({ username, password: hash(password) });
        saveUsers(users);
        
        submitBtn.innerHTML = '<i class="fas fa-check"></i> تم إنشاء الحساب بنجاح';
        submitBtn.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
        
        showEnhancedNotification('تم إنشاء الحساب بنجاح! يمكنك تسجيل الدخول الآن', 'success', 'center');
        
        setTimeout(() => {
            toggleForms();
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            submitBtn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%)';
        }, 2000);
    }, 1000);
}

// تبديل العرض بين نموذج الدخول ونموذج التسجيل محسن
function toggleForms() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const formTitle = document.getElementById('formTitle');
    const switchText = document.getElementById('switchText');
    const switchBtn = document.getElementById('switchBtn');
    const container = document.querySelector('.auth-container');

    const showingLogin = loginForm.style.display !== 'none';

    // تأثير الانتقال
    container.style.transform = 'translateY(20px)';
    container.style.opacity = '0.7';

    setTimeout(() => {
        if (showingLogin) {
            loginForm.style.display = 'none';
            registerForm.style.display = 'block';
            formTitle.textContent = 'إنشاء حساب';
            switchText.textContent = 'لديك حساب بالفعل؟';
            switchBtn.textContent = 'تسجيل الدخول';
        } else {
            registerForm.style.display = 'none';
            loginForm.style.display = 'block';
            formTitle.textContent = 'تسجيل الدخول';
            switchText.textContent = 'ليس لديك حساب؟';
            switchBtn.textContent = 'إنشاء حساب';
        }

        container.style.transform = 'translateY(0)';
        container.style.opacity = '1';
    }, 300);
}

// إضافة حساب افتراضي (admin / admin) إذا لم يكن موجوداً
function ensureDefaultAdmin() {
    const users = getUsers();
    const exists = users.some(u => u.username === 'admin');
    if (!exists) {
        users.push({ username: 'admin', password: hash('admin') });
        saveUsers(users);
        console.info('[Auth] تم إنشاء حساب المسؤول الافتراضي (admin / admin)');
    }
}

// تحسين تجربة المستخدم مع التحقق من صحة الحقول
function addFieldValidation() {
    const passwordInputs = document.querySelectorAll('input[type="password"]');
    
    passwordInputs.forEach(input => {
        input.addEventListener('input', function() {
            const strength = calculatePasswordStrength(this.value);
            updatePasswordStrengthIndicator(this, strength);
        });
    });
}

// حساب قوة كلمة المرور
function calculatePasswordStrength(password) {
    let strength = 0;
    if (password.length >= 6) strength += 25;
    if (password.length >= 8) strength += 25;
    if (/[a-z]/.test(password)) strength += 25;
    if (/[A-Z]/.test(password)) strength += 25;
    if (/[0-9]/.test(password)) strength += 25;
    if (/[^A-Za-z0-9]/.test(password)) strength += 25;
    
    return Math.min(strength, 100);
}

// تحديث مؤشر قوة كلمة المرور
function updatePasswordStrengthIndicator(input, strength) {
    let color = '#ef4444'; // أحمر
    if (strength >= 50) color = '#f59e0b'; // برتقالي
    if (strength >= 75) color = '#10b981'; // أخضر
    
    input.style.borderColor = color;
    input.style.boxShadow = `0 0 0 2px ${color}33`;
}

// ربط الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // إنشاء حساب المدير الافتراضي
    ensureDefaultAdmin();
    
    // حماية الصفحات المحمية
    requireAuth();

    // إضافة التأثيرات البصرية
    addFormEffects();
    addFieldValidation();

    // نماذج الدخول والتسجيل
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const switchBtn = document.getElementById('switchBtn');

    if (loginForm) loginForm.addEventListener('submit', handleLogin);
    if (registerForm) registerForm.addEventListener('submit', handleRegister);
    if (switchBtn) switchBtn.addEventListener('click', (e) => { 
        e.preventDefault(); 
        toggleForms(); 
    });

    // زر تسجيل الخروج داخل القائمة الجانبية (إن وجد)
    const logoutMenu = document.getElementById('logoutMenu');
    if (logoutMenu) {
        logoutMenu.addEventListener('click', (e) => {
            e.preventDefault();
            logout();
        });
    }

    // تحسين تجربة المستخدم مع التحميل
    const container = document.querySelector('.auth-container');
    if (container) {
        container.style.animation = 'slideUp 1s cubic-bezier(0.4, 0, 0.2, 1) forwards';
    }

    // إضافة تأثيرات للجزيئات
    const particles = document.querySelectorAll('.particle');
    particles.forEach((particle, index) => {
        particle.style.animationDelay = `${index * 0.5}s`;
    });
}); 