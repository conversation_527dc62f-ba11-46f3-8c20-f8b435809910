// TDL V1.0 - Enhanced Authentication System
// نظام المصادقة المحسن والمتطور

console.log('🚀 تحميل نظام المصادقة المحسن...');

class EnhancedAuth {
    constructor() {
        this.users = this.loadUsers();
        this.currentUser = null;
        this.sessionTimeout = 24 * 60 * 60 * 1000; // 24 ساعة
        this.init();
    }

    // تهيئة النظام
    init() {
        console.log('⚙️ تهيئة نظام المصادقة...');
        this.createDefaultAccounts();
        this.setupEventListeners();
        this.checkExistingSession();
    }

    // إنشاء الحسابات الافتراضية
    createDefaultAccounts() {
        console.log('🔧 إنشاء الحسابات الافتراضية...');
        
        // حساب المدير kha
        if (!this.users['kha']) {
            this.users['kha'] = {
                username: 'kha',
                password: this.hashPassword('kha/admin'),
                fullName: 'خالد - المدير العام',
                email: '<EMAIL>',
                avatar: 'KHA',
                role: 'admin',
                isActive: true,
                createdAt: new Date().toISOString(),
                lastLogin: null,
                settings: {
                    theme: 'dark',
                    language: 'ar',
                    notifications: true
                },
                tasks: [],
                stats: {
                    totalTasks: 0,
                    completedTasks: 0,
                    loginCount: 0
                }
            };
            console.log('✅ تم إنشاء حساب المدير: kha');
        }

        // حساب المدير admin
        if (!this.users['admin']) {
            this.users['admin'] = {
                username: 'admin',
                password: this.hashPassword('admin123'),
                fullName: 'مدير النظام',
                email: '<EMAIL>',
                avatar: 'ADM',
                role: 'admin',
                isActive: true,
                createdAt: new Date().toISOString(),
                lastLogin: null,
                settings: {
                    theme: 'dark',
                    language: 'ar',
                    notifications: true
                },
                tasks: [],
                stats: {
                    totalTasks: 0,
                    completedTasks: 0,
                    loginCount: 0
                }
            };
            console.log('✅ تم إنشاء حساب المدير: admin');
        }

        // حساب مستخدم تجريبي
        if (!this.users['user']) {
            this.users['user'] = {
                username: 'user',
                password: this.hashPassword('user123'),
                fullName: 'مستخدم تجريبي',
                email: '<EMAIL>',
                avatar: 'USR',
                role: 'user',
                isActive: true,
                createdAt: new Date().toISOString(),
                lastLogin: null,
                settings: {
                    theme: 'dark',
                    language: 'ar',
                    notifications: true
                },
                tasks: [],
                stats: {
                    totalTasks: 0,
                    completedTasks: 0,
                    loginCount: 0
                }
            };
            console.log('✅ تم إنشاء حساب المستخدم: user');
        }

        this.saveUsers();
    }

    // تحميل المستخدمين
    loadUsers() {
        try {
            const users = localStorage.getItem('tdl_users');
            const result = users ? JSON.parse(users) : {};
            console.log('📂 تحميل المستخدمين:', Object.keys(result).length, 'مستخدم');
            return result;
        } catch (error) {
            console.error('❌ خطأ في تحميل المستخدمين:', error);
            return {};
        }
    }

    // حفظ المستخدمين
    saveUsers() {
        try {
            localStorage.setItem('tdl_users', JSON.stringify(this.users));
            console.log('💾 تم حفظ المستخدمين بنجاح');
            return true;
        } catch (error) {
            console.error('❌ خطأ في حفظ المستخدمين:', error);
            return false;
        }
    }

    // تشفير كلمة المرور
    hashPassword(password) {
        let hash = 0;
        if (password.length === 0) return hash;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // تحويل إلى 32bit integer
        }
        return Math.abs(hash).toString();
    }

    // تسجيل الدخول
    async login(username, password) {
        console.log('🔐 محاولة تسجيل دخول:', username);
        
        if (!username || !password) {
            throw new Error('اسم المستخدم وكلمة المرور مطلوبان');
        }

        // تحديث قائمة المستخدمين
        this.users = this.loadUsers();
        
        const user = this.users[username];
        if (!user) {
            console.error('❌ المستخدم غير موجود:', username);
            throw new Error('اسم المستخدم غير موجود');
        }

        const hashedPassword = this.hashPassword(password);
        if (user.password !== hashedPassword) {
            console.error('❌ كلمة المرور غير صحيحة');
            throw new Error('كلمة المرور غير صحيحة');
        }

        // تحديث بيانات تسجيل الدخول
        user.lastLogin = new Date().toISOString();
        user.stats.loginCount++;
        this.users[username] = user;
        this.saveUsers();

        // إنشاء جلسة
        this.currentUser = user;
        this.createSession(user);

        console.log('✅ تم تسجيل الدخول بنجاح:', username);
        return this.getSafeUserData(user);
    }

    // إنشاء جلسة
    createSession(user) {
        const session = {
            username: user.username,
            loginTime: Date.now(),
            expiresAt: Date.now() + this.sessionTimeout
        };

        localStorage.setItem('tdl_session', JSON.stringify(session));
        localStorage.setItem('tdl_current_user', JSON.stringify(this.getSafeUserData(user)));
        console.log('🔑 تم إنشاء جلسة جديدة');
    }

    // فحص الجلسة الموجودة
    checkExistingSession() {
        try {
            const session = JSON.parse(localStorage.getItem('tdl_session') || '{}');
            const currentUser = JSON.parse(localStorage.getItem('tdl_current_user') || '{}');

            if (session.username && session.expiresAt > Date.now()) {
                const user = this.users[session.username];
                if (user) {
                    this.currentUser = user;
                    console.log('✅ جلسة صالحة موجودة:', session.username);
                    return true;
                }
            }
        } catch (error) {
            console.error('❌ خطأ في فحص الجلسة:', error);
        }

        this.logout();
        return false;
    }

    // تسجيل الخروج
    logout() {
        this.currentUser = null;
        localStorage.removeItem('tdl_session');
        localStorage.removeItem('tdl_current_user');
        console.log('👋 تم تسجيل الخروج');
    }

    // الحصول على بيانات المستخدم الآمنة
    getSafeUserData(user) {
        const { password, ...safeData } = user;
        return safeData;
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // نموذج تسجيل الدخول
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // نموذج التسجيل
        const registerForm = document.getElementById('registerForm');
        if (registerForm) {
            registerForm.addEventListener('submit', (e) => this.handleRegister(e));
        }

        // أزرار التبديل
        const switchBtn = document.getElementById('switchBtn');
        if (switchBtn) {
            switchBtn.addEventListener('click', () => this.toggleForms());
        }
    }

    // معالج تسجيل الدخول
    async handleLogin(e) {
        e.preventDefault();
        
        const username = document.getElementById('loginUsername').value.trim();
        const password = document.getElementById('loginPassword').value;
        const submitBtn = e.target.querySelector('button[type="submit"]');

        try {
            // تأثير التحميل
            this.setLoadingState(submitBtn, true);
            
            // محاولة تسجيل الدخول
            const user = await this.login(username, password);
            
            // إشعار نجاح
            this.showNotification('تم تسجيل الدخول بنجاح! 🎉', 'success');
            
            // توجيه للصفحة الرئيسية
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1000);

        } catch (error) {
            console.error('❌ خطأ في تسجيل الدخول:', error);
            this.showNotification(error.message, 'error');
        } finally {
            this.setLoadingState(submitBtn, false);
        }
    }

    // معالج التسجيل
    async handleRegister(e) {
        e.preventDefault();
        
        const username = document.getElementById('registerUsername').value.trim();
        const password = document.getElementById('registerPassword').value;
        const confirmPassword = document.getElementById('registerPasswordConfirm').value;
        const submitBtn = e.target.querySelector('button[type="submit"]');

        try {
            // التحقق من تطابق كلمات المرور
            if (password !== confirmPassword) {
                throw new Error('كلمات المرور غير متطابقة');
            }

            // تأثير التحميل
            this.setLoadingState(submitBtn, true);
            
            // إنشاء المستخدم الجديد
            await this.register(username, password);
            
            // إشعار نجاح
            this.showNotification('تم إنشاء الحساب بنجاح! 🎉', 'success');
            
            // التبديل لنموذج تسجيل الدخول
            setTimeout(() => {
                this.toggleForms();
                document.getElementById('loginUsername').value = username;
            }, 1000);

        } catch (error) {
            console.error('❌ خطأ في التسجيل:', error);
            this.showNotification(error.message, 'error');
        } finally {
            this.setLoadingState(submitBtn, false);
        }
    }

    // تسجيل مستخدم جديد
    async register(username, password) {
        if (!username || !password) {
            throw new Error('اسم المستخدم وكلمة المرور مطلوبان');
        }

        if (this.users[username]) {
            throw new Error('اسم المستخدم موجود بالفعل');
        }

        const newUser = {
            username,
            password: this.hashPassword(password),
            fullName: username,
            email: `${username}@tdl.com`,
            avatar: username.substring(0, 3).toUpperCase(),
            role: 'user',
            isActive: true,
            createdAt: new Date().toISOString(),
            lastLogin: null,
            settings: {
                theme: 'dark',
                language: 'ar',
                notifications: true
            },
            tasks: [],
            stats: {
                totalTasks: 0,
                completedTasks: 0,
                loginCount: 0
            }
        };

        this.users[username] = newUser;
        this.saveUsers();
        
        console.log('✅ تم إنشاء مستخدم جديد:', username);
        return this.getSafeUserData(newUser);
    }

    // تبديل النماذج
    toggleForms() {
        const loginForm = document.getElementById('loginForm');
        const registerForm = document.getElementById('registerForm');
        const formTitle = document.getElementById('formTitle');
        const switchText = document.getElementById('switchText');
        const switchBtn = document.getElementById('switchBtn');

        if (loginForm.style.display === 'none') {
            // إظهار نموذج تسجيل الدخول
            loginForm.style.display = 'block';
            registerForm.style.display = 'none';
            formTitle.textContent = 'تسجيل الدخول';
            switchText.textContent = 'ليس لديك حساب؟';
            switchBtn.textContent = 'إنشاء حساب';
        } else {
            // إظهار نموذج التسجيل
            loginForm.style.display = 'none';
            registerForm.style.display = 'block';
            formTitle.textContent = 'إنشاء حساب جديد';
            switchText.textContent = 'لديك حساب بالفعل؟';
            switchBtn.textContent = 'تسجيل الدخول';
        }
    }

    // حالة التحميل
    setLoadingState(button, loading) {
        if (loading) {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
        } else {
            button.disabled = false;
            const isLogin = button.closest('#loginForm');
            button.innerHTML = isLogin ? 
                '<i class="fas fa-sign-in-alt"></i> تسجيل الدخول' : 
                '<i class="fas fa-user-plus"></i> إنشاء حساب';
        }
    }

    // إظهار الإشعارات
    showNotification(message, type = 'info') {
        // إنشاء عنصر الإشعار
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.style.cssText = `
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'success' ? 'linear-gradient(135deg, #10b981, #059669)' : 'linear-gradient(135deg, #ef4444, #dc2626)'};
            color: white;
            padding: 1rem 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
            z-index: 10000;
            font-weight: 600;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            opacity: 0;
            transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        `;
        
        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 0.8rem;">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}" style="font-size: 1.3rem;"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // إظهار الإشعار
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(-50%) translateY(0)';
        }, 100);

        // إخفاء الإشعار
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(-50%) translateY(100px)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 500);
        }, 3000);
    }
}

// إنشاء نسخة من النظام
const enhancedAuth = new EnhancedAuth();

// دالة الدخول السريع
function quickLogin() {
    document.getElementById('loginUsername').value = 'kha';
    document.getElementById('loginPassword').value = 'kha/admin';
    
    const button = event.target;
    button.style.transform = 'scale(0.95)';
    button.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-left: 0.5rem;"></i>جاري تسجيل الدخول...';
    
    setTimeout(() => {
        document.getElementById('loginForm').dispatchEvent(new Event('submit'));
    }, 500);
}

// تصدير للاستخدام العام
window.enhancedAuth = enhancedAuth;
window.quickLogin = quickLogin;

console.log('✅ تم تحميل نظام المصادقة المحسن بنجاح');
