<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 192 192">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4a6cf7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2541b2;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="rgba(0,0,0,0.3)" />
    </filter>
  </defs>
  
  <!-- Background -->
  <rect x="4" y="4" rx="30" ry="30" width="184" height="184" fill="url(#grad1)" filter="url(#shadow)" />
  
  <!-- Checkmark and list lines -->
  <g fill="#ffffff" transform="translate(34, 34) scale(0.65)">
    <!-- Checkmark circle -->
    <circle cx="96" cy="96" r="64" fill="rgba(255,255,255,0.15)" stroke="#ffffff" stroke-width="10" />
    
    <!-- Checkmark -->
    <path d="M70,96 L90,116 L126,76" fill="none" stroke="#ffffff" stroke-width="12" stroke-linecap="round" stroke-linejoin="round" />
    
    <!-- List lines -->
    <rect x="160" y="64" width="80" height="10" rx="5" ry="5" />
    <rect x="160" y="96" width="80" height="10" rx="5" ry="5" />
    <rect x="160" y="128" width="80" height="10" rx="5" ry="5" />
  </g>
  
  <!-- Arabic text for TDL -->
  <text x="96" y="150" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#ffffff" direction="rtl" xml:lang="ar">قائمة المهام</text>
</svg>