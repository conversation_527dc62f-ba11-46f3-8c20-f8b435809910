/* TDL V1.0 - Authentication Styles */
/* تصميم واجهة تسجيل الدخول والتسجيل */

/* ========== المتغيرات العامة ========== */
:root {
  --auth-primary: #6366f1;
  --auth-primary-dark: #4f46e5;
  --auth-secondary: #8b5cf6;
  --auth-accent: #06b6d4;
  --auth-success: #10b981;
  --auth-error: #ef4444;
  --auth-warning: #f59e0b;

  /* خلفية ليلية زجاجية متدرجة */
  --auth-bg: linear-gradient(135deg,
    #0f0f23 0%,
    #1a1a2e 25%,
    #16213e 50%,
    #0f3460 75%,
    #533483 100%);

  /* تأثير زجاجي */
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

  /* ألوان النص */
  --auth-text: #e2e8f0;
  --auth-text-light: #94a3b8;
  --auth-text-muted: #64748b;

  /* حدود وظلال */
  --auth-border: rgba(255, 255, 255, 0.1);
  --auth-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  --auth-radius: 20px;

  /* تدرجات متقدمة */
  --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #06b6d4 100%);
  --gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --gradient-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

/* ========== الجسم الأساسي ========== */
.auth-body {
  margin: 0;
  padding: 0;
  font-family: 'Cairo', sans-serif;
  background: var(--auth-bg);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-x: hidden;
  position: relative;

  /* تأثير الضوء المتحرك */
  background-attachment: fixed;
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* ========== الخلفية المتحركة المحسنة ========== */
.auth-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
  pointer-events: none;
}

.floating-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  backdrop-filter: blur(2px);
  animation: float 8s ease-in-out infinite;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.shape-1 {
  width: 120px;
  height: 120px;
  top: 15%;
  left: 8%;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.3) 0%, rgba(139, 92, 246, 0.1) 100%);
  animation-delay: 0s;
}

.shape-2 {
  width: 180px;
  height: 180px;
  top: 55%;
  left: 75%;
  background: radial-gradient(circle, rgba(6, 182, 212, 0.3) 0%, rgba(16, 185, 129, 0.1) 100%);
  animation-delay: 2s;
}

.shape-3 {
  width: 90px;
  height: 90px;
  top: 75%;
  left: 15%;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.3) 0%, rgba(99, 102, 241, 0.1) 100%);
  animation-delay: 4s;
}

.shape-4 {
  width: 150px;
  height: 150px;
  top: 8%;
  left: 65%;
  background: radial-gradient(circle, rgba(16, 185, 129, 0.3) 0%, rgba(6, 182, 212, 0.1) 100%);
  animation-delay: 1s;
}

.shape-5 {
  width: 200px;
  height: 200px;
  top: 35%;
  left: 3%;
  background: radial-gradient(circle, rgba(6, 182, 212, 0.3) 0%, rgba(139, 92, 246, 0.1) 100%);
  animation-delay: 3s;
}

.shape-6 {
  width: 110px;
  height: 110px;
  top: 25%;
  right: 10%;
  background: radial-gradient(circle, rgba(245, 158, 11, 0.3) 0%, rgba(239, 68, 68, 0.1) 100%);
  animation-delay: 5s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-30px) rotate(90deg) scale(1.1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-60px) rotate(180deg) scale(0.9);
    opacity: 0.4;
  }
  75% {
    transform: translateY(-30px) rotate(270deg) scale(1.05);
    opacity: 0.7;
  }
}

/* تأثير الماوس على الأشكال */
.auth-body:hover .shape {
  animation-duration: 4s;
  opacity: 0.8;
}

/* جزيئات متحركة إضافية */
.auth-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(99, 102, 241, 0.4), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(139, 92, 246, 0.3), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(6, 182, 212, 0.3), transparent);
  background-repeat: repeat;
  background-size: 200px 200px;
  animation: sparkle 20s linear infinite;
  opacity: 0.5;
}

@keyframes sparkle {
  0% { transform: translateY(0px); }
  100% { transform: translateY(-200px); }
}

/* ========== حاوي التطبيق ========== */
.auth-container {
  width: 100%;
  max-width: 520px;
  margin: 2rem;
  z-index: 1;
  position: relative;
}

/* ========== رأس التطبيق المحسن ========== */
.auth-header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.app-logo {
  color: var(--auth-text);
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  position: relative;
}

.app-logo i {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  display: block;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: logoFloat 3s ease-in-out infinite;
  filter: drop-shadow(0 4px 8px rgba(99, 102, 241, 0.3));
}

.app-logo h1 {
  font-size: 4rem;
  font-weight: 900;
  margin: 1rem 0;
  letter-spacing: 8px;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  animation: titleGlow 4s ease-in-out infinite;
  text-align: center;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.app-logo h1::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--gradient-primary);
  border-radius: 2px;
  animation: underlineExpand 2s ease-in-out infinite;
}

.app-logo p {
  font-size: 1.2rem;
  opacity: 0.8;
  margin: 0;
  color: var(--auth-text-light);
  font-weight: 300;
  letter-spacing: 1px;
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    filter: drop-shadow(0 4px 8px rgba(99, 102, 241, 0.3));
  }
  50% {
    transform: translateY(-10px) rotate(5deg);
    filter: drop-shadow(0 8px 16px rgba(99, 102, 241, 0.5));
  }
}

@keyframes titleGlow {
  0%, 100% {
    text-shadow: 0 0 20px rgba(99, 102, 241, 0.5);
  }
  50% {
    text-shadow: 0 0 30px rgba(139, 92, 246, 0.8), 0 0 40px rgba(6, 182, 212, 0.3);
  }
}

@keyframes underlineExpand {
  0%, 100% {
    width: 60px;
    opacity: 0.7;
  }
  50% {
    width: 120px;
    opacity: 1;
  }
}

/* ========== حاوي النموذج المحسن ========== */
.auth-form-container {
  background: var(--glass-bg);
  border-radius: var(--auth-radius);
  box-shadow: var(--glass-shadow);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  /* تأثير الضوء المتحرك */
  background-image: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%,
    rgba(255, 255, 255, 0.05) 100%
  );
}

.auth-form-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.8s ease;
}

.auth-form-container:hover::before {
  left: 100%;
}

.auth-form-container:hover {
  transform: translateY(-5px);
  box-shadow:
    var(--glass-shadow),
    0 0 40px rgba(99, 102, 241, 0.2);
  border-color: rgba(99, 102, 241, 0.3);
}

.auth-form-container.hidden {
  opacity: 0;
  transform: scale(0.9) translateY(20px);
  pointer-events: none;
}

.auth-form {
  padding: 3rem;
  position: relative;
  z-index: 1;
}

.auth-form h2 {
  text-align: center;
  color: var(--auth-text);
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8rem;
  position: relative;
}

.auth-form h2 i {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 1.2em;
  animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* ========== مجموعات النماذج ========== */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: var(--auth-text);
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.form-group label i {
  color: var(--auth-primary);
  width: 16px;
}

/* ========== حقول الإدخال المحسنة ========== */
.form-group input {
  width: 100%;
  padding: 1rem 1.2rem;
  border: 2px solid var(--auth-border);
  border-radius: 12px;
  font-size: 1rem;
  font-family: inherit;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  color: var(--auth-text);
  box-sizing: border-box;
  position: relative;
}

.form-group input:focus {
  outline: none;
  border-color: var(--auth-primary);
  background: rgba(255, 255, 255, 0.08);
  box-shadow:
    0 0 0 3px rgba(99, 102, 241, 0.2),
    0 8px 25px rgba(99, 102, 241, 0.15);
  transform: translateY(-3px);
}

.form-group input:hover:not(:focus) {
  border-color: rgba(99, 102, 241, 0.5);
  background: rgba(255, 255, 255, 0.07);
  transform: translateY(-1px);
}

.form-group input::placeholder {
  color: var(--auth-text-muted);
  transition: all 0.3s ease;
}

.form-group input:focus::placeholder {
  color: transparent;
  transform: translateY(-20px);
}

/* تأثير الموجة عند التركيز */
.form-group {
  position: relative;
  overflow: hidden;
}

.form-group::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--gradient-primary);
  transition: all 0.4s ease;
  transform: translateX(-50%);
}

.form-group:focus-within::after {
  width: 100%;
}

/* ========== حقول كلمة المرور ========== */
.password-input {
  position: relative;
}

.toggle-password {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--auth-text-light);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.toggle-password:hover {
  background: rgba(0, 0, 0, 0.05);
  color: var(--auth-primary);
}

/* ========== خيارات النموذج ========== */
.form-options {
  margin: 1.5rem 0;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--auth-text-light);
}

.checkbox-container input {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--auth-border);
  border-radius: 4px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-container input:checked + .checkmark {
  background: var(--auth-primary);
  border-color: var(--auth-primary);
}

.checkbox-container input:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* ========== الأزرار المحسنة ========== */
.auth-btn {
  width: 100%;
  padding: 1.2rem;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8rem;
  text-decoration: none;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.auth-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.6s ease;
}

.auth-btn:hover::before {
  left: 100%;
}

.auth-btn.primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow:
    0 8px 25px rgba(99, 102, 241, 0.3),
    0 4px 10px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.auth-btn.primary:hover {
  transform: translateY(-3px);
  box-shadow:
    0 12px 35px rgba(99, 102, 241, 0.4),
    0 8px 15px rgba(0, 0, 0, 0.3);
  filter: brightness(1.1);
}

.auth-btn.primary:active {
  transform: translateY(-1px);
  box-shadow:
    0 6px 20px rgba(99, 102, 241, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.2);
}

.auth-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  filter: grayscale(1);
}

/* تأثير النبضة للأزرار */
.auth-btn i {
  transition: transform 0.3s ease;
}

.auth-btn:hover i {
  transform: scale(1.2);
}

/* ========== تذييل النموذج ========== */
.auth-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--auth-border);
}

.auth-footer p {
  color: var(--auth-text-light);
  margin: 0;
}

.auth-footer a {
  color: var(--auth-primary);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.auth-footer a:hover {
  text-decoration: underline;
  color: var(--auth-primary-dark);
}

/* ========== اختيار الصورة الرمزية ========== */
.avatar-selection {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
  border: 2px dashed var(--auth-border);
}

.avatar-preview {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--auth-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.avatar-options {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.avatar-btn {
  padding: 0.5rem 1rem;
  border: 1px solid var(--auth-border);
  border-radius: 6px;
  background: white;
  color: var(--auth-text);
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.avatar-btn:hover {
  background: var(--auth-primary);
  color: white;
  border-color: var(--auth-primary);
}

/* ========== الإشعارات ========== */
.auth-notification {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 1rem 1.5rem;
  z-index: 1000;
  transform: translateX(400px);
  transition: all 0.3s ease;
  border-left: 4px solid var(--auth-primary);
}

.auth-notification:not(.hidden) {
  transform: translateX(0);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.notification-icon {
  font-size: 1.2rem;
}

.auth-notification.success {
  border-left-color: var(--auth-success);
}

.auth-notification.success .notification-icon {
  color: var(--auth-success);
}

.auth-notification.error {
  border-left-color: var(--auth-error);
}

.auth-notification.error .notification-icon {
  color: var(--auth-error);
}

/* ========== مؤشر التحميل ========== */
.auth-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.auth-loader.hidden {
  display: none;
}

.loader-content {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--auth-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ========== التصميم المتجاوب ========== */
@media (max-width: 768px) {
  .auth-container {
    margin: 1rem;
    max-width: none;
  }

  .auth-form {
    padding: 1.5rem;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .app-logo h1 {
    font-size: 2rem;
  }

  .app-logo i {
    font-size: 2.5rem;
  }

  .avatar-selection {
    flex-direction: column;
    text-align: center;
  }

  .auth-notification {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    transform: translateY(-100px);
  }

  .auth-notification:not(.hidden) {
    transform: translateY(0);
  }
}

@media (max-width: 480px) {
  .auth-form {
    padding: 1rem;
  }

  .app-logo h1 {
    font-size: 1.8rem;
  }

  .auth-form h2 {
    font-size: 1.5rem;
  }
}

/* ========== تأثيرات الماوس التفاعلية ========== */
.auth-container {
  cursor: none;
}

/* مؤشر مخصص */
.auth-body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.8) 0%, transparent 70%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  transition: transform 0.1s ease;
  transform: translate(-50%, -50%);
  mix-blend-mode: screen;
}

/* تأثير التتبع للماوس */
.mouse-trail {
  position: fixed;
  width: 6px;
  height: 6px;
  background: rgba(139, 92, 246, 0.6);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9998;
  transition: all 0.3s ease;
  mix-blend-mode: screen;
}

/* تأثيرات hover متقدمة */
.auth-form-container:hover {
  animation: containerFloat 6s ease-in-out infinite;
}

@keyframes containerFloat {
  0%, 100% { transform: translateY(-5px) rotateX(0deg); }
  50% { transform: translateY(-15px) rotateX(2deg); }
}

/* تأثير الضوء المتحرك مع الماوس */
.auth-form-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    600px circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
    rgba(99, 102, 241, 0.1) 0%,
    transparent 40%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.auth-form-container:hover::after {
  opacity: 1;
}

/* تأثيرات الجزيئات المتحركة */
@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-20px) translateX(10px) rotate(90deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-40px) translateX(-5px) rotate(180deg);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-20px) translateX(-15px) rotate(270deg);
    opacity: 0.4;
  }
}

/* تأثير النبضة للعناصر التفاعلية */
.form-group:hover label {
  color: var(--auth-primary);
  transform: translateX(5px);
  transition: all 0.3s ease;
}

.form-group:hover label i {
  transform: scale(1.2);
  color: var(--auth-secondary);
}

/* تأثيرات الانتقال السلس */
.auth-form-container,
.auth-form input,
.auth-btn,
.user-avatar {
  will-change: transform;
}

/* تحسين الأداء */
.auth-background,
.floating-shapes,
.shape {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* تأثير الموجة عند النقر */
.ripple-effect {
  position: relative;
  overflow: hidden;
}

.ripple-effect::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
}

.ripple-effect:active::before {
  width: 300px;
  height: 300px;
}

/* تأثير الموجة المحسن */
.ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  transform: scale(0);
  animation: rippleAnimation 0.6s linear;
  pointer-events: none;
}

@keyframes rippleAnimation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* تحسينات الأداء للانيميشن */
.auth-form-container,
.floating-shapes,
.shape,
.mouse-trail {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* تأثير التوهج للعناصر النشطة */
.form-group input:focus,
.auth-btn:hover {
  animation: elementGlow 2s ease-in-out infinite alternate;
}

@keyframes elementGlow {
  from {
    box-shadow:
      0 0 5px rgba(99, 102, 241, 0.3),
      0 0 10px rgba(99, 102, 241, 0.2),
      0 0 15px rgba(99, 102, 241, 0.1);
  }
  to {
    box-shadow:
      0 0 10px rgba(99, 102, 241, 0.5),
      0 0 20px rgba(99, 102, 241, 0.3),
      0 0 30px rgba(99, 102, 241, 0.2);
  }
}

/* تأثير الكتابة المتحركة */
.typing-cursor::after {
  content: '|';
  animation: blink 1s infinite;
  color: var(--auth-primary);
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* تحسين الانتقالات */
.auth-form-container * {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* تأثير الضباب الخلفي */
.auth-background::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(ellipse at top, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at bottom, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
  animation: mistFloat 20s ease-in-out infinite;
}

@keyframes mistFloat {
  0%, 100% {
    opacity: 0.3;
    transform: translateY(0px);
  }
  50% {
    opacity: 0.6;
    transform: translateY(-20px);
  }
}

/* ========== تحسينات إضافية ========== */
.terms-link {
  color: var(--auth-primary) !important;
  text-decoration: underline;
}

.terms-link:hover {
  color: var(--auth-primary-dark) !important;
}

/* تأثيرات التركيز المحسنة */
.form-group input:focus {
  animation: focusGlow 0.3s ease;
}

@keyframes focusGlow {
  0% { box-shadow: 0 0 0 0 rgba(25, 118, 210, 0.4); }
  100% { box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1); }
}

/* تحسين الأزرار */
.auth-btn:active {
  transform: translateY(0) scale(0.98);
}

/* تحسين الصورة الرمزية */
.avatar-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

/* ========== حوار تأكيد إنشاء الحساب ========== */
.account-created-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.account-created-dialog.show {
  opacity: 1;
  visibility: visible;
}

.account-created-dialog.hide {
  opacity: 0;
  visibility: hidden;
}

.dialog-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
}

.dialog-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  background: var(--glass-bg);
  backdrop-filter: blur(25px);
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.5),
    0 0 40px rgba(99, 102, 241, 0.2);
  padding: 2.5rem;
  max-width: 500px;
  width: 90%;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.account-created-dialog.show .dialog-content {
  transform: translate(-50%, -50%) scale(1);
}

.dialog-header {
  text-align: center;
  margin-bottom: 2rem;
}

.success-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  background: var(--gradient-secondary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: successPulse 2s ease-in-out infinite;
}

.success-icon i {
  font-size: 2.5rem;
  color: white;
}

@keyframes successPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(16, 185, 129, 0.5);
  }
}

.dialog-header h3 {
  color: var(--auth-text);
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dialog-header p {
  color: var(--auth-text-light);
  font-size: 1.1rem;
  margin: 0;
}

.dialog-body {
  margin-bottom: 2rem;
}

.account-summary {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid var(--glass-border);
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  color: var(--auth-text-light);
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-item i {
  width: 20px;
  color: var(--auth-primary);
}

.dialog-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.dialog-btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 150px;
  justify-content: center;
}

.dialog-btn.primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

.dialog-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
}

.dialog-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: var(--auth-text);
  border: 1px solid var(--glass-border);
}

.dialog-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

/* تحسين responsive للحوار */
@media (max-width: 768px) {
  .dialog-content {
    padding: 2rem;
    margin: 1rem;
    width: calc(100% - 2rem);
  }

  .dialog-actions {
    flex-direction: column;
  }

  .dialog-btn {
    width: 100%;
  }
}
