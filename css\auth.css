/* TDL V1.0 - Authentication Styles */
/* تصميم واجهة تسجيل الدخول والتسجيل */

/* ========== المتغيرات العامة ========== */
:root {
  --auth-primary: #1976d2;
  --auth-primary-dark: #1565c0;
  --auth-secondary: #42a5f5;
  --auth-success: #4caf50;
  --auth-error: #f44336;
  --auth-warning: #ff9800;
  --auth-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --auth-card-bg: rgba(255, 255, 255, 0.95);
  --auth-text: #333;
  --auth-text-light: #666;
  --auth-border: #e0e0e0;
  --auth-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  --auth-radius: 16px;
}

/* ========== الجسم الأساسي ========== */
.auth-body {
  margin: 0;
  padding: 0;
  font-family: 'Cairo', sans-serif;
  background: var(--auth-bg);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-x: hidden;
}

/* ========== الخلفية المتحركة ========== */
.auth-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
}

.floating-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  left: 80%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  top: 80%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 10%;
  left: 70%;
  animation-delay: 1s;
}

.shape-5 {
  width: 140px;
  height: 140px;
  top: 40%;
  left: 5%;
  animation-delay: 3s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* ========== حاوي التطبيق ========== */
.auth-container {
  width: 100%;
  max-width: 500px;
  margin: 2rem;
  z-index: 1;
}

/* ========== رأس التطبيق ========== */
.auth-header {
  text-align: center;
  margin-bottom: 2rem;
}

.app-logo {
  color: white;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.app-logo i {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
  animation: pulse 2s infinite;
}

.app-logo h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0.5rem 0;
  letter-spacing: 2px;
}

.app-logo p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* ========== حاوي النموذج ========== */
.auth-form-container {
  background: var(--auth-card-bg);
  border-radius: var(--auth-radius);
  box-shadow: var(--auth-shadow);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.3s ease;
}

.auth-form-container.hidden {
  display: none;
}

.auth-form {
  padding: 2.5rem;
}

.auth-form h2 {
  text-align: center;
  color: var(--auth-text);
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.auth-form h2 i {
  color: var(--auth-primary);
}

/* ========== مجموعات النماذج ========== */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: var(--auth-text);
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.form-group label i {
  color: var(--auth-primary);
  width: 16px;
}

/* ========== حقول الإدخال ========== */
.form-group input {
  width: 100%;
  padding: 0.8rem 1rem;
  border: 2px solid var(--auth-border);
  border-radius: 10px;
  font-size: 1rem;
  font-family: inherit;
  transition: all 0.3s ease;
  background: white;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: var(--auth-primary);
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
  transform: translateY(-2px);
}

.form-group input::placeholder {
  color: #aaa;
}

/* ========== حقول كلمة المرور ========== */
.password-input {
  position: relative;
}

.toggle-password {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--auth-text-light);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.toggle-password:hover {
  background: rgba(0, 0, 0, 0.05);
  color: var(--auth-primary);
}

/* ========== خيارات النموذج ========== */
.form-options {
  margin: 1.5rem 0;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--auth-text-light);
}

.checkbox-container input {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--auth-border);
  border-radius: 4px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-container input:checked + .checkmark {
  background: var(--auth-primary);
  border-color: var(--auth-primary);
}

.checkbox-container input:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* ========== الأزرار ========== */
.auth-btn {
  width: 100%;
  padding: 1rem;
  border: none;
  border-radius: 10px;
  font-size: 1.1rem;
  font-weight: 600;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-decoration: none;
  box-sizing: border-box;
}

.auth-btn.primary {
  background: linear-gradient(135deg, var(--auth-primary), var(--auth-primary-dark));
  color: white;
  box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);
}

.auth-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(25, 118, 210, 0.4);
}

.auth-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* ========== تذييل النموذج ========== */
.auth-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--auth-border);
}

.auth-footer p {
  color: var(--auth-text-light);
  margin: 0;
}

.auth-footer a {
  color: var(--auth-primary);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.auth-footer a:hover {
  text-decoration: underline;
  color: var(--auth-primary-dark);
}

/* ========== اختيار الصورة الرمزية ========== */
.avatar-selection {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
  border: 2px dashed var(--auth-border);
}

.avatar-preview {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--auth-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.avatar-options {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.avatar-btn {
  padding: 0.5rem 1rem;
  border: 1px solid var(--auth-border);
  border-radius: 6px;
  background: white;
  color: var(--auth-text);
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.avatar-btn:hover {
  background: var(--auth-primary);
  color: white;
  border-color: var(--auth-primary);
}

/* ========== الإشعارات ========== */
.auth-notification {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 1rem 1.5rem;
  z-index: 1000;
  transform: translateX(400px);
  transition: all 0.3s ease;
  border-left: 4px solid var(--auth-primary);
}

.auth-notification:not(.hidden) {
  transform: translateX(0);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.notification-icon {
  font-size: 1.2rem;
}

.auth-notification.success {
  border-left-color: var(--auth-success);
}

.auth-notification.success .notification-icon {
  color: var(--auth-success);
}

.auth-notification.error {
  border-left-color: var(--auth-error);
}

.auth-notification.error .notification-icon {
  color: var(--auth-error);
}

/* ========== مؤشر التحميل ========== */
.auth-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.auth-loader.hidden {
  display: none;
}

.loader-content {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--auth-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ========== التصميم المتجاوب ========== */
@media (max-width: 768px) {
  .auth-container {
    margin: 1rem;
    max-width: none;
  }

  .auth-form {
    padding: 1.5rem;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .app-logo h1 {
    font-size: 2rem;
  }

  .app-logo i {
    font-size: 2.5rem;
  }

  .avatar-selection {
    flex-direction: column;
    text-align: center;
  }

  .auth-notification {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    transform: translateY(-100px);
  }

  .auth-notification:not(.hidden) {
    transform: translateY(0);
  }
}

@media (max-width: 480px) {
  .auth-form {
    padding: 1rem;
  }

  .app-logo h1 {
    font-size: 1.8rem;
  }

  .auth-form h2 {
    font-size: 1.5rem;
  }
}

/* ========== تحسينات إضافية ========== */
.terms-link {
  color: var(--auth-primary) !important;
  text-decoration: underline;
}

.terms-link:hover {
  color: var(--auth-primary-dark) !important;
}

/* تأثيرات التركيز المحسنة */
.form-group input:focus {
  animation: focusGlow 0.3s ease;
}

@keyframes focusGlow {
  0% { box-shadow: 0 0 0 0 rgba(25, 118, 210, 0.4); }
  100% { box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1); }
}

/* تحسين الأزرار */
.auth-btn:active {
  transform: translateY(0) scale(0.98);
}

/* تحسين الصورة الرمزية */
.avatar-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}
