<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام التسجيل - TDL</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: #e0e0e0;
            padding: 2rem;
            line-height: 1.6;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 15px;
        }
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
        }
        .test-btn {
            background: #6366f1;
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            margin: 0.5rem;
        }
        .test-btn:hover {
            background: #4f46e5;
        }
        .result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { background: rgba(16, 185, 129, 0.2); }
        .error { background: rgba(239, 68, 68, 0.2); }
        .info { background: rgba(59, 130, 246, 0.2); }
        
        .form-group {
            margin-bottom: 1rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
        }
        .form-group input {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔧 اختبار نظام التسجيل</h1>
        
        <div class="test-section">
            <h2>📋 معلومات النظام</h2>
            <button class="test-btn" onclick="checkSystem()">فحص النظام</button>
            <div id="system-info" class="result"></div>
        </div>

        <div class="test-section">
            <h2>📝 اختبار إنشاء حساب</h2>
            <div class="form-group">
                <label>الاسم الكامل:</label>
                <input type="text" id="testFullName" value="مستخدم تجريبي">
            </div>
            <div class="form-group">
                <label>اسم المستخدم:</label>
                <input type="text" id="testUsername" value="">
            </div>
            <div class="form-group">
                <label>البريد الإلكتروني:</label>
                <input type="email" id="testEmail" value="">
            </div>
            <div class="form-group">
                <label>كلمة المرور:</label>
                <input type="password" id="testPassword" value="123456">
            </div>
            <button class="test-btn" onclick="testRegister()">اختبار التسجيل</button>
            <div id="register-result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>🔐 اختبار تسجيل الدخول</h2>
            <div class="form-group">
                <label>اسم المستخدم:</label>
                <input type="text" id="loginUsername" value="">
            </div>
            <div class="form-group">
                <label>كلمة المرور:</label>
                <input type="password" id="loginPassword" value="123456">
            </div>
            <button class="test-btn" onclick="testLogin()">اختبار تسجيل الدخول</button>
            <div id="login-result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>🗂️ إدارة البيانات</h2>
            <button class="test-btn" onclick="showUsers()">عرض المستخدمين</button>
            <button class="test-btn" onclick="clearData()" style="background: #ef4444;">مسح البيانات</button>
            <div id="data-result" class="result"></div>
        </div>
    </div>

    <script type="module">
        import userManager from './js/simple-user-manager.js';
        
        window.userManager = userManager;
        
        // إنشاء أسماء مستخدمين عشوائية
        document.getElementById('testUsername').value = 'user_' + Date.now();
        document.getElementById('testEmail').value = 'test_' + Date.now() + '@example.com';
        document.getElementById('loginUsername').value = document.getElementById('testUsername').value;
        
        // فحص النظام
        window.checkSystem = function() {
            const result = document.getElementById('system-info');
            try {
                const info = `
النظام: ${userManager ? '✅ محمل' : '❌ غير محمل'}
نوع userManager: ${typeof userManager}
الوظائف المتاحة: ${userManager ? Object.getOwnPropertyNames(Object.getPrototypeOf(userManager)).join(', ') : 'لا يوجد'}
التخزين المحلي: ${typeof Storage !== 'undefined' ? '✅ متاح' : '❌ غير متاح'}
المستخدمين المحفوظين: ${userManager ? Object.keys(userManager.loadUsers()).length : 0}
الجلسة الحالية: ${userManager && userManager.checkSession() ? '✅ نشطة' : '❌ غير نشطة'}
                `;
                
                result.className = 'result info';
                result.textContent = info;
            } catch (error) {
                result.className = 'result error';
                result.textContent = 'خطأ في فحص النظام: ' + error.message;
            }
        };

        // اختبار التسجيل
        window.testRegister = async function() {
            const result = document.getElementById('register-result');
            try {
                const userData = {
                    fullName: document.getElementById('testFullName').value,
                    username: document.getElementById('testUsername').value,
                    email: document.getElementById('testEmail').value,
                    password: document.getElementById('testPassword').value
                };

                console.log('🧪 اختبار التسجيل:', userData);
                
                const registerResult = await userManager.register(userData);
                
                if (registerResult.success) {
                    result.className = 'result success';
                    result.textContent = '✅ تم إنشاء الحساب بنجاح!\n' + JSON.stringify(registerResult, null, 2);
                    
                    // تحديث حقول تسجيل الدخول
                    document.getElementById('loginUsername').value = userData.username;
                } else {
                    result.className = 'result error';
                    result.textContent = '❌ فشل في إنشاء الحساب: ' + registerResult.message;
                }
            } catch (error) {
                result.className = 'result error';
                result.textContent = '❌ خطأ في التسجيل: ' + error.message;
                console.error('خطأ في التسجيل:', error);
            }
        };

        // اختبار تسجيل الدخول
        window.testLogin = async function() {
            const result = document.getElementById('login-result');
            try {
                const username = document.getElementById('loginUsername').value;
                const password = document.getElementById('loginPassword').value;

                console.log('🧪 اختبار تسجيل الدخول:', username);
                
                const loginResult = await userManager.login(username, password);
                
                if (loginResult.success) {
                    result.className = 'result success';
                    result.textContent = '✅ تم تسجيل الدخول بنجاح!\n' + JSON.stringify(loginResult, null, 2);
                } else {
                    result.className = 'result error';
                    result.textContent = '❌ فشل في تسجيل الدخول: ' + loginResult.message;
                }
            } catch (error) {
                result.className = 'result error';
                result.textContent = '❌ خطأ في تسجيل الدخول: ' + error.message;
                console.error('خطأ في تسجيل الدخول:', error);
            }
        };

        // عرض المستخدمين
        window.showUsers = function() {
            const result = document.getElementById('data-result');
            try {
                const users = userManager.loadUsers();
                const userList = Object.keys(users).map(username => {
                    const user = users[username];
                    return `${username}: ${user.fullName} (${user.email})`;
                }).join('\n');
                
                result.className = 'result info';
                result.textContent = `المستخدمين المحفوظين (${Object.keys(users).length}):\n\n${userList || 'لا يوجد مستخدمين'}`;
            } catch (error) {
                result.className = 'result error';
                result.textContent = 'خطأ في عرض المستخدمين: ' + error.message;
            }
        };

        // مسح البيانات
        window.clearData = function() {
            const result = document.getElementById('data-result');
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                try {
                    localStorage.clear();
                    result.className = 'result success';
                    result.textContent = '✅ تم مسح جميع البيانات';
                } catch (error) {
                    result.className = 'result error';
                    result.textContent = 'خطأ في مسح البيانات: ' + error.message;
                }
            }
        };

        // فحص النظام عند التحميل
        setTimeout(() => {
            checkSystem();
        }, 1000);
    </script>
</body>
</html>
