/* TDL V1.0 - Optimized CSS File */
/* تم تحسين هذا الملف للأداء الأمثل وتقليل حجم التحميل */

:root {
  /* الألوان الأساسية */
  --primary-color: #4a6cf7;
  --primary-hover: #2541b2;
  --danger-color: #f44336;
  --danger-hover: #d32f2f;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --info-color: #2196f3;
  
  /* ألوان الوضع الفاتح */
  --bg-color: #f4f6fa;
  --text-color: #222;
  --text-light: #757575;
  --border-color: #e0e0e0;
  --card-bg: #fff;
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  
  /* المسافات والأبعاد */
  --transition: 0.2s;
  --border-radius: 12px;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
}

/* تعريفات الوضع الداكن */
[data-theme="dark"] {
  --bg-color: #121212;
  --card-bg: #1e1e1e;
  --text-color: #e0e0e0;
  --text-light: #9e9e9e;
  --border-color: #333;
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

/* إعادة ضبط عامة */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

/* تنسيقات HTML و Body */
html, body {
  font-family: 'Cairo', sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  transition: background-color var(--transition), color var(--transition);
  font-size: 0.92rem;
  position: relative;
  direction: rtl;
}

/* إصلاحات للوضع الداكن */
[data-theme="dark"] html,
[data-theme="dark"] body {
  background: var(--bg-color);
}

/* منع التمرير الأفقي وتحسين التجربة على الأجهزة المحمولة */
@media (max-width: 768px) {
  html, body {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: none;
  }
}

/* الحاوية الرئيسية */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  background: transparent;
}

/* تنسيقات الرأس */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) 0;
  margin-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  flex-wrap: wrap;
  gap: var(--spacing-md);
  min-height: 60px;
}

.header h1 {
  font-size: 1.7rem;
  margin-bottom: 0.4rem;
}

/* إحصائيات المهام */
.task-stats {
  background-color: var(--card-bg);
  padding: 0.5rem 1.2rem;
  border-radius: 50px;
  box-shadow: var(--shadow);
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin: 0;
  border: 1px solid var(--border-color);
  transition: background-color var(--transition), box-shadow var(--transition);
}

.task-stats span {
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
}

.task-stats .total { color: #9c27b0; }
.task-stats .completed { color: var(--success-color); }
.task-stats .remaining { color: var(--warning-color); }

.task-stats i {
  font-size: 1rem;
  opacity: 0.9;
}

/* نموذج إضافة المهام */
.add-task-form {
  background-color: var(--card-bg);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: var(--spacing-lg);
  transition: background-color var(--transition), box-shadow var(--transition);
}

.form-row {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  flex-wrap: wrap;
}

.form-group {
  flex: 1;
  min-width: 200px;
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 600;
  color: var(--text-color);
}

.form-control {
  width: 100%;
  padding: 0.7rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--card-bg);
  color: var(--text-color);
  font-family: inherit;
  transition: border-color var(--transition), box-shadow var(--transition);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.2);
}

/* الأزرار */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.7rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-family: inherit;
  font-weight: 600;
  cursor: pointer;
  transition: background-color var(--transition), transform var(--transition), box-shadow var(--transition);
  gap: 0.5rem;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background-color: var(--danger-hover);
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

/* قائمة المهام */
.task-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.task-item {
  background-color: var(--card-bg);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: var(--spacing-md);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color var(--transition), box-shadow var(--transition), transform 0.3s ease;
  position: relative;
  overflow: hidden;
}

.task-item:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-3px);
}

.task-content {
  flex: 1;
  margin-left: var(--spacing-md);
}

.task-title {
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
  color: var(--text-color);
  transition: color var(--transition), text-decoration 0.3s ease;
}

.task-completed .task-title {
  text-decoration: line-through;
  color: var(--text-light);
}

.task-meta {
  display: flex;
  gap: var(--spacing-md);
  color: var(--text-light);
  font-size: 0.85rem;
  flex-wrap: wrap;
}

.task-category, .task-priority, .task-date {
  display: inline-flex;
  align-items: center;
  gap: 0.3rem;
}

.task-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.task-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  color: var(--text-light);
  transition: color var(--transition), transform var(--transition);
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.task-btn:hover {
  color: var(--primary-color);
  background-color: rgba(74, 108, 247, 0.1);
  transform: scale(1.1);
}

.task-btn.delete:hover {
  color: var(--danger-color);
  background-color: rgba(244, 67, 54, 0.1);
}

.task-btn.complete:hover {
  color: var(--success-color);
  background-color: rgba(76, 175, 80, 0.1);
}

/* تأثيرات الإكمال */
.task-completed {
  background-color: var(--card-bg);
  opacity: 0.8;
}

.task-completed .task-btn.complete {
  color: var(--success-color);
}

/* تأثيرات التحرير */
.edit-mode .task-title {
  background-color: rgba(74, 108, 247, 0.1);
  padding: 0.3rem 0.5rem;
  border-radius: 4px;
  border: 1px solid var(--primary-color);
}

.edit-actions {
  display: flex;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xs);
}

.edit-actions button {
  padding: 0.3rem 0.8rem;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.85rem;
  transition: background-color var(--transition);
}

.edit-actions .save {
  background-color: var(--success-color);
  color: white;
}

.edit-actions .cancel {
  background-color: var(--danger-color);
  color: white;
}

/* تأثيرات الحذف */
.delete-animation {
  animation: deleteTask 0.5s ease forwards;
}

@keyframes deleteTask {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(50px);
  }
}

/* تأثيرات الإكمال */
.complete-animation {
  animation: completeTask 0.5s ease forwards;
}

@keyframes completeTask {
  0% {
    background-color: var(--card-bg);
  }
  50% {
    background-color: rgba(76, 175, 80, 0.2);
  }
  100% {
    background-color: var(--card-bg);
  }
}

/* نافذة التأكيد المخصصة */
.custom-confirmation-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.custom-confirmation-modal.show {
  opacity: 1;
  visibility: visible;
}

.confirmation-content {
  background-color: var(--card-bg);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  max-width: 400px;
  width: 90%;
  text-align: center;
  transform: translateY(-20px);
  transition: transform 0.3s ease;
}

.custom-confirmation-modal.show .confirmation-content {
  transform: translateY(0);
}

.confirmation-content h3 {
  margin-bottom: var(--spacing-md);
  color: var(--text-color);
}

.confirmation-content p {
  margin-bottom: var(--spacing-lg);
  color: var(--text-light);
}

.confirmation-buttons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    text-align: center;
  }
  
  .task-stats {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
  }
  
  .form-row {
    flex-direction: column;
  }
  
  .task-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .task-actions {
    margin-top: var(--spacing-sm);
    align-self: flex-end;
  }
  
  .task-meta {
    margin-top: var(--spacing-xs);
  }
}

/* تحسينات للطباعة */
@media print {
  body {
    background: white;
    color: black;
  }
  
  .container {
    width: 100%;
    max-width: 100%;
    padding: 0;
  }
  
  .add-task-form,
  .task-actions,
  .header button {
    display: none;
  }
  
  .task-item {
    break-inside: avoid;
    page-break-inside: avoid;
    border: 1px solid #ddd;
    box-shadow: none;
  }
}

/* أنماط PWA وحالة الاتصال */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg-color);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 1;
  transition: opacity 0.3s ease-out, visibility 0.3s ease-out;
}

.loading-screen.hidden {
  opacity: 0;
  visibility: hidden;
}

.loading-container {
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 18px;
  color: var(--text-color);
  margin-top: 10px;
}

/* حالة الاتصال */
.connection-status {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 10px 15px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transform: translateY(100px);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.connection-status.show {
  transform: translateY(0);
  opacity: 1;
}

.connection-status.online {
  background-color: #e7f7ed;
  color: #0d904f;
  border: 1px solid #a8e6c1;
}

.connection-status.offline {
  background-color: #fff4e5;
  color: #e67700;
  border: 1px solid #ffe0b2;
}

.connection-status i {
  font-size: 16px;
}

/* إشعار التحديث */
.update-notification {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%) translateY(-100px);
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-width: 400px;
  width: calc(100% - 40px);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.update-notification.show {
  transform: translateX(-50%) translateY(0);
  opacity: 1;
}

.update-notification-content {
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.update-notification-content i {
  font-size: 24px;
  color: var(--primary-color);
  margin-bottom: 10px;
}

.update-notification-content span {
  font-size: 16px;
  margin-bottom: 15px;
  text-align: center;
}

.update-actions {
  display: flex;
  gap: 10px;
  margin-top: 5px;
  width: 100%;
  justify-content: center;
}

.update-notification .close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 14px;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.update-notification .close-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #666;
}

/* وضع عدم الاتصال */
.offline-mode .task-input-bar input,
.offline-mode .task-actions button:not(.offline-supported),
.offline-mode .sidebar-nav a:not(.offline-supported) {
  opacity: 0.6;
  pointer-events: none;
}

.offline-mode .offline-indicator {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #e67700;
  background-color: #fff4e5;
  padding: 3px 8px;
  border-radius: 4px;
  margin-right: 10px;
}

.offline-mode .offline-indicator i {
  font-size: 10px;
}

/* تحسينات الأداء */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* تحسينات تحميل الصفحة */
.content-placeholder {
  background: linear-gradient(90deg, var(--card-bg) 25%, var(--border-color) 50%, var(--card-bg) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--border-radius);
  height: 20px;
  margin-bottom: var(--spacing-sm);
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* تحسينات الوصول */
:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* تحسينات الأداء - تقليل repaints */
.hardware-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}