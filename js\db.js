// TDL V1.0 - IndexedDB Implementation

const DB_NAME = 'tdl-database';
const DB_VERSION = 1;
const TASKS_STORE = 'tasks';
const SETTINGS_STORE = 'settings';
const USERS_STORE = 'users';

class TDLDatabase {
  constructor() {
    this.db = null;
    this.initDatabase();
  }

  // تهيئة قاعدة البيانات
  async initDatabase() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      // إنشاء أو ترقية قاعدة البيانات
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // إنشاء مخزن المهام إذا لم يكن موجوداً
        if (!db.objectStoreNames.contains(TASKS_STORE)) {
          const tasksStore = db.createObjectStore(TASKS_STORE, { keyPath: 'id' });
          tasksStore.createIndex('category', 'category', { unique: false });
          tasksStore.createIndex('priority', 'priority', { unique: false });
          tasksStore.createIndex('completed', 'completed', { unique: false });
          tasksStore.createIndex('createdAt', 'createdAt', { unique: false });
        }
        
        // إنشاء مخزن الإعدادات إذا لم يكن موجوداً
        if (!db.objectStoreNames.contains(SETTINGS_STORE)) {
          db.createObjectStore(SETTINGS_STORE, { keyPath: 'id' });
        }
        
        // إنشاء مخزن المستخدمين إذا لم يكن موجوداً
        if (!db.objectStoreNames.contains(USERS_STORE)) {
          const usersStore = db.createObjectStore(USERS_STORE, { keyPath: 'username' });
          usersStore.createIndex('email', 'email', { unique: true });
        }
      };

      // نجاح فتح قاعدة البيانات
      request.onsuccess = (event) => {
        this.db = event.target.result;
        console.log('تم فتح قاعدة البيانات بنجاح');
        resolve(this.db);
      };

      // فشل فتح قاعدة البيانات
      request.onerror = (event) => {
        console.error('خطأ في فتح قاعدة البيانات:', event.target.error);
        reject(event.target.error);
      };
    });
  }

  // الحصول على قاعدة البيانات
  async getDatabase() {
    if (this.db) return this.db;
    return this.initDatabase();
  }

  // ==================== وظائف المهام ====================

  // إضافة مهمة جديدة
  async addTask(task) {
    const db = await this.getDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([TASKS_STORE], 'readwrite');
      const store = transaction.objectStore(TASKS_STORE);
      const request = store.add(task);

      request.onsuccess = () => resolve(task);
      request.onerror = (event) => reject(event.target.error);
    });
  }

  // تحديث مهمة موجودة
  async updateTask(taskId, updates) {
    const db = await this.getDatabase();
    return new Promise(async (resolve, reject) => {
      try {
        // الحصول على المهمة الحالية أولاً
        const transaction = db.transaction([TASKS_STORE], 'readwrite');
        const store = transaction.objectStore(TASKS_STORE);
        const getRequest = store.get(taskId);

        getRequest.onsuccess = () => {
          const existingTask = getRequest.result;
          if (existingTask) {
            // دمج التحديثات مع المهمة الموجودة
            const updatedTask = { ...existingTask, ...updates };
            const putRequest = store.put(updatedTask);

            putRequest.onsuccess = () => resolve(updatedTask);
            putRequest.onerror = (event) => reject(event.target.error);
          } else {
            reject(new Error('المهمة غير موجودة'));
          }
        };

        getRequest.onerror = (event) => reject(event.target.error);
      } catch (error) {
        reject(error);
      }
    });
  }

  // حذف مهمة
  async deleteTask(taskId) {
    const db = await this.getDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([TASKS_STORE], 'readwrite');
      const store = transaction.objectStore(TASKS_STORE);
      const request = store.delete(taskId);

      request.onsuccess = () => resolve(true);
      request.onerror = (event) => reject(event.target.error);
    });
  }

  // الحصول على مهمة بواسطة المعرف
  async getTask(taskId) {
    const db = await this.getDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([TASKS_STORE], 'readonly');
      const store = transaction.objectStore(TASKS_STORE);
      const request = store.get(taskId);

      request.onsuccess = () => resolve(request.result);
      request.onerror = (event) => reject(event.target.error);
    });
  }

  // الحصول على جميع المهام
  async getAllTasks() {
    const db = await this.getDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([TASKS_STORE], 'readonly');
      const store = transaction.objectStore(TASKS_STORE);
      const request = store.getAll();

      request.onsuccess = () => resolve(request.result);
      request.onerror = (event) => reject(event.target.error);
    });
  }

  // الحصول على المهام حسب الفئة
  async getTasksByCategory(category) {
    const db = await this.getDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([TASKS_STORE], 'readonly');
      const store = transaction.objectStore(TASKS_STORE);
      const index = store.index('category');
      const request = index.getAll(category);

      request.onsuccess = () => resolve(request.result);
      request.onerror = (event) => reject(event.target.error);
    });
  }

  // الحصول على المهام حسب الحالة (مكتملة/غير مكتملة)
  async getTasksByCompletion(completed) {
    const db = await this.getDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([TASKS_STORE], 'readonly');
      const store = transaction.objectStore(TASKS_STORE);
      const index = store.index('completed');
      const request = index.getAll(completed);

      request.onsuccess = () => resolve(request.result);
      request.onerror = (event) => reject(event.target.error);
    });
  }

  // حذف جميع المهام
  async deleteAllTasks() {
    const db = await this.getDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([TASKS_STORE], 'readwrite');
      const store = transaction.objectStore(TASKS_STORE);
      const request = store.clear();

      request.onsuccess = () => resolve(true);
      request.onerror = (event) => reject(event.target.error);
    });
  }

  // تهيئة قاعدة البيانات (alias للتوافق)
  async init() {
    return this.initDatabase();
  }

  // ==================== وظائف الإعدادات ====================

  // حفظ الإعدادات
  async saveSettings(settings) {
    const db = await this.getDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([SETTINGS_STORE], 'readwrite');
      const store = transaction.objectStore(SETTINGS_STORE);
      const request = store.put({ id: 'userSettings', ...settings });

      request.onsuccess = () => resolve(settings);
      request.onerror = (event) => reject(event.target.error);
    });
  }

  // الحصول على الإعدادات
  async getSettings() {
    const db = await this.getDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([SETTINGS_STORE], 'readonly');
      const store = transaction.objectStore(SETTINGS_STORE);
      const request = store.get('userSettings');

      request.onsuccess = () => {
        if (request.result) {
          // حذف معرف الإعدادات من النتيجة
          const { id, ...settings } = request.result;
          resolve(settings);
        } else {
          resolve(null);
        }
      };
      request.onerror = (event) => reject(event.target.error);
    });
  }

  // حفظ إعداد واحد
  async saveSetting(key, value) {
    const db = await this.getDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([SETTINGS_STORE], 'readwrite');
      const store = transaction.objectStore(SETTINGS_STORE);
      const request = store.put({ id: key, value: value });

      request.onsuccess = () => resolve(value);
      request.onerror = (event) => reject(event.target.error);
    });
  }

  // الحصول على إعداد واحد
  async getSetting(key) {
    const db = await this.getDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([SETTINGS_STORE], 'readonly');
      const store = transaction.objectStore(SETTINGS_STORE);
      const request = store.get(key);

      request.onsuccess = () => {
        if (request.result) {
          resolve(request.result.value);
        } else {
          resolve(null);
        }
      };
      request.onerror = (event) => reject(event.target.error);
    });
  }

  // ==================== وظائف المستخدمين ====================

  // إضافة مستخدم جديد
  async addUser(user) {
    const db = await this.getDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([USERS_STORE], 'readwrite');
      const store = transaction.objectStore(USERS_STORE);
      const request = store.add(user);

      request.onsuccess = () => resolve(user);
      request.onerror = (event) => reject(event.target.error);
    });
  }

  // تحديث مستخدم موجود
  async updateUser(user) {
    const db = await this.getDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([USERS_STORE], 'readwrite');
      const store = transaction.objectStore(USERS_STORE);
      const request = store.put(user);

      request.onsuccess = () => resolve(user);
      request.onerror = (event) => reject(event.target.error);
    });
  }

  // الحصول على مستخدم بواسطة اسم المستخدم
  async getUser(username) {
    const db = await this.getDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([USERS_STORE], 'readonly');
      const store = transaction.objectStore(USERS_STORE);
      const request = store.get(username);

      request.onsuccess = () => resolve(request.result);
      request.onerror = (event) => reject(event.target.error);
    });
  }

  // الحصول على جميع المستخدمين
  async getAllUsers() {
    const db = await this.getDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([USERS_STORE], 'readonly');
      const store = transaction.objectStore(USERS_STORE);
      const request = store.getAll();

      request.onsuccess = () => resolve(request.result);
      request.onerror = (event) => reject(event.target.error);
    });
  }

  // حذف مستخدم
  async deleteUser(username) {
    const db = await this.getDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([USERS_STORE], 'readwrite');
      const store = transaction.objectStore(USERS_STORE);
      const request = store.delete(username);

      request.onsuccess = () => resolve(true);
      request.onerror = (event) => reject(event.target.error);
    });
  }

  // ترحيل البيانات من localStorage إلى IndexedDB
  async migrateFromLocalStorage() {
    try {
      // ترحيل المهام
      const tasksJson = localStorage.getItem('tasks');
      if (tasksJson) {
        const tasks = JSON.parse(tasksJson);
        const transaction = this.db.transaction([TASKS_STORE], 'readwrite');
        const store = transaction.objectStore(TASKS_STORE);
        
        for (const task of tasks) {
          store.put(task);
        }
      }

      // ترحيل الإعدادات
      const settingsJson = localStorage.getItem('settings');
      if (settingsJson) {
        const settings = JSON.parse(settingsJson);
        await this.saveSettings(settings);
      }

      // ترحيل المستخدمين
      const usersJson = localStorage.getItem('users');
      if (usersJson) {
        const users = JSON.parse(usersJson);
        const transaction = this.db.transaction([USERS_STORE], 'readwrite');
        const store = transaction.objectStore(USERS_STORE);
        
        for (const user of users) {
          store.put(user);
        }
      }

      console.log('تم ترحيل البيانات من localStorage إلى IndexedDB بنجاح');
      return true;
    } catch (error) {
      console.error('خطأ في ترحيل البيانات:', error);
      return false;
    }
  }
}

// تصدير الكلاس للاستخدام في الملفات الأخرى
export { TDLDatabase };

// إنشاء نسخة واحدة من قاعدة البيانات للاستخدام في جميع أنحاء التطبيق
const db = new TDLDatabase();

// تصدير النسخة الافتراضية
export default db;

// تصدير الكائن للاستخدام في الملفات الأخرى (للتوافق مع الكود القديم)
window.TDLDatabase = db;