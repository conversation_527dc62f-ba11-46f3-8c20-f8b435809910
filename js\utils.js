// دالة لإنشاء عنصر HTML مع خصائص
function createElement(tag, attributes = {}, textContent = '') {
    const element = document.createElement(tag);
    
    // تعيين الخصائص
    for (const [key, value] of Object.entries(attributes)) {
        if (key === 'class') {
            element.className = value;
        } else if (key === 'data') {
            for (const [dataKey, dataValue] of Object.entries(value)) {
                element.dataset[dataKey] = dataValue;
            }
        } else if (key === 'style') {
            Object.assign(element.style, value);
        } else {
            element.setAttribute(key, value);
        }
    }
    
    // تعيين المحتوى النصي
    if (textContent) {
        element.textContent = textContent;
    }
    
    return element;
}

let lastNotification = null;
// دالة لعرض إشعار للمستخدم
function showNotification(message, type = 'info', position = 'bottom') {
    // إغلاق الإشعار السابق إذا كان من نفس النوع والنص
    if (lastNotification && lastNotification.text === message && lastNotification.type === type) {
        lastNotification.element.remove();
    }
    // إنشاء عنصر الإشعار
    const notification = createElement('div', {
        class: `notification ${type} notification-center`,
        'aria-live': 'polite'
    });
    
    // إضافة أيقونة حسب نوع الإشعار
    let iconClass = 'fa-info-circle';
    switch (type) {
        case 'success':
            iconClass = 'fa-check-circle';
            break;
        case 'error':
            iconClass = 'fa-exclamation-circle';
            break;
        case 'warning':
            iconClass = 'fa-exclamation-triangle';
            break;
    }
    
    // إضافة محتوى الإشعار
    notification.innerHTML = `
        <i class="fas ${iconClass}"></i>
        <span>${message}</span>
        <button class="close-notification" aria-label="إغلاق الإشعار">&times;</button>
    `;
    
    // جميع الرسائل تظهر في منتصف وأسفل الشاشة
    notification.style.position = 'fixed';
    notification.style.top = '';
    notification.style.bottom = '2.5rem';
    notification.style.left = '50%';
    notification.style.right = '';
    notification.style.transform = 'translateX(-50%)';
    notification.style.zIndex = '3000';
    notification.style.boxShadow = '0 8px 32px rgba(0,0,0,0.18)';
    document.body.appendChild(notification);
    
    // إظهار الإشعار بانتقال سلس
    setTimeout(() => notification.classList.add('show'), 10);
    
    // إغلاق الإشعار تلقائياً بعد 5 ثواني
    const timer = setTimeout(() => {
        closeNotification(notification);
    }, 5000);
    
    // إغلاق الإشعار يدوياً
    const closeBtn = notification.querySelector('.close-notification');
    closeBtn.addEventListener('click', () => {
        clearTimeout(timer);
        closeNotification(notification);
    });
    
    // إبقاء الإشعار مفتوحاً عند التحويم عليه
    notification.addEventListener('mouseenter', () => {
        clearTimeout(timer);
    });
    
    notification.addEventListener('mouseleave', () => {
        setTimeout(() => closeNotification(notification), 1000);
    });
    
    // دالة مساعدة لإغلاق الإشعار
    function closeNotification(element) {
        element.classList.remove('show');
        element.classList.add('fade-out');
        setTimeout(() => element.remove(), 300);
    }
    lastNotification = { element: notification, text: message, type };
}

// دالة لتنسيق التاريخ
function formatDate(dateString) {
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    
    return new Date(dateString).toLocaleDateString('ar-EG', options);
}

// دالة للتحقق من صحة النص (عدم كونه فارغاً أو مسافة بيضاء فقط)
function isValidText(text) {
    return typeof text === 'string' && text.trim().length > 0;
}

// دالة لنسخ النص إلى الحافظة
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        return true;
    } catch (err) {
        console.error('فشل في نسخ النص:', err);
        return false;
    }
}

// دالة للتحقق مما إذا كان العنصر مرئياً في نافذة العرض
function isElementInViewport(element) {
    if (!element) return false;
    
    const rect = element.getBoundingClientRect();
    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
}

// دالة لتقليل استدعاء الدوال (Debounce)
function debounce(func, delay = 300) {
    let timeoutId;
    return function (...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
            func.apply(this, args);
        }, delay);
    };
}

// دالة لتحسين الأداء - تخزين مؤقت للعناصر
const elementCache = new Map();

function getCachedElement(selector) {
    if (!elementCache.has(selector)) {
        elementCache.set(selector, document.querySelector(selector));
    }
    return elementCache.get(selector);
}

// دالة لتنظيف التخزين المؤقت
function clearElementCache() {
    elementCache.clear();
}

// دالة لتحسين التمرير
function smoothScrollTo(element, offset = 0) {
    if (!element) return;
    
    const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
    const offsetPosition = elementPosition - offset;
    
    window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
    });
}

// دالة لتحسين الأداء - تقليل إعادة الرسم
function batchDOMUpdates(updates) {
    requestAnimationFrame(() => {
        updates.forEach(update => update());
    });
}

// دالة لتحسين التحميل
function preloadImage(src) {
    return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve(img);
        img.onerror = reject;
        img.src = src;
    });
}

// تصدير الدوال للاستخدام في ملفات أخرى
export {
    createElement,
    showNotification,
    formatDate,
    isValidText,
    copyToClipboard,
    isElementInViewport,
    debounce,
    getCachedElement,
    clearElementCache,
    smoothScrollTo,
    batchDOMUpdates,
    preloadImage
};
