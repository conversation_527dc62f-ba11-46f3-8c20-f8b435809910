// TDL V1.0 - Simple User Management System
// نظام إدارة المستخدمين المبسط والفعال

console.log('🚀 تحميل Simple User Manager...');

class SimpleUserManager {
  constructor() {
    console.log('🔧 إنشاء SimpleUserManager...');
    this.currentUser = null;
    this.sessionTimeout = 24 * 60 * 60 * 1000; // 24 ساعة
    this.init();
  }

  // تهيئة النظام
  init() {
    console.log('⚙️ تهيئة SimpleUserManager...');
    this.checkSession();
  }

  // تحميل المستخدمين من التخزين المحلي
  loadUsers() {
    try {
      const users = localStorage.getItem('tdl_users');
      const result = users ? JSON.parse(users) : {};
      console.log('📂 تحميل المستخدمين:', Object.keys(result).length, 'مستخدم');
      return result;
    } catch (error) {
      console.error('❌ خطأ في تحميل المستخدمين:', error);
      return {};
    }
  }

  // حفظ المستخدمين في التخزين المحلي
  saveUsers(users) {
    try {
      localStorage.setItem('tdl_users', JSON.stringify(users));
      console.log('💾 تم حفظ المستخدمين بنجاح');
      return true;
    } catch (error) {
      console.error('❌ خطأ في حفظ المستخدمين:', error);
      if (error.name === 'QuotaExceededError') {
        alert('مساحة التخزين ممتلئة، يرجى حذف بعض البيانات');
      }
      return false;
    }
  }

  // تشفير كلمة المرور (بسيط)
  hashPassword(password) {
    let hash = 0;
    for (let i = 0; i < password.length; i++) {
      const char = password.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return hash.toString();
  }

  // التحقق من صحة البريد الإلكتروني
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // إنشاء صورة رمزية افتراضية
  generateAvatar(fullName) {
    const initials = fullName.split(' ').map(name => name[0]).join('').toUpperCase();
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
    const color = colors[Math.floor(Math.random() * colors.length)];
    
    return {
      type: 'initials',
      initials,
      backgroundColor: color,
      textColor: '#FFFFFF'
    };
  }

  // إنشاء حساب جديد
  async register(userData) {
    console.log('📝 بدء عملية التسجيل:', userData.username);
    
    const { username, email, password, fullName, avatar } = userData;

    // التحقق من صحة البيانات
    if (!username || !email || !password || !fullName) {
      throw new Error('جميع الحقول مطلوبة');
    }

    if (username.length < 3) {
      throw new Error('اسم المستخدم يجب أن يكون 3 أحرف على الأقل');
    }

    if (password.length < 6) {
      throw new Error('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
    }

    if (!this.isValidEmail(email)) {
      throw new Error('البريد الإلكتروني غير صالح');
    }

    // تحميل المستخدمين الحاليين
    const users = this.loadUsers();

    // التحقق من عدم وجود المستخدم
    if (users[username]) {
      throw new Error('اسم المستخدم موجود بالفعل');
    }

    if (Object.values(users).some(user => user.email === email)) {
      throw new Error('البريد الإلكتروني مستخدم بالفعل');
    }

    // إنشاء المستخدم الجديد
    const newUser = {
      username,
      email,
      password: this.hashPassword(password),
      fullName,
      avatar: avatar || this.generateAvatar(fullName),
      createdAt: new Date().toISOString(),
      lastLogin: null,
      settings: {
        theme: 'dark',
        language: 'ar',
        notifications: true
      },
      tasks: [],
      stats: {
        totalTasks: 0,
        completedTasks: 0,
        loginCount: 0
      }
    };

    // حفظ المستخدم
    users[username] = newUser;
    const saved = this.saveUsers(users);

    if (saved) {
      console.log('✅ تم إنشاء المستخدم بنجاح:', username);
      return { success: true, message: 'تم إنشاء الحساب بنجاح' };
    } else {
      throw new Error('فشل في حفظ بيانات المستخدم');
    }
  }

  // تسجيل الدخول
  async login(username, password) {
    console.log('🔐 بدء عملية تسجيل الدخول:', username);

    if (!username || !password) {
      throw new Error('اسم المستخدم وكلمة المرور مطلوبان');
    }

    const users = this.loadUsers();
    const user = users[username];

    if (!user) {
      throw new Error('اسم المستخدم غير موجود');
    }

    if (user.password !== this.hashPassword(password)) {
      throw new Error('كلمة المرور غير صحيحة');
    }

    // تحديث بيانات تسجيل الدخول
    user.lastLogin = new Date().toISOString();
    user.stats.loginCount++;
    users[username] = user;
    this.saveUsers(users);

    // إنشاء جلسة
    this.currentUser = user;
    this.createSession(user);

    console.log('✅ تم تسجيل الدخول بنجاح:', username);
    return { success: true, user: this.getSafeUserData(user) };
  }

  // إنشاء جلسة
  createSession(user) {
    const session = {
      username: user.username,
      loginTime: Date.now(),
      expiresAt: Date.now() + this.sessionTimeout
    };

    localStorage.setItem('tdl_session', JSON.stringify(session));
    localStorage.setItem('tdl_current_user', JSON.stringify(this.getSafeUserData(user)));
    console.log('🎫 تم إنشاء الجلسة:', user.username);
  }

  // فحص الجلسة
  checkSession() {
    try {
      const sessionData = localStorage.getItem('tdl_session');
      const currentUserData = localStorage.getItem('tdl_current_user');

      if (!sessionData || !currentUserData) {
        return false;
      }

      const session = JSON.parse(sessionData);
      const currentUser = JSON.parse(currentUserData);

      if (session && currentUser && Date.now() < session.expiresAt) {
        const users = this.loadUsers();
        const user = users[session.username];
        if (user) {
          this.currentUser = user;
          console.log('✅ الجلسة صالحة:', user.username);
          return true;
        }
      }
    } catch (error) {
      console.error('❌ خطأ في فحص الجلسة:', error);
    }

    this.logout();
    return false;
  }

  // تسجيل الخروج
  logout() {
    this.currentUser = null;
    localStorage.removeItem('tdl_session');
    localStorage.removeItem('tdl_current_user');
    console.log('👋 تم تسجيل الخروج');
  }

  // الحصول على بيانات المستخدم الآمنة
  getSafeUserData(user) {
    const { password, ...safeData } = user;
    return safeData;
  }

  // الحصول على المستخدم الحالي
  getCurrentUser() {
    return this.currentUser ? this.getSafeUserData(this.currentUser) : null;
  }

  // تحديث إحصائيات المستخدم
  updateUserStats(stats) {
    if (this.currentUser) {
      const users = this.loadUsers();
      const user = users[this.currentUser.username];
      if (user) {
        Object.assign(user.stats, stats);
        users[this.currentUser.username] = user;
        this.saveUsers(users);
        this.currentUser = user;
      }
    }
  }
}

// إنشاء نسخة واحدة من مدير المستخدمين
const simpleUserManager = new SimpleUserManager();

console.log('✅ تم إنشاء SimpleUserManager بنجاح');

// تصدير للاستخدام في الملفات الأخرى
export default simpleUserManager;

// إضافة إلى window للوصول العام
if (typeof window !== 'undefined') {
  window.simpleUserManager = simpleUserManager;
  window.userManager = simpleUserManager; // للتوافق مع الكود الموجود
  console.log('✅ تم إضافة simpleUserManager إلى window');
}
