// استيراد الدوال المساعدة
import { showNotification, isValidText, formatDate, debounce } from './utils.js';

// دالة تسجيل الخروج
function logout() {
    if (confirm('هل تريد تسجيل الخروج؟')) {
        localStorage.clear();
        window.location.reload();
    }
}

// تعريف المتغيرات العامة
let tasks = [];
let currentFilter = 'all';
let currentView = localStorage.getItem('viewMode') || 'list';
let currentCategoryFilter = 'all';
let pendingDeleteAction = null;
let searchQuery = '';
let pendingDeleteTaskId = null;

// العناصر الرئيسية
const elements = {
    taskInput: null,
    addTaskBtn: null,
    taskList: null,
    taskGrid: null,
    taskCount: null,
    clearAllBtn: null,
    clearCompletedBtn: null,
    themeToggle: null,
    filterButtons: null,
    viewToggleButtons: null,
    emptyState: null,
    listView: null,
    gridView: null,
    categoryInput: null,
    categoryFilterContainer: null,
    searchInput: null,
    statsBar: null,
    attachmentInput: null
};

// ========== إعدادات متقدمة وتخصيص واجهة الإعدادات ==========

const settingsModal = document.getElementById('settingsModal');
const closeSettingsModal = document.getElementById('closeSettingsModal');
const saveSettingsBtn = document.getElementById('saveSettingsBtn');
const cancelSettingsBtn = document.getElementById('cancelSettingsBtn');
const resetSettingsBtn = document.getElementById('resetSettingsBtn');
const deleteAllTasksBtn = document.getElementById('deleteAllTasksBtn');
const exportTasksBtn = document.getElementById('exportTasksBtn');
const importTasksBtn = document.getElementById('importTasksBtn');

// عناصر الإعدادات
const primaryColorSelect = document.getElementById('primaryColorSelect');
const themeSelect = document.getElementById('themeSelect');
const notifSound = document.getElementById('notifSound');
const notifPosition = document.getElementById('notifPosition');
const notifDuration = document.getElementById('notifDuration');
const confirmDelete = document.getElementById('confirmDelete');
const undoDelete = document.getElementById('undoDelete');
const defaultView = document.getElementById('defaultView');
const addCompleted = document.getElementById('addCompleted');
const instantSearch = document.getElementById('instantSearch');
const searchScope = document.getElementById('searchScope');
const fontSize = document.getElementById('fontSize');
const roundedBorders = document.getElementById('roundedBorders');
const shadows = document.getElementById('shadows');
const animations = document.getElementById('animations');

// ========== Accordion منطق ========== //
document.querySelectorAll('.settings-section .accordion-toggle').forEach(btn => {
  btn.addEventListener('click', function(e) {
    const section = btn.closest('.settings-section');
    const isCollapsed = section.classList.toggle('collapsed');
    section.classList.toggle('active', !isCollapsed);
    // تدوير السهم
    btn.querySelector('i').style.transform = isCollapsed ? 'rotate(0deg)' : 'rotate(180deg)';
  });
});
// افتراضيًا: أول قسم مفتوح
setTimeout(() => {
  const firstSection = document.querySelector('.settings-section');
  if (firstSection) {
    firstSection.classList.add('active');
    firstSection.classList.remove('collapsed');
    const icon = firstSection.querySelector('.accordion-toggle i');
    if (icon) icon.style.transform = 'rotate(180deg)';
  }
}, 200);

// ========== تحميل/حفظ الإعدادات من/إلى localStorage ==========
const SETTINGS_KEY = 'tdl_settings';
let settings = {
  primaryColor: '#1976d2',
  theme: 'auto',
  notifSound: false,
  notifPosition: 'bottom',
  notifDuration: 'normal',
  confirmDelete: true,
  undoDelete: true,
  defaultView: 'list',
  addCompleted: false,
  instantSearch: true,
  searchScope: 'all',
  fontSize: 'medium',
  roundedBorders: true,
  shadows: true,
  animations: true
};
function loadSettings() {
  const saved = localStorage.getItem(SETTINGS_KEY);
  if (saved) {
    try {
      settings = { ...settings, ...JSON.parse(saved) };
    } catch {}
  }
  
  // عكس القيم على الواجهة مع فحص وجود العناصر
  if (primaryColorSelect) primaryColorSelect.value = settings.primaryColor;
  if (themeSelect) themeSelect.value = settings.theme;
  if (notifSound) notifSound.checked = settings.notifSound;
  if (notifPosition) notifPosition.value = settings.notifPosition;
  if (notifDuration) notifDuration.value = settings.notifDuration;
  if (confirmDelete) confirmDelete.checked = settings.confirmDelete;
  if (undoDelete) undoDelete.checked = settings.undoDelete;
  if (defaultView) defaultView.value = settings.defaultView;
  if (addCompleted) addCompleted.checked = settings.addCompleted;
  if (instantSearch) instantSearch.checked = settings.instantSearch;
  if (searchScope) searchScope.value = settings.searchScope;
  if (fontSize) fontSize.value = settings.fontSize;
  if (roundedBorders) roundedBorders.checked = settings.roundedBorders;
  if (shadows) shadows.checked = settings.shadows;
  if (animations) animations.checked = settings.animations;
}
function saveSettings() {
  localStorage.setItem(SETTINGS_KEY, JSON.stringify(settings));
}
// ========== ربط كل خيار بوظيفته ==========
function applySettings() {
  // اللون الرئيسي
  document.documentElement.style.setProperty('--primary', settings.primaryColor);
  // الثيم
  if (settings.theme === 'dark') {
    document.documentElement.setAttribute('data-theme', 'dark');
  } else if (settings.theme === 'light') {
    document.documentElement.setAttribute('data-theme', 'light');
  } else {
    // تلقائي حسب النظام
    if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
      document.documentElement.setAttribute('data-theme', 'dark');
    } else {
      document.documentElement.setAttribute('data-theme', 'light');
    }
  }
  // حجم الخط
  document.body.style.fontSize = settings.fontSize === 'small' ? '14px' : settings.fontSize === 'large' ? '18px' : '16px';
  // الحواف الدائرية
  document.body.classList.toggle('rounded', settings.roundedBorders);
  // الظلال
  document.body.classList.toggle('shadows', settings.shadows);
  // الحركات
  document.body.classList.toggle('animations', settings.animations);
  // طريقة العرض الافتراضية
  if (typeof setTasksView === 'function') setTasksView(settings.defaultView);
  // البحث الفوري
  if (typeof setInstantSearch === 'function') setInstantSearch(settings.instantSearch);
  // ... يمكن ربط باقي الخيارات حسب الحاجة ...
}
// ========== أحداث التغيير لكل خيار ==========
function bindSettingsEvents() {
  // التحقق من وجود العناصر قبل تعيين الأحداث
  if (primaryColorSelect) {
    primaryColorSelect.onchange = e => { 
      settings.primaryColor = e.target.value; 
      applySettings(); 
    };
  }
  if (themeSelect) {
    themeSelect.onchange = e => { 
      settings.theme = e.target.value; 
      applySettings(); 
    };
  }
  if (notifSound) {
    notifSound.onchange = e => { 
      settings.notifSound = e.target.checked; 
    };
  }
  if (notifPosition) {
    notifPosition.onchange = e => { 
      settings.notifPosition = e.target.value; 
    };
  };
  if (notifDuration) {
    notifDuration.onchange = e => { 
      settings.notifDuration = e.target.value; 
    };
  }
  if (confirmDelete) {
    confirmDelete.onchange = e => { 
      settings.confirmDelete = e.target.checked; 
    };
  }
  if (undoDelete) {
    undoDelete.onchange = e => { 
      settings.undoDelete = e.target.checked; 
    };
  }
  if (defaultView) {
    defaultView.onchange = e => { 
      settings.defaultView = e.target.value; 
      applySettings(); 
    };
  }
  if (addCompleted) {
    addCompleted.onchange = e => { 
      settings.addCompleted = e.target.checked; 
    };
  }
  if (instantSearch) {
    instantSearch.onchange = e => { 
      settings.instantSearch = e.target.checked; 
      if (typeof setInstantSearch === 'function') setInstantSearch(settings.instantSearch); 
    };
  }
  if (searchScope) {
    searchScope.onchange = e => { 
      settings.searchScope = e.target.value; 
    };
  }
  if (fontSize) {
    fontSize.onchange = e => { 
      settings.fontSize = e.target.value; 
      applySettings(); 
    };
  }
  if (roundedBorders) {
    roundedBorders.onchange = e => { 
      settings.roundedBorders = e.target.checked; 
      applySettings(); 
    };
  }
  if (shadows) {
    shadows.onchange = e => { 
      settings.shadows = e.target.checked; 
      applySettings(); 
    };
  }
  if (animations) {
    animations.onchange = e => { 
      settings.animations = e.target.checked; 
      applySettings(); 
    };
  }
}
// ========== منطق الحفظ/الإلغاء/إعادة التعيين ==========
let settingsBackup = null;
function openSettingsModal() {
  loadSettings();
  applySettings();
  settingsBackup = JSON.stringify(settings);
  settingsModal.style.display = 'flex';
}
function closeSettings() {
  settingsModal.style.display = 'none';
  // استرجاع النسخة الاحتياطية إذا أُلغي
  if (settingsBackup) {
    settings = { ...settings, ...JSON.parse(settingsBackup) };
    applySettings();
  }
}
saveSettingsBtn.onclick = function() {
  // حفظ إعدادات الثيم
  saveThemeSettings();
  
  // حفظ باقي الإعدادات
  saveSettings();
  
  // تطبيق جميع الإعدادات على الموقع
  applySettings();
  applyThemeSettings();
  
  // إغلاق النافذة
  settingsModal.style.display = 'none';
  
  // رسالة تأكيد
  showNotification('تم حفظ الإعدادات بنجاح!', 'success');
};
cancelSettingsBtn.onclick = closeSettingsModal.onclick = closeSettings;
resetSettingsBtn && (resetSettingsBtn.onclick = function() {
  if (confirm('هل أنت متأكد أنك تريد إعادة تعيين جميع الإعدادات؟')) {
    localStorage.removeItem(SETTINGS_KEY);
    settings = {
      primaryColor: '#1976d2',
      theme: 'auto',
      notifSound: false,
      notifPosition: 'bottom',
      notifDuration: 'normal',
      confirmDelete: true,
      undoDelete: true,
      defaultView: 'list',
      addCompleted: false,
      instantSearch: true,
      searchScope: 'all',
      fontSize: 'medium',
      roundedBorders: true,
      shadows: true,
      animations: true
    };
    loadSettings();
    applySettings();
    saveSettings();
  }
});
// ========== تصدير/استيراد المهام ==========
exportTasksBtn && (exportTasksBtn.onclick = function() {
  if (typeof exportTasks === 'function') exportTasks();
});
importTasksBtn && (importTasksBtn.onclick = function() {
  if (typeof importTasks === 'function') importTasks();
});
// ========== حذف جميع المهام ==========
deleteAllTasksBtn && (deleteAllTasksBtn.onclick = function() {
  if (confirm('هل أنت متأكد أنك تريد حذف جميع المهام؟')) {
    if (typeof deleteAllTasks === 'function') deleteAllTasks();
  }
});
// ========== تفعيل الإعدادات عند التحميل ==========
window.addEventListener('DOMContentLoaded', () => {
  loadSettings();
  applySettings();
  bindSettingsEvents();
});


// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // تفعيل الدارك مود تلقائيًا عند أول دخول أو حسب تفضيل المستخدم
    let darkPref = localStorage.getItem('darkMode');
    if (darkPref === null) {
        darkPref = 'true';
        localStorage.setItem('darkMode', 'true');
    }
    if (darkPref === 'true') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        if (elements.themeToggle) {
            elements.themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
            elements.themeToggle.classList.add('active');
        }
    } else {
        document.body.classList.remove('dark-mode');
        document.documentElement.removeAttribute('data-theme');
        if (elements.themeToggle) {
            elements.themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
            elements.themeToggle.classList.remove('active');
        }
    }
    try {
        initElements();
        setupEventListeners();
        loadAndRenderTasks();
        applySavedSettings();
        setupDeleteConfirmationEvents();
        setupSideMenuEvents();
        console.log('تم تحميل التطبيق بنجاح');
    } catch (error) {
        console.error('حدث خطأ أثناء تحميل التطبيق:', error);
        showNotification('حدث خطأ أثناء تحميل التطبيق', 'error');
    }
    const menuToggle = document.getElementById('menuToggle');
    const sideMenu = document.getElementById('sideMenu');
    const closeBtn = document.getElementById('closeSideMenu');
    const overlay = document.getElementById('sidebarOverlay');

    function openSidebar() {
        sideMenu.classList.add('active');
        overlay.style.display = 'block';
        document.body.classList.add('sidebar-open');
    }
    function closeSidebar() {
        sideMenu.classList.remove('active');
        overlay.style.display = 'none';
        document.body.classList.remove('sidebar-open');
    }
    if (menuToggle) menuToggle.addEventListener('click', openSidebar);
    if (closeBtn) closeBtn.addEventListener('click', closeSidebar);
    if (overlay) overlay.addEventListener('click', closeSidebar);
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && sideMenu.classList.contains('active')) closeSidebar();
    });
    document.addEventListener('click', globalCloseForms);
    // تفعيل خاصية سحب القائمة الجانبية
    setupSidebarResize();

    // فتح نافذة الإعدادات عند الضغط على زر الإعدادات
    const settingsBtn = document.getElementById('settingsMenu');
    if (settingsBtn) settingsBtn.onclick = openSettingsModal;

    function openSettingsModal() {
        const modal = document.getElementById('settingsModal');
        if (modal) modal.style.display = 'flex';
    }

    // إغلاق نافذة الإعدادات عند الضغط على زر الإغلاق أو خارج الصندوق
    const closeSettingsBtn = document.getElementById('closeSettingsModal');
    if (closeSettingsBtn) closeSettingsBtn.onclick = closeSettingsModal;

    function closeSettingsModal() {
        const modal = document.getElementById('settingsModal');
        if (modal) modal.style.display = 'none';
    }

    document.addEventListener('mousedown', (e) => {
        const modal = document.getElementById('settingsModal');
        const box = document.querySelector('.settings-modal-box');
        if (modal && box && modal.style.display !== 'none' && !box.contains(e.target) && !e.target.closest('#settingsMenu')) {
            modal.style.display = 'none';
        }
    });
});

// تهيئة عناصر واجهة المستخدم
function initElements() {
    elements.taskInput = document.getElementById('taskInput');
    elements.addTaskBtn = document.getElementById('addTaskBtn');
    elements.taskList = document.getElementById('taskList');
    elements.taskGrid = document.getElementById('gridView');
    elements.taskCount = document.getElementById('taskCount');
    elements.clearAllBtn = document.getElementById('clearAllBtn');
    elements.clearCompletedBtn = document.getElementById('clearCompletedBtn');
    elements.themeToggle = document.getElementById('themeToggle');
    elements.filterButtons = document.querySelectorAll('.filter-btn');
    elements.viewToggleButtons = document.querySelectorAll('.view-btn');
    elements.emptyState = document.getElementById('emptyState');
    elements.listView = document.getElementById('listView');
    elements.gridView = document.getElementById('gridView');
    elements.categoryInput = document.getElementById('categoryInput');
    elements.categoryFilterContainer = document.getElementById('categoryFilterContainer');
    elements.searchInput = document.getElementById('searchInput');
    elements.statsBar = document.querySelector('.stats-bar');
    elements.attachmentInput = document.getElementById('attachmentInput');
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    console.log('تشغيل setupEventListeners...');
    
    // إضافة مهمة جديدة
    if (elements.addTaskBtn && elements.taskInput) {
        elements.addTaskBtn.onclick = addTask;
        elements.taskInput.onkeypress = (e) => {
            if (e.key === 'Enter') {
                if (elements.taskInput.value.trim() === '') {
                    searchTasks();
                } else {
                    addTask();
                }
            }
        };
    }
    
    // تفعيل الفلاتر في القائمة الجانبية
    setupSidebarFilters();
    
    // تفعيل الأقسام في القائمة الجانبية
    setupSidebarCategories();
    
    // تفعيل أزرار العرض (قائمة/شبكة)
    setupViewToggle();
    
    // إعداد واجهة البحث الجديدة
    setupSearchInterface();
    
    // زر البحث - فتح واجهة البحث الجديدة
    const searchBtn = document.getElementById('searchTaskBtn');
    if (searchBtn) searchBtn.onclick = openSearchInterface;
    
    // إغلاق صندوق البحث
    const closeSearchBtn = document.getElementById('closeSearchBtn');
    if (closeSearchBtn) closeSearchBtn.onclick = closeSearchBox;
    
    // البحث في صندوق البحث المحسن
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', (e) => {
            searchTasks(e.target.value);
        });
        
        searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeSearchBox();
            }
        });
    }
    
    // إغلاق البحث بالنقر خارج الصندوق
    const searchBox = document.getElementById('searchBox');
    if (searchBox) {
        searchBox.addEventListener('click', (e) => {
            if (e.target === searchBox) {
                closeSearchBox();
            }
        });
    }

    function openSearchInputModal() {
        // إنشاء طبقة التعتيم المتقدمة
        const overlay = document.createElement('div');
        overlay.className = 'advanced-search-overlay';
        overlay.tabIndex = -1;

        // إنشاء صندوق البحث المتقدم
        const modal = document.createElement('div');
        modal.className = 'advanced-search-modal';
        modal.tabIndex = 0;

        // رأس البحث
        const header = document.createElement('div');
        header.className = 'search-header';
        header.innerHTML = `
            <div class="search-title">
                <i class="fas fa-search"></i>
                <h3>البحث المتقدم والديناميكي</h3>
            </div>
            <button class="search-close-btn" title="إغلاق البحث">
                <i class="fas fa-times"></i>
            </button>
        `;
        modal.appendChild(header);

        // منطقة البحث الرئيسية
        const searchArea = document.createElement('div');
        searchArea.className = 'search-input-area';
        
        // حقل البحث الرئيسي مع اقتراحات
        const mainInput = document.createElement('div');
        mainInput.className = 'main-search-input';
        mainInput.innerHTML = `
            <i class="fas fa-search search-icon"></i>
            <input type="text" id="advancedSearchInput" placeholder="اكتب للبحث الفوري..." autocomplete="off">
            <button class="clear-search-btn" title="مسح البحث">
                <i class="fas fa-times"></i>
            </button>
            <div class="search-suggestions" id="searchSuggestions"></div>
        `;
        searchArea.appendChild(mainInput);

        // خيارات البحث المتقدمة
        const advancedOptions = document.createElement('div');
        advancedOptions.className = 'search-options';
        advancedOptions.innerHTML = `
            <div class="search-filters">
                <div class="filter-group">
                    <label>البحث في:</label>
                    <div class="filter-buttons">
                        <button class="filter-btn active" data-scope="all">
                            <i class="fas fa-globe"></i>
                            الكل
                        </button>
                        <button class="filter-btn" data-scope="text">
                            <i class="fas fa-font"></i>
                            النص
                        </button>
                        <button class="filter-btn" data-scope="category">
                            <i class="fas fa-tag"></i>
                            التصنيف
                        </button>
                        <button class="filter-btn" data-scope="priority">
                            <i class="fas fa-exclamation-triangle"></i>
                            الأولوية
                        </button>
                        <button class="filter-btn" data-scope="date">
                            <i class="fas fa-calendar"></i>
                            التاريخ
                        </button>
                    </div>
                </div>
                <div class="filter-group">
                    <label>حالة المهمة:</label>
                    <div class="filter-buttons">
                        <button class="filter-btn active" data-status="all">
                            <i class="fas fa-list"></i>
                            الكل
                        </button>
                        <button class="filter-btn" data-status="active">
                            <i class="fas fa-clock"></i>
                            النشطة
                        </button>
                        <button class="filter-btn" data-status="completed">
                            <i class="fas fa-check"></i>
                            المكتملة
                        </button>
                    </div>
                </div>
                <div class="filter-group">
                    <label>خيارات متقدمة:</label>
                    <div class="advanced-search-options">
                        <label class="checkbox-option">
                            <input type="checkbox" id="exactMatch">
                            <span class="checkmark"></span>
                            تطابق دقيق
                        </label>
                        <label class="checkbox-option">
                            <input type="checkbox" id="caseSensitive">
                            <span class="checkmark"></span>
                            حساس لحالة الأحرف
                        </label>
                        <label class="checkbox-option">
                            <input type="checkbox" id="highlightResults">
                            <span class="checkmark"></span>
                            تمييز النتائج
                        </label>
                    </div>
                </div>
            </div>
        `;
        searchArea.appendChild(advancedOptions);

        // منطقة النتائج المحسنة
        const resultsArea = document.createElement('div');
        resultsArea.className = 'search-results-area';
        resultsArea.innerHTML = `
            <div class="results-header">
                <div class="results-info">
                    <span class="results-count">0 نتيجة</span>
                    <span class="search-time" id="searchTime"></span>
                </div>
                <div class="results-actions">
                    <button class="action-btn" id="selectAllResults" title="تحديد الكل">
                        <i class="fas fa-check-square"></i>
                    </button>
                    <button class="action-btn" id="exportResults" title="تصدير النتائج">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="action-btn" id="sortResults" title="ترتيب النتائج">
                        <i class="fas fa-sort"></i>
                    </button>
                </div>
            </div>
            <div class="results-list" id="searchResultsList">
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <p>ابدأ بالكتابة للبحث الفوري في المهام</p>
                    <small>يمكنك البحث في النص، التصنيف، الأولوية، والتاريخ</small>
                </div>
            </div>
        `;
        searchArea.appendChild(resultsArea);

        modal.appendChild(searchArea);
        overlay.appendChild(modal);
        document.body.appendChild(overlay);

        // إعداد الأحداث
        setupAdvancedSearchEvents(overlay, modal, mainInput, resultsArea);
        
        // التركيز على حقل البحث
        setTimeout(() => {
            const input = mainInput.querySelector('input');
            if (input) input.focus();
        }, 100);
    }

    function setupAdvancedSearchEvents(overlay, modal, mainInput, resultsArea) {
        const input = mainInput.querySelector('input');
        const clearBtn = mainInput.querySelector('.clear-search-btn');
        const closeBtn = modal.querySelector('.search-close-btn');
        const resultsList = resultsArea.querySelector('#searchResultsList');
        const resultsCount = resultsArea.querySelector('.results-count');
        const searchTime = resultsArea.querySelector('#searchTime');
        const suggestions = mainInput.querySelector('#searchSuggestions');

        // إغلاق البحث
        closeBtn.onclick = () => {
            overlay.remove();
            searchQuery = '';
            renderTasks();
        };

        // مسح البحث
        clearBtn.onclick = () => {
            input.value = '';
            input.focus();
            searchQuery = '';
            suggestions.innerHTML = '';
            performAdvancedSearch('', resultsList, resultsCount, searchTime);
        };

        // البحث الفوري مع debounce محسن
        const debouncedSearch = createDebounce((query) => {
            const startTime = performance.now();
            performAdvancedSearch(query, resultsList, resultsCount, searchTime, startTime);
        }, 100); // تقليل وقت الانتظار للبحث الفوري

        // البحث الفوري مع اقتراحات
        input.addEventListener('input', (e) => {
            const query = e.target.value.trim();
            searchQuery = query;
            
            // عرض اقتراحات فورية
            if (query.length > 0) {
                showSearchSuggestions(query, suggestions, input);
            } else {
                suggestions.innerHTML = '';
            }
            
            debouncedSearch(query);
        });

        // اختصارات لوحة المفاتيح المحسنة
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                overlay.remove();
                searchQuery = '';
                renderTasks();
            } else if (e.key === 'Enter') {
                if (input.value.trim() === '') {
                    modal.classList.remove('shake');
                    void modal.offsetWidth;
                    modal.classList.add('shake');
                    input.focus();
                } else {
                    // تنفيذ البحث المباشر
                    const query = input.value.trim();
                    const startTime = performance.now();
                    performAdvancedSearch(query, resultsList, resultsCount, searchTime, startTime);
                }
            } else if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                // التنقل في الاقتراحات
                navigateSuggestions(e, suggestions, input);
            }
        });

        // أحداث الفلاتر مع تحديث فوري
        modal.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                btn.parentElement.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                const query = input.value.trim();
                const startTime = performance.now();
                performAdvancedSearch(query, resultsList, resultsCount, searchTime, startTime);
            });
        });

        // أحداث الخيارات المتقدمة
        modal.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                const query = input.value.trim();
                if (query) {
                    const startTime = performance.now();
                    performAdvancedSearch(query, resultsList, resultsCount, searchTime, startTime);
                }
            });
        });

        // إغلاق عند الضغط خارج الصندوق
        overlay.addEventListener('mousedown', (e) => {
            if (e.target === overlay) {
                overlay.remove();
                searchQuery = '';
                renderTasks();
            }
        });

        // إخفاء الاقتراحات عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (!mainInput.contains(e.target)) {
                suggestions.innerHTML = '';
                
                // البحث عند النقر خارج الصندوق إذا كان هناك نص
                const query = mainInput.value.trim();
                if (query.length > 0) {
                    const startTime = performance.now();
                    performAdvancedSearch(query, resultsList, resultsCount, searchTime, startTime);
                }
            }
        });
    }

    function showSearchSuggestions(query, suggestionsContainer, input) {
        if (query.length < 2) {
            suggestionsContainer.innerHTML = '';
            return;
        }

        const suggestions = generateSearchSuggestions(query);
        
        if (suggestions.length === 0) {
            suggestionsContainer.innerHTML = '';
            return;
        }

        const suggestionsHTML = suggestions.map((suggestion, index) => `
            <div class="suggestion-item" data-index="${index}" onclick="selectSuggestion('${suggestion.text}', '${suggestion.type}')">
                <i class="fas fa-${suggestion.icon}"></i>
                <span class="suggestion-text">${suggestion.text}</span>
                <span class="suggestion-type">${suggestion.type}</span>
            </div>
        `).join('');

        suggestionsContainer.innerHTML = suggestionsHTML;
        suggestionsContainer.style.display = 'block';
    }

    function generateSearchSuggestions(query) {
        const suggestions = [];
        const lowerQuery = query.toLowerCase();
        
        // تحسين الأداء: البحث فقط إذا كان النص طويلاً بما يكفي
        if (query.length < 2) return suggestions;

        // اقتراحات من النصوص الموجودة (محسنة)
        const textMatches = tasks
            .filter(task => task.text && task.text.toLowerCase().includes(lowerQuery))
            .slice(0, 3) // تقليل عدد النتائج
            .map(task => ({
                text: task.text.length > 40 ? task.text.substring(0, 40) + '...' : task.text,
                type: 'نص مهمة',
                icon: 'font'
            }));

        suggestions.push(...textMatches);

        // اقتراحات من التصنيفات (محسنة)
        const categories = [...new Set(tasks.map(task => task.category).filter(Boolean))];
        const categoryMatches = categories
            .filter(category => category.toLowerCase().includes(lowerQuery))
            .slice(0, 2)
            .map(category => ({
                text: category,
                type: 'تصنيف',
                icon: 'tag'
            }));

        suggestions.push(...categoryMatches);

        // اقتراحات من الأولويات
        const priorities = ['عاجل', 'عالية', 'متوسطة', 'منخفضة'];
        const priorityMatches = priorities
            .filter(priority => priority.includes(query))
            .map(priority => ({
                text: priority,
                type: 'أولوية',
                icon: 'exclamation-triangle'
            }));

        suggestions.push(...priorityMatches);

        // إزالة التكرار وتحسين الأداء
        const uniqueSuggestions = [];
        const seen = new Set();
        
        for (const suggestion of suggestions) {
            if (!seen.has(suggestion.text)) {
                seen.add(suggestion.text);
                uniqueSuggestions.push(suggestion);
                if (uniqueSuggestions.length >= 4) break; // تقليل العدد الإجمالي
            }
        }

        return uniqueSuggestions;
    }

    function selectSuggestion(text, type) {
        const input = document.getElementById('advancedSearchInput');
        if (input) {
            input.value = text;
            input.focus();
            
            // تنفيذ البحث مباشرة
            const resultsList = document.getElementById('searchResultsList');
            const resultsCount = document.querySelector('.results-count');
            const searchTime = document.getElementById('searchTime');
            
            if (resultsList && resultsCount && searchTime) {
                const startTime = performance.now();
                performAdvancedSearch(text, resultsList, resultsCount, searchTime, startTime);
            }
        }
    }

    function navigateSuggestions(event, suggestionsContainer, input) {
        const items = suggestionsContainer.querySelectorAll('.suggestion-item');
        const currentIndex = parseInt(suggestionsContainer.querySelector('.suggestion-item.selected')?.dataset.index || -1);
        
        if (items.length === 0) return;

        event.preventDefault();
        
        let newIndex;
        if (event.key === 'ArrowDown') {
            newIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
        } else {
            newIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
        }

        items.forEach(item => item.classList.remove('selected'));
        items[newIndex].classList.add('selected');
        
        const selectedText = items[newIndex].querySelector('.suggestion-text').textContent;
        input.value = selectedText;
    }

    function performAdvancedSearch(query, resultsList, resultsCount, searchTime, startTime) {
        if (!query.trim()) {
            resultsList.innerHTML = `
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <p>ابدأ بالكتابة للبحث الفوري في المهام</p>
                    <small>يمكنك البحث في النص، التصنيف، الأولوية، والتاريخ</small>
                </div>
            `;
            resultsCount.textContent = '0 نتيجة';
            if (searchTime) searchTime.textContent = '';
            
            // إعادة عرض جميع المهام في الواجهة الرئيسية
            renderTasks();
            return;
        }

        // الحصول على الفلاتر والخيارات النشطة
        const searchScope = document.querySelector('.filter-btn[data-scope].active')?.dataset.scope || 'all';
        const searchStatus = document.querySelector('.filter-btn[data-status].active')?.dataset.status || 'all';
        const exactMatch = document.getElementById('exactMatch')?.checked || false;
        const caseSensitive = document.getElementById('caseSensitive')?.checked || false;
        const highlightResults = document.getElementById('highlightResults')?.checked || true;

        // فلترة المهام
        let filteredTasks = [...tasks];
        
        // فلترة حسب الحالة
        if (searchStatus !== 'all') {
            filteredTasks = filteredTasks.filter(task => {
                if (searchStatus === 'active') return !task.completed;
                if (searchStatus === 'completed') return task.completed;
                return true;
            });
        }

        // فلترة حسب النص مع خيارات متقدمة
        const searchTerms = query.split(' ').filter(term => term.length > 0);
        filteredTasks = filteredTasks.filter(task => {
            return searchTerms.every(term => {
                const searchTerm = caseSensitive ? term : term.toLowerCase();
                
                switch (searchScope) {
                    case 'text':
                        return searchInText(task.text, searchTerm, exactMatch, caseSensitive);
                    case 'category':
                        return searchInText(task.category, searchTerm, exactMatch, caseSensitive);
                    case 'priority':
                        return searchInText(task.priority, searchTerm, exactMatch, caseSensitive);
                    case 'date':
                        return searchInDate(task.date, searchTerm);
                    case 'all':
                    default:
                        return searchInText(task.text, searchTerm, exactMatch, caseSensitive) ||
                               searchInText(task.category, searchTerm, exactMatch, caseSensitive) ||
                               searchInText(task.priority, searchTerm, exactMatch, caseSensitive) ||
                               searchInDate(task.date, searchTerm);
                }
            });
        });

        // ترتيب النتائج حسب الأهمية
        filteredTasks = sortSearchResults(filteredTasks, query, searchScope);

        // حساب وقت البحث
        const endTime = performance.now();
        const searchDuration = endTime - startTime;
        
        if (searchTime) {
            searchTime.textContent = `(${searchDuration.toFixed(1)}ms)`;
        }

        // عرض النتائج في صندوق البحث
        displaySearchResults(filteredTasks, resultsList, resultsCount, query, highlightResults);
        
        // عرض النتائج في الواجهة الرئيسية أيضاً
        displaySearchResultsInMainInterface(filteredTasks, query, highlightResults);
    }

    function searchInText(text, searchTerm, exactMatch, caseSensitive) {
        if (!text) return false;
        
        const textToSearch = caseSensitive ? text : text.toLowerCase();
        const termToSearch = caseSensitive ? searchTerm : searchTerm.toLowerCase();
        
        if (exactMatch) {
            return textToSearch === termToSearch;
        } else {
            return textToSearch.includes(termToSearch);
        }
    }

    function searchInDate(date, searchTerm) {
        if (!date) return false;
        
        const dateStr = new Date(date).toLocaleDateString('ar-SA');
        const searchLower = searchTerm.toLowerCase();
        
        return dateStr.includes(searchLower) || 
               date.toLowerCase().includes(searchLower);
    }

    function sortSearchResults(tasks, query, scope) {
        const queryLower = query.toLowerCase();
        
        return tasks.sort((a, b) => {
            let scoreA = 0;
            let scoreB = 0;
            
            // حساب نقاط التطابق (محسن)
            const aText = a.text ? a.text.toLowerCase() : '';
            const bText = b.text ? b.text.toLowerCase() : '';
            const aCategory = a.category ? a.category.toLowerCase() : '';
            const bCategory = b.category ? b.category.toLowerCase() : '';
            const aPriority = a.priority ? a.priority.toLowerCase() : '';
            const bPriority = b.priority ? b.priority.toLowerCase() : '';
            
            // تطابق في النص (أعلى وزن)
            if (aText.includes(queryLower)) scoreA += 15;
            if (bText.includes(queryLower)) scoreB += 15;
            
            // تطابق في التصنيف
            if (aCategory.includes(queryLower)) scoreA += 8;
            if (bCategory.includes(queryLower)) scoreB += 8;
            
            // تطابق في الأولوية
            if (aPriority.includes(queryLower)) scoreA += 5;
            if (bPriority.includes(queryLower)) scoreB += 5;
            
            // الأولوية للمهام النشطة
            if (!a.completed) scoreA += 3;
            if (!b.completed) scoreB += 3;
            
            // الأولوية للمهام العاجلة
            if (a.priority === 'urgent') scoreA += 8;
            if (b.priority === 'urgent') scoreB += 8;
            
            // الأولوية للمهام عالية الأولوية
            if (a.priority === 'high') scoreA += 4;
            if (b.priority === 'high') scoreB += 4;
            
            return scoreB - scoreA;
        });
    }

    function displaySearchResults(tasks, resultsList, resultsCount, query, highlightResults) {
        if (tasks.length === 0) {
            resultsList.innerHTML = `
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <p>لا توجد نتائج تطابق "${query}"</p>
                    <small>جرب كلمات مختلفة أو غير الفلتر أو استخدم خيارات البحث المتقدمة</small>
                </div>
            `;
            resultsCount.textContent = '0 نتيجة';
            return;
        }

        resultsCount.textContent = `${tasks.length} نتيجة`;
        
        const resultsHTML = tasks.map((task, index) => {
            const highlightedText = highlightResults ? highlightSearchTerms(task.text, query) : task.text;
            const highlightedCategory = highlightResults ? highlightSearchTerms(task.category || 'عام', query) : (task.category || 'عام');
            const highlightedPriority = highlightResults ? highlightSearchTerms(getPriorityText(task.priority || 'medium'), query) : getPriorityText(task.priority || 'medium');
            const isCompleted = task.completed ? 'completed' : '';
            const priorityClass = task.priority || 'medium';
            
            // حساب درجة التطابق
            const matchScore = calculateMatchScore(task, query);
            const matchIndicator = matchScore > 80 ? 'excellent' : matchScore > 60 ? 'good' : 'fair';
            
            return `
                <div class="search-result-item ${isCompleted} ${matchIndicator}-match" data-task-id="${task.id}">
                    <div class="result-content">
                        <div class="result-header">
                            <span class="result-number">#${index + 1}</span>
                            <span class="match-score" title="درجة التطابق: ${matchScore}%">
                                <i class="fas fa-${matchScore > 80 ? 'star' : matchScore > 60 ? 'star-half-alt' : 'circle'}"></i>
                                ${matchScore}%
                            </span>
                        </div>
                        <div class="result-text">
                            <div class="result-title">${highlightedText}</div>
                            ${task.date ? `<div class="result-date">
                                <i class="fas fa-calendar"></i>
                                ${highlightSearchTerms(new Date(task.date).toLocaleDateString('ar-SA'), query)}
                            </div>` : ''}
                        </div>
                        <div class="result-meta">
                            <span class="result-category">
                                <i class="fas fa-tag"></i>
                                ${highlightedCategory}
                            </span>
                            <span class="result-priority ${priorityClass}">
                                <i class="fas fa-exclamation-triangle"></i>
                                ${highlightedPriority}
                            </span>
                            <span class="result-status">
                                <i class="fas fa-${task.completed ? 'check-circle' : 'clock'}"></i>
                                ${task.completed ? 'مكتملة' : 'نشطة'}
                            </span>
                        </div>
                    </div>
                    <div class="result-actions">
                        <button class="result-action-btn" onclick="editTask('${task.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="result-action-btn" onclick="toggleTaskCompletion('${task.id}')" title="${task.completed ? 'إلغاء الإكمال' : 'إكمال'}">
                            <i class="fas fa-${task.completed ? 'undo' : 'check'}"></i>
                        </button>
                        <button class="result-action-btn delete" onclick="deleteTask('${task.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        resultsList.innerHTML = resultsHTML;
        
        // إضافة تأثيرات حركية للنتائج
        const resultItems = resultsList.querySelectorAll('.search-result-item');
        resultItems.forEach((item, index) => {
            item.style.animationDelay = `${index * 50}ms`;
            item.classList.add('result-animate-in');
        });
    }

    function calculateMatchScore(task, query) {
        let score = 0;
        const queryLower = query.toLowerCase();
        const terms = queryLower.split(' ').filter(term => term.length > 0);
        
        terms.forEach(term => {
            // تطابق في النص الرئيسي
            if (task.text && task.text.toLowerCase().includes(term)) {
                score += 40;
            }
            
            // تطابق في التصنيف
            if (task.category && task.category.toLowerCase().includes(term)) {
                score += 25;
            }
            
            // تطابق في الأولوية
            if (task.priority && task.priority.toLowerCase().includes(term)) {
                score += 20;
            }
            
            // تطابق في التاريخ
            if (task.date) {
                const dateStr = new Date(task.date).toLocaleDateString('ar-SA');
                if (dateStr.includes(term)) {
                    score += 15;
                }
            }
        });
        
        // مكافأة للمهام النشطة
        if (!task.completed) score += 10;
        
        // مكافأة للمهام العاجلة
        if (task.priority === 'urgent') score += 15;
        
        return Math.min(score, 100);
    }

    function highlightSearchTerms(text, query) {
        if (!query.trim()) return text;
        
        const terms = query.toLowerCase().split(' ').filter(term => term.length > 0);
        let highlightedText = text;
        
        terms.forEach(term => {
            const regex = new RegExp(`(${term})`, 'gi');
            highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
        });
        
        return highlightedText;
    }

    function displaySearchResultsInMainInterface(filteredTasks, query, highlightResults) {
        const tasksContainer = document.getElementById('taskList');
        if (!tasksContainer) return;

        // إضافة مؤشر البحث
        const searchIndicator = document.getElementById('search-indicator') || createSearchIndicator();
        
        if (filteredTasks.length === 0) {
            tasksContainer.innerHTML = `
                <div class="no-tasks-found">
                    <i class="fas fa-search-minus"></i>
                    <h3>لا توجد مهام تطابق البحث</h3>
                    <p>البحث: "${query}"</p>
                    <button class="btn-clear-search" onclick="clearSearch()">
                        <i class="fas fa-times"></i>
                        مسح البحث
                    </button>
                </div>
            `;
            searchIndicator.style.display = 'block';
            return;
        }

        // مسح المحتوى الحالي
        tasksContainer.innerHTML = '';

        // عرض المهام المفلترة مع تمييز النص المطابق
        filteredTasks.forEach(task => {
            const taskElement = createTaskElement(task);
            
            // تمييز النص المطابق إذا كان مطلوباً
            if (highlightResults && query) {
                const textElement = taskElement.querySelector('.task-text');
                if (textElement) {
                    textElement.innerHTML = highlightSearchTerms(task.text, query);
                }
            }
            
            tasksContainer.appendChild(taskElement);
        });

        searchIndicator.style.display = 'block';
        
        // تحديث عداد المهام
        updateTaskStats();
    }

    function createSearchIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'search-indicator';
        indicator.className = 'search-indicator';
        indicator.innerHTML = `
            <div class="search-indicator-content">
                <i class="fas fa-search"></i>
                <span>عرض نتائج البحث</span>
                <button class="btn-clear-search" onclick="clearSearch()">
                    <i class="fas fa-times"></i>
                    مسح البحث
                </button>
            </div>
        `;
        
        const header = document.querySelector('.header');
        if (header) {
            header.parentNode.insertBefore(indicator, header.nextSibling);
        }
        
        return indicator;
    }

    function clearSearch() {
        // إعادة عرض جميع المهام
        renderTasks();
        
        // إخفاء مؤشر البحث
        const searchIndicator = document.getElementById('search-indicator');
        if (searchIndicator) {
            searchIndicator.style.display = 'none';
        }
        
        // مسح صندوق البحث
        const searchInput = document.querySelector('.advanced-search-input');
        if (searchInput) {
            searchInput.value = '';
            searchInput.dispatchEvent(new Event('input'));
        }
    }

    function updateTaskStats() {
        const totalTasks = tasks.length;
        const completedTasks = tasks.filter(task => task.completed).length;
        const activeTasks = totalTasks - completedTasks;
        
        const totalElement = document.querySelector('.stat-total span');
        const completedElement = document.querySelector('.stat-completed span');
        const activeElement = document.querySelector('.stat-active span');
        
        if (totalElement) totalElement.textContent = totalTasks;
        if (completedElement) completedElement.textContent = completedTasks;
        if (activeElement) activeElement.textContent = activeTasks;
    }

    function startInlineEdit(taskTextElement, task) {
        // حفظ النص الأصلي
        const originalText = taskTextElement.textContent;
        
        // جعل النص قابل للتعديل
        taskTextElement.contentEditable = true;
        taskTextElement.classList.add('editing');
        taskTextElement.focus();
        
        // تحديد النص بالكامل
        const range = document.createRange();
        range.selectNodeContents(taskTextElement);
        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(range);
        
        // إنشاء أزرار الحفظ والإلغاء
        const editActions = document.createElement('div');
        editActions.className = 'edit-actions';
        editActions.innerHTML = `
            <button class="save-edit-btn" title="حفظ التعديل">
                <i class="fas fa-check"></i>
            </button>
            <button class="cancel-edit-btn" title="إلغاء التعديل">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        // إضافة الأزرار بعد النص
        taskTextElement.parentNode.appendChild(editActions);
        
        // دالة الحفظ
        const saveEdit = () => {
            const newText = taskTextElement.textContent.trim();
            
            if (newText && newText !== originalText) {
                // تحديث المهمة
                task.text = newText;
                task.updatedAt = new Date().toISOString();
                
                // حفظ التغييرات
                saveTasks();
                
                // إشعار النجاح
                showNotification('تم تحديث المهمة بنجاح', 'success');
            }
            
            // إعادة تعيين النص
            taskTextElement.textContent = task.text;
            taskTextElement.contentEditable = false;
            taskTextElement.classList.remove('editing');
            taskTextElement.style.cursor = 'pointer';
            
            // إزالة أزرار التعديل
            if (editActions.parentNode) {
                editActions.parentNode.removeChild(editActions);
            }
            
            // إزالة مستمعي الأحداث
            document.removeEventListener('keydown', handleKeyDown);
            document.removeEventListener('click', handleClickOutside);
        };
        
        // دالة الإلغاء
        const cancelEdit = () => {
            taskTextElement.textContent = originalText;
            taskTextElement.contentEditable = false;
            taskTextElement.classList.remove('editing');
            taskTextElement.style.cursor = 'pointer';
            
            // إزالة أزرار التعديل
            if (editActions.parentNode) {
                editActions.parentNode.removeChild(editActions);
            }
            
            // إزالة مستمعي الأحداث
            document.removeEventListener('keydown', handleKeyDown);
            document.removeEventListener('click', handleClickOutside);
        };
        
        // معالجة المفاتيح
        const handleKeyDown = (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                saveEdit();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit();
            }
        };
        
        // معالجة النقر خارج النص
        const handleClickOutside = (e) => {
            if (!taskTextElement.contains(e.target) && !editActions.contains(e.target)) {
                saveEdit();
            }
        };
        
        // ربط الأحداث
        editActions.querySelector('.save-edit-btn').addEventListener('click', saveEdit);
        editActions.querySelector('.cancel-edit-btn').addEventListener('click', cancelEdit);
        document.addEventListener('keydown', handleKeyDown);
        document.addEventListener('click', handleClickOutside);
    }
    // أزرار الفلاتر
    document.querySelectorAll('.nav-btn[data-filter]').forEach(btn => {
        btn.onclick = () => {
            document.querySelectorAll('.nav-btn[data-filter]').forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            currentFilter = btn.dataset.filter;
            localStorage.setItem('currentFilter', currentFilter);
            renderTasks();
        };
    });
    // أزرار الأقسام
    document.querySelectorAll('.cat-btn[data-category]').forEach(btn => {
        btn.onclick = () => {
            document.querySelectorAll('.cat-btn[data-category]').forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            currentCategoryFilter = btn.dataset.category;
            renderTasks();
        };
    });
    // أزرار تبديل العرض (قائمة/شبكة)
    document.querySelectorAll('.view-toggle-btn').forEach(btn => {
        btn.onclick = () => {
            const view = btn.dataset.view;
            if (view) {
                currentView = view;
                localStorage.setItem('viewMode', view);
                document.querySelectorAll('.view-toggle-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                renderTasks();
            }
        };
    });
    // زر الإعدادات - تم نقله إلى optimized.js
    // const settingsBtn = document.getElementById('settingsMenu');
    // if (settingsBtn) settingsBtn.onclick = () => showNotification('إعدادات قادمة قريبًا!', 'info');
    // زر تسجيل الخروج
    const logoutBtn = document.getElementById('logoutMenu');
    if (logoutBtn) logoutBtn.onclick = logout;
    // زر سلة المهملات (placeholder)
    const trashBtn = document.getElementById('trashMenu');
    if (trashBtn) trashBtn.onclick = () => showNotification('سلة المهملات قادمة قريبًا!', 'info');
    // زر حذف الكل
    const clearAllBtn = document.getElementById('clearAllBtn');
    if (clearAllBtn) clearAllBtn.onclick = () => showBulkDeleteConfirmation('all');
    // زر حذف المكتملة
    const clearCompletedBtn = document.getElementById('clearCompletedBtn');
    if (clearCompletedBtn) clearCompletedBtn.onclick = () => showBulkDeleteConfirmation('completed');
}

// البحث عن المهام
function searchTasks(query = null) {
    if (query !== null) {
        searchQuery = query;
    } else {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchQuery = searchInput.value.trim();
        }
    }
    renderTasks();
}

// فتح صندوق البحث
function toggleSearchBox() {
    const searchBox = document.getElementById('searchBox');
    if (searchBox) {
        searchBox.style.display = searchBox.style.display === 'none' ? 'block' : 'none';
        if (searchBox.style.display === 'block') {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.focus();
            }
        }
    }
}

// إغلاق صندوق البحث
function closeSearchBox() {
    const searchBox = document.getElementById('searchBox');
    if (searchBox) {
        searchBox.style.display = 'none';
        searchQuery = '';
        renderTasks();
    }
}

// تحميل وعرض المهام
function loadAndRenderTasks() {
    try {
        showLoadingState();
            const savedTasks = localStorage.getItem('tasks');
            if (savedTasks) {
                tasks = JSON.parse(savedTasks);
            }
            renderTasks();
    } catch (error) {
        console.error('حدث خطأ أثناء تحميل المهام:', error);
        showNotification('حدث خطأ أثناء تحميل المهام', 'error');
        tasks = [];
        saveTasks();
    }
    updateStatsBar();
}

// تطبيق الإعدادات المحفوظة
function applySavedSettings() {
    // تحميل المهام المحفوظة
    const savedTasks = localStorage.getItem('tasks');
    if (savedTasks) {
        try {
            tasks = JSON.parse(savedTasks);
        } catch (error) {
            console.error('خطأ في تحميل المهام المحفوظة:', error);
            tasks = [];
        }
    }
    
    // تطبيق الفلتر المحفوظ
    const savedFilter = localStorage.getItem('currentFilter');
    if (savedFilter) {
        currentFilter = savedFilter;
        updateActiveFilter();
    }
    
    // تطبيق طريقة العرض المحفوظة
    const savedView = localStorage.getItem('viewMode');
    if (savedView) {
        toggleView(savedView, false);
    }
    
    // تفعيل الوضع المظلم دائمًا
    document.body.classList.add('dark-mode');
    document.documentElement.setAttribute('data-theme', 'dark');
    if (elements.themeToggle) {
        elements.themeToggle.checked = true;
    }
    // التأكد من حفظ الإعداد
    localStorage.setItem('darkMode', 'true');
}

// عرض حالة التحميل
function showLoadingState() {
    if (elements.taskList) {
        elements.taskList.innerHTML = `
            <div class="loading-state">
                <div class="loading-spinner"></div>
                <p>جاري تحميل المهام...</p>
            </div>`;
    }
}

// تحديث زر الفلتر النشط
function updateActiveFilter() {
    if (elements.filterButtons) {
        elements.filterButtons.forEach(btn => {
            if (btn.dataset.filter === currentFilter) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });
    }
}

// عرض المهام
function renderTasks() {
    const taskList = document.getElementById('taskList');
    const emptyState = document.getElementById('emptyState');
    if (!taskList) return;
    
    // فلترة المهام حسب البحث أولاً
    let filteredTasks = [...tasks];
    
    if (searchQuery && searchQuery.trim()) {
        filteredTasks = filteredTasks.filter(task => 
            task.text && task.text.toLowerCase().includes(searchQuery.toLowerCase())
        );
    }
    
    // فلترة حسب الحالة (النشطة/المكتملة)
    const activeFilter = document.querySelector('.nav-btn.active')?.dataset.filter || 'all';
    if (activeFilter !== 'all') {
        filteredTasks = filteredTasks.filter(task => {
            if (activeFilter === 'active') return !task.completed;
            if (activeFilter === 'completed') return task.completed;
            return true;
        });
    }
    
    // فلترة حسب القسم
    const activeCategory = document.querySelector('.cat-btn.active')?.dataset.category || 'all';
    if (activeCategory !== 'all') {
        filteredTasks = filteredTasks.filter(task => 
            (task.category || 'عام') === activeCategory
        );
    }
    
    // مسح المحتوى الحالي
    taskList.innerHTML = '';
    
    // إظهار/إخفاء الحالة الفارغة
    if (emptyState) {
        if (filteredTasks.length === 0) {
            emptyState.style.display = 'flex';
            let message = 'لا توجد مهام';
            if (searchQuery) message += ` تطابق "${searchQuery}"`;
            else if (activeFilter === 'active') message = 'لا توجد مهام نشطة';
            else if (activeFilter === 'completed') message = 'لا توجد مهام مكتملة';
            else if (activeCategory !== 'all') message = `لا توجد مهام في قسم "${activeCategory}"`;
            
            emptyState.innerHTML = `
                <i class="fas fa-${searchQuery ? 'search' : 'tasks'}"></i>
                <p>${message}</p>
            `;
        } else {
            emptyState.style.display = 'none';
        }
    }
    
    if (filteredTasks.length === 0) return;
    
    // عرض المهام
    filteredTasks.forEach(task => {
        const taskElement = createTaskElement(task);
        if (taskElement) {
            taskList.appendChild(taskElement);
        }
    });
    
    // تحديث الإحصائيات
    updateStatsWithAnimation();
}

// تصفية المهام
function filterTasks(filter) {
    const taskElements = document.querySelectorAll('.task-card');
    const navBtns = document.querySelectorAll('.nav-btn');
    
    // تحديث الأزرار النشطة
    navBtns.forEach(btn => btn.classList.remove('active'));
    const activeBtn = document.querySelector(`[data-filter="${filter}"]`);
    if (activeBtn) activeBtn.classList.add('active');
    
    taskElements.forEach((element, index) => {
        const isCompleted = element.classList.contains('completed');
        let shouldShow = false;
        
        switch(filter) {
            case 'all':
                shouldShow = true;
                break;
            case 'active':
                shouldShow = !isCompleted;
                break;
            case 'completed':
                shouldShow = isCompleted;
                break;
        }
        
        if (shouldShow) {
            element.style.display = 'block';
            element.style.animationDelay = `${index * 50}ms`;
            element.style.animation = 'slideIn 0.3s ease';
        } else {
            element.style.display = 'none';
        }
    });
}

// تحديث قائمة المهام
function updateTaskList(filteredTasks) {
    if (!elements.taskList) return;
    
    elements.taskList.innerHTML = '';
    
    if (filteredTasks.length === 0) {
        return;
    }
    
    filteredTasks.forEach(task => {
        const taskItem = createTaskElement(task);
        elements.taskList.appendChild(taskItem);
    });
}

// تحديث شبكة المهام
function updateTaskGrid(filteredTasks) {
    if (!elements.taskGrid) return;
    
    elements.taskGrid.innerHTML = '';
    
    if (filteredTasks.length === 0) {
        return;
    }
    
    filteredTasks.forEach(task => {
        const taskItem = createTaskElement(task, true);
        elements.taskGrid.appendChild(taskItem);
    });
}

// إنشاء عنصر مهمة مبسط مع دعم السحب والإفلات
function createTaskElement(task, isGrid = false) {
    const taskItem = document.createElement('div');
    taskItem.className = `task-card ${task.completed ? 'completed' : ''}`;
    taskItem.dataset.taskId = task.id;
    taskItem.setAttribute('data-category', task.category || 'عام');
    taskItem.setAttribute('data-priority', task.priority || 'medium');
    taskItem.draggable = true;
    
    // إنشاء محتوى المهمة
    const taskContent = document.createElement('div');
    taskContent.className = 'task-content';
    
    // نص المهمة مع إمكانية التعديل
    const taskText = document.createElement('div');
    taskText.className = 'task-text';
    taskText.textContent = task.text;
    taskText.setAttribute('data-task-id', task.id);
    
    // إضافة إمكانية التعديل عند الضغط على النص
    taskText.addEventListener('click', (e) => {
        // منع التعديل إذا كان العنصر في حالة السحب
        if (taskElement.classList.contains('dragging')) return;
        
        // منع التعديل إذا كان النص قابل للتعديل بالفعل
        if (taskText.contentEditable === 'true') return;
        
        e.stopPropagation();
        startInlineEdit(taskText, task);
    });
    
    // إضافة مؤشر للتعديل
    taskText.style.cursor = 'pointer';
    taskText.title = 'اضغط للتعديل';
    
    // معلومات المهمة
    const taskMeta = document.createElement('div');
    taskMeta.className = 'task-meta';
    
    // التصنيف
    const taskCategory = document.createElement('span');
    taskCategory.className = 'task-category';
    taskCategory.textContent = task.category || 'عام';
    
    // الأولوية
    const taskPriority = document.createElement('span');
    taskPriority.className = `task-priority ${task.priority || 'medium'}`;
    taskPriority.textContent = getPriorityText(task.priority || 'medium');
    
    // التاريخ
    const taskDate = document.createElement('span');
    taskDate.className = 'task-date';
    taskDate.innerHTML = `<i class="fas fa-calendar-alt"></i> ${formatDate(task.createdAt)}`;
    
    // إضافة التصنيف والأولوية والتاريخ للمعلومات
    taskMeta.appendChild(taskCategory);
    taskMeta.appendChild(taskPriority);
    taskMeta.appendChild(taskDate);
    
    // إضافة النص والمعلومات للمحتوى
    taskContent.appendChild(taskText);
    taskContent.appendChild(taskMeta);
    
    // أزرار التحكم
    const taskActions = document.createElement('div');
    taskActions.className = 'task-actions';
    
    // زر إكمال المهمة
    const completeBtn = document.createElement('button');
    completeBtn.className = 'complete-task-btn';
    completeBtn.innerHTML = task.completed ? '<i class="fas fa-check-circle"></i>' : '<i class="far fa-circle"></i>';
    completeBtn.title = task.completed ? 'إلغاء الإكمال' : 'إكمال المهمة';
    completeBtn.addEventListener('click', () => toggleTaskCompletion(task.id));
    
    // زر التعديل
    const editBtn = document.createElement('button');
    editBtn.className = 'edit-task-btn';
    editBtn.innerHTML = '<i class="fas fa-edit"></i>';
    editBtn.title = 'تعديل المهمة';
    editBtn.addEventListener('click', () => editTask(task.id));
    
    // زر الحذف
    const deleteBtn = document.createElement('button');
    deleteBtn.className = 'delete-task-btn';
    deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
    deleteBtn.title = 'حذف المهمة';
    deleteBtn.addEventListener('click', () => deleteTask(task.id));
    
    // إضافة الأزرار
    taskActions.appendChild(completeBtn);
    taskActions.appendChild(editBtn);
    taskActions.appendChild(deleteBtn);
    
    // مؤشر السحب
    const dragHandle = document.createElement('div');
    dragHandle.className = 'drag-handle';
    dragHandle.innerHTML = '<i class="fas fa-grip-vertical"></i>';
    dragHandle.title = 'اسحب لإعادة الترتيب';
    
    // إضافة المحتوى والأزرار للعنصر الرئيسي
    taskItem.appendChild(taskContent);
    taskItem.appendChild(taskActions);
    taskItem.appendChild(dragHandle);
    
    // إضافة أحداث السحب والإفلات
    setupDragAndDrop(taskItem, task);
    
    return taskItem;
}

// دالة مساعدة للحصول على نص الأولوية
function getPriorityText(priority) {
    const priorityTexts = {
        'urgent': 'عاجل',
        'high': 'عالية',
        'medium': 'متوسطة',
        'low': 'منخفضة'
    };
    return priorityTexts[priority] || 'متوسطة';
}



// إعداد السحب والإفلات المحسن
function setupDragAndDrop(taskElement, task) {
    let draggedElement = null;
    let originalIndex = -1;
    let isDragging = false;
    
    // بداية السحب
    taskElement.addEventListener('dragstart', (e) => {
        draggedElement = taskElement;
        originalIndex = Array.from(taskElement.parentElement.children).indexOf(taskElement);
        isDragging = true;
        
        taskElement.classList.add('dragging');
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/html', taskElement.outerHTML);
        
        // تأثير بصري محسن للسحب
        taskElement.style.transform = 'rotate(2deg) scale(1.02)';
        taskElement.style.zIndex = '1000';
        taskElement.style.boxShadow = '0 15px 40px rgba(0, 0, 0, 0.3)';
        taskElement.style.opacity = '0.9';
        
        // إضافة تأثير للعناصر الأخرى
        const allTasks = document.querySelectorAll('.task-card:not(.dragging)');
        allTasks.forEach(task => {
            task.style.transition = 'all 0.2s ease';
        });
    });
    
    // نهاية السحب
    taskElement.addEventListener('dragend', (e) => {
        isDragging = false;
        taskElement.classList.remove('dragging');
        
        // إعادة تعيين الأنماط
        taskElement.style.transform = '';
        taskElement.style.zIndex = '';
        taskElement.style.boxShadow = '';
        taskElement.style.opacity = '';
        
        // إعادة تعيين العناصر الأخرى
        const allTasks = document.querySelectorAll('.task-card');
        allTasks.forEach(task => {
            task.style.transition = '';
            task.style.transform = '';
            task.style.opacity = '';
        });
        
        // إزالة جميع مناطق الإفلات
        document.querySelectorAll('.drop-zone').forEach(zone => {
            zone.classList.remove('drop-zone');
        });
        
        draggedElement = null;
    });
    
    // السماح بالإفلات مع تأثيرات محسنة
    taskElement.addEventListener('dragover', (e) => {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
        
        if (!isDragging || draggedElement === taskElement) return;
        
        taskElement.classList.add('drop-zone');
        
        // تأثير بصري للعناصر المجاورة
        const rect = taskElement.getBoundingClientRect();
        const centerY = rect.top + rect.height / 2;
        
        if (e.clientY < centerY) {
            taskElement.style.transform = 'translateY(-8px) scale(1.02)';
            taskElement.style.opacity = '0.8';
        } else {
            taskElement.style.transform = 'translateY(8px) scale(1.02)';
            taskElement.style.opacity = '0.8';
        }
    });
    
    // مغادرة منطقة الإفلات
    taskElement.addEventListener('dragleave', (e) => {
        if (!taskElement.contains(e.relatedTarget)) {
            taskElement.classList.remove('drop-zone');
            taskElement.style.transform = '';
            taskElement.style.opacity = '';
        }
    });
    
    // الإفلات
    taskElement.addEventListener('drop', (e) => {
        e.preventDefault();
        taskElement.classList.remove('drop-zone');
        taskElement.style.transform = '';
        taskElement.style.opacity = '';
        
        if (draggedElement && draggedElement !== taskElement) {
            const newIndex = Array.from(taskElement.parentElement.children).indexOf(taskElement);
            
            // إعادة ترتيب المهام في المصفوفة
            reorderTasks(originalIndex, newIndex);
            
            // إعادة ترتيب العناصر في DOM
            if (originalIndex < newIndex) {
                taskElement.parentElement.insertBefore(draggedElement, taskElement.nextSibling);
            } else {
                taskElement.parentElement.insertBefore(draggedElement, taskElement);
            }
            
            // حفظ الترتيب الجديد
            saveTasks();
            showNotification('تم إعادة ترتيب المهام بنجاح', 'success');
        }
    });
}

// إعادة ترتيب المهام في المصفوفة
function reorderTasks(fromIndex, toIndex) {
    const taskList = elements.taskList || elements.taskGrid;
    if (!taskList) return;
    
    const taskElements = Array.from(taskList.children);
    const taskIds = taskElements.map(el => el.dataset.taskId);
    
    // إعادة ترتيب المهام في المصفوفة
    const movedTask = tasks.splice(fromIndex, 1)[0];
    tasks.splice(toIndex, 0, movedTask);
}



// تحديث دالة إضافة المهمة لدعم الأولوية مع ديناميكية محسنة
async function addTask() {
    const taskInput = document.getElementById('taskInput');
    const categorySelect = document.getElementById('taskCategory');
    const prioritySelect = document.getElementById('taskPriority');
    
    const text = taskInput.value.trim();
    const category = categorySelect ? categorySelect.value : 'عام';
    const priority = prioritySelect ? prioritySelect.value : 'medium';
    
    if (!isValidText(text)) {
        showNotification('الرجاء إدخال نص المهمة', 'error');
        taskInput.focus();
        return;
    }
    
    // إنشاء المهمة الجديدة
    const task = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        text: text,
        category: category,
        priority: priority,
        completed: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
    
    // إضافة المهمة في بداية المصفوفة
    tasks.unshift(task);
    
    // حفظ المهام فوراً
    saveTasks();
    
    // إنشاء عنصر المهمة
    const taskElement = createTaskElement(task);
    const taskList = elements.taskList || elements.taskGrid;
    
    if (taskList) {
        // إخفاء الحالة الفارغة إذا كانت موجودة
        const emptyState = taskList.querySelector('.empty-state');
        if (emptyState) {
            emptyState.style.display = 'none';
        }
        
        // إضافة المهمة في البداية مع تأثير حركي
        taskList.insertBefore(taskElement, taskList.firstChild);
        
        // تأثير إضافة المهمة
        taskElement.style.opacity = '0';
        taskElement.style.transform = 'translateY(-30px) scale(0.9)';
        
        // تطبيق التأثير الحركي
        requestAnimationFrame(() => {
            taskElement.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
            taskElement.style.opacity = '1';
            taskElement.style.transform = 'translateY(0) scale(1)';
        });
        
        // تأثير نبض للعنصر الجديد
        setTimeout(() => {
            taskElement.style.boxShadow = '0 8px 25px rgba(25, 118, 210, 0.3)';
            setTimeout(() => {
                taskElement.style.boxShadow = '';
            }, 1000);
        }, 400);
    }
    
    // مسح الحقول
    taskInput.value = '';
    if (categorySelect) categorySelect.value = 'عام';
    if (prioritySelect) prioritySelect.value = 'medium';
    
    // تحديث الإحصائيات فوراً
    updateStatsWithAnimation();
    
    // إشعار النجاح
    showNotification('تم إضافة المهمة بنجاح', 'success');
    
    // التركيز على حقل الإدخال
    taskInput.focus();
    
    // تحديث الفلاتر إذا كانت مفعلة
    if (currentFilter && currentFilter !== 'all') {
        filterTasks(currentFilter);
    }
}

// دالة مساعدة لإغلاق جميع النماذج المفتوحة
function closeAllTaskForms() {
    document.querySelectorAll('.task-edit-form, .delete-confirmation').forEach(el => {
        el.classList.remove('active');
    });
}

// تعديل مهمة (الآن يتم التعامل معها مباشرة في createTaskElement)
function editTask(taskId) {
    const task = tasks.find(t => t.id === taskId);
    if (!task) return;
    
    const taskElement = document.querySelector(`[data-task-id="${taskId}"]`);
    if (!taskElement) return;
    
    const taskText = taskElement.querySelector('.task-text');
    if (!taskText) return;
    
    // حفظ النص الأصلي
    const originalText = taskText.textContent;
    
    // جعل النص قابل للتعديل
    taskText.contentEditable = true;
    taskText.focus();
    
    // تحديد النص بالكامل
    const range = document.createRange();
    range.selectNodeContents(taskText);
    const selection = window.getSelection();
    selection.removeAllRanges();
    selection.addRange(range);
    
    // إضافة كلاس للتعديل
    taskText.classList.add('editing');
    
    // إنشاء أزرار الحفظ والإلغاء
    const editActions = document.createElement('div');
    editActions.className = 'edit-actions';
    editActions.innerHTML = `
        <button class="save-edit-btn" title="حفظ التعديل">
            <i class="fas fa-check"></i>
        </button>
        <button class="cancel-edit-btn" title="إلغاء التعديل">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // إضافة الأزرار بعد النص
    taskText.parentNode.insertBefore(editActions, taskText.nextSibling);
    
    // دالة حفظ التعديل
    const saveEdit = () => {
        const newText = taskText.textContent.trim();
        if (newText && newText !== originalText) {
            task.text = newText;
            task.updatedAt = new Date().toISOString();
            saveTasks();
            showNotification('تم تحديث المهمة بنجاح', 'success');
        }
        
        // إزالة وضع التعديل
        taskText.contentEditable = false;
        taskText.classList.remove('editing');
        editActions.remove();
    };
    
    // دالة إلغاء التعديل
    const cancelEdit = () => {
        taskText.textContent = originalText;
        taskText.contentEditable = false;
        taskText.classList.remove('editing');
        editActions.remove();
    };
    
    // ربط الأحداث
    const saveBtn = editActions.querySelector('.save-edit-btn');
    const cancelBtn = editActions.querySelector('.cancel-edit-btn');
    
    saveBtn.addEventListener('click', saveEdit);
    cancelBtn.addEventListener('click', cancelEdit);
    
    // حفظ بالضغط على Enter
    taskText.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            saveEdit();
        } else if (e.key === 'Escape') {
            cancelEdit();
        }
    });
    
    // إغلاق عند النقر خارج النص
    const handleClickOutside = (e) => {
        if (!taskText.contains(e.target) && !editActions.contains(e.target)) {
            saveEdit();
            document.removeEventListener('click', handleClickOutside);
        }
    };
    
    // تأخير إضافة مستمع النقر الخارجي لتجنب الإغلاق الفوري
    setTimeout(() => {
        document.addEventListener('click', handleClickOutside);
    }, 100);
}

// حذف مهمة
function deleteTask(taskId) {
    const task = tasks.find(t => t.id === taskId);
    if (!task) return;
    
    // استخدام نافذة التأكيد المخصصة
    showCustomDeleteConfirmation(task.text, () => {
        const index = tasks.findIndex(t => t.id === taskId);
        if (index !== -1) {
            // العثور على عنصر المهمة
            const taskElement = document.querySelector(`[data-task-id="${taskId}"]`);
            
            if (taskElement) {
                // تأثير حذف المهمة
                taskElement.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                taskElement.style.opacity = '0';
                taskElement.style.transform = 'translateX(-100px) scale(0.8)';
                
                // حذف العنصر بعد انتهاء التأثير
                setTimeout(() => {
                    if (taskElement.parentElement) {
                        taskElement.parentElement.removeChild(taskElement);
                    }
                    
                    // حذف من المصفوفة
                    tasks.splice(index, 1);
                    saveTasks();
                    
                    // تحديث الإحصائيات
                    updateStatsWithAnimation();
                    
                    // إظهار الحالة الفارغة إذا لم تتبق مهام
                    const taskList = elements.taskList || elements.taskGrid;
                    if (taskList && tasks.length === 0) {
                        const emptyState = taskList.querySelector('.empty-state');
                        if (emptyState) {
                            emptyState.style.display = 'flex';
                            emptyState.style.animation = 'fadeIn 0.5s ease';
                        }
                    }
                    
                    // إشعار النجاح
                    showNotification('تم حذف المهمة بنجاح', 'success');
                }, 300);
            } else {
                // إذا لم يتم العثور على العنصر، احذف من المصفوفة فقط
                tasks.splice(index, 1);
                saveTasks();
                updateStatsWithAnimation();
                showNotification('تم حذف المهمة بنجاح', 'success');
            }
        }
    });
}

// عرض زر التراجع عن الحذف
function showUndoDelete(task, index) {
    const notification = document.createElement('div');
    notification.className = 'notification info';
    notification.innerHTML = `
        <i class="fas fa-info-circle"></i>
        <span>تم حذف المهمة: ${task.text}</span>
        <button class="undo-delete" data-task='${JSON.stringify(task)}' data-index='${index}'>
            تراجع
        </button>
        <button class="close-notification" aria-label="إغلاق الإشعار">&times;</button>
    `;
    
    document.body.appendChild(notification);
    
    // إظهار الإشعار
    setTimeout(() => notification.classList.add('show'), 10);
    
    // إغلاق الإشعار تلقائياً بعد 5 ثواني
    const timer = setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 5000);
    
    // معالجة حدث التراجع
    const undoBtn = notification.querySelector('.undo-delete');
    undoBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        const taskData = JSON.parse(undoBtn.dataset.task);
        tasks.splice(parseInt(undoBtn.dataset.index), 0, taskData);
        saveTasks();
        renderTasks();
        notification.remove();
        showNotification('تم استعادة المهمة بنجاح', 'success');
    });
    
    // إغلاق الإشعار يدوياً
    const closeBtn = notification.querySelector('.close-notification');
    closeBtn.addEventListener('click', () => {
        clearTimeout(timer);
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    });
    
    // إبقاء الإشعار مفتوحاً عند التحويم عليه
    notification.addEventListener('mouseenter', () => {
        clearTimeout(timer);
    });
    
    notification.addEventListener('mouseleave', () => {
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 1000);
    });
}

// حذف جميع المهام
function clearAllTasks() {
    try {
        if (!Array.isArray(tasks)) tasks = [];
        if (tasks.length === 0) {
            showNotification('لا توجد مهام لحذفها', 'info');
            return;
        }
        const backupTasks = [...tasks];
        tasks = [];
        saveTasks();
        renderTasks();
        showNotification('تم حذف جميع المهام بنجاح', 'success');
        showUndoDeleteAll(backupTasks, 'clear-all');
    } catch (err) {
        showNotification('حدث خطأ أثناء حذف جميع المهام', 'error');
    }
}

// حذف المهام المكتملة
function clearCompletedTasks() {
    try {
        if (!Array.isArray(tasks)) tasks = [];
        const completedTasks = tasks.filter(task => task.completed);
        if (completedTasks.length === 0) {
            showNotification('لا توجد مهام مكتملة للحذف', 'info');
            return;
        }
        const backupTasks = [...tasks];
        tasks = tasks.filter(task => !task.completed);
        saveTasks();
        renderTasks();
        showNotification('تم حذف المهام المكتملة بنجاح', 'success');
        showUndoDeleteAll(backupTasks, 'clear-completed');
    } catch (err) {
        showNotification('حدث خطأ أثناء حذف المهام المكتملة', 'error');
    }
}

// عرض زر التراجع عن حذف الكل
function showUndoDeleteAll(backupTasks, action) {
    const notification = document.createElement('div');
    notification.className = 'notification undo-notification';
    notification.innerHTML = `
        <span>تم حذف جميع المهام</span>
        <button class="undo-btn">تراجع</button>
    `;
    
    document.body.appendChild(notification);
    
    // إظهار الإشعار بتحريك سلس
    setTimeout(() => {
        notification.style.transform = 'translateY(0)';
        notification.style.opacity = '1';
    }, 100);
    
    // إخفاء الإشعار بعد 5 ثوانٍ
    const timeoutId = setTimeout(() => {
        notification.style.transform = 'translateY(100%)';
        notification.style.opacity = '0';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
    
    // معالجة حدث الزر التراجع
    const undoBtn = notification.querySelector('.undo-btn');
    undoBtn.addEventListener('click', () => {
        clearTimeout(timeoutId);
        tasks = backupTasks;
        saveTasks();
        renderTasks();
        notification.style.transform = 'translateY(100%)';
        notification.style.opacity = '0';
        setTimeout(() => {
            notification.remove();
        }, 300);
        showNotification('تم استعادة جميع المهام بنجاح', 'success');
    });
    
    // إغلاق الإشعار عند النقر على الزر X
    const closeBtn = notification.querySelector('.close-btn');
    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            clearTimeout(timeoutId);
            notification.style.transform = 'translateY(100%)';
            notification.style.opacity = '0';
            setTimeout(() => {
                notification.remove();
            }, 300);
        });
    }
}

// تبديل طريقة العرض
function toggleView(viewType, saveToStorage = true) {
    if (!viewType || (viewType !== 'list' && viewType !== 'grid')) return;
    
    currentView = viewType;
    
    if (saveToStorage) {
        localStorage.setItem('viewMode', viewType);
    }
    
    // تحديث واجهة المستخدم
    if (elements.listView && elements.gridView) {
        if (viewType === 'list') {
            elements.listView.style.display = 'block';
            elements.gridView.style.display = 'none';
        } else {
            elements.listView.style.display = 'none';
            elements.gridView.style.display = 'grid';
        }
    }
    
    // تحديث أزرار التبديل
    if (elements.viewToggleButtons) {
        elements.viewToggleButtons.forEach(button => {
            if (button.dataset.view === viewType) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }
        });
    }
    
    // إعادة عرض المهام مع طريقة العرض الجديدة
    renderTasks();
}

// تبديل الوضع الليلي
function toggleDarkMode() {
    const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
    const newTheme = isDark ? 'light' : 'dark';
    
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('darkMode', newTheme === 'dark');
    
    // تحديث أزرار تبديل الوضع
    updateThemeToggleButton();
    
    // إشعار
    const message = newTheme === 'dark' ? 'تم تفعيل الوضع الليلي 🌙' : 'تم تفعيل الوضع النهاري ☀️';
    showNotification(message, 'info');
}

// دالة إنشاء نافذة تأكيد الحذف المخصصة
function showCustomDeleteConfirmation(taskText, onConfirm) {
    // إنشاء نافذة التأكيد
    const confirmationModal = document.createElement('div');
    confirmationModal.className = 'custom-confirmation-modal';
    confirmationModal.innerHTML = `
        <div class="custom-confirmation-content">
            <div class="custom-confirmation-header">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>تأكيد الحذف</h3>
            </div>
            <div class="custom-confirmation-body">
                <p>هل أنت متأكد من حذف المهمة التالية؟</p>
                <div class="task-preview">"${taskText}"</div>
                <p class="warning-text">⚠️ هذا الإجراء لا يمكن التراجع عنه!</p>
            </div>
            <div class="custom-confirmation-actions">
                <button class="btn-cancel" id="cancelDeleteBtn">
                    <i class="fas fa-times"></i> إلغاء
                </button>
                <button class="btn-confirm" id="confirmDeleteBtn">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        </div>
    `;
    
    // إضافة النافذة للصفحة
    document.body.appendChild(confirmationModal);
    
    // إظهار النافذة بتأثير
    setTimeout(() => {
        confirmationModal.classList.add('show');
    }, 10);
    
    // معالجة الأحداث
    const cancelBtn = confirmationModal.querySelector('#cancelDeleteBtn');
    const confirmBtn = confirmationModal.querySelector('#confirmDeleteBtn');
    
    const closeModal = () => {
        confirmationModal.classList.remove('show');
        setTimeout(() => {
            confirmationModal.remove();
        }, 300);
    };
    
    cancelBtn.addEventListener('click', closeModal);
    confirmBtn.addEventListener('click', () => {
        closeModal();
        onConfirm();
    });
    
    // إغلاق عند النقر خارج النافذة
    confirmationModal.addEventListener('click', (e) => {
        if (e.target === confirmationModal) {
            closeModal();
        }
    });
    
    // إغلاق بمفتاح Escape
    const handleEscape = (e) => {
        if (e.key === 'Escape') {
            closeModal();
            document.removeEventListener('keydown', handleEscape);
        }
    };
    document.addEventListener('keydown', handleEscape);
}

// إعداد أزرار المسح
function setupClearButtons() {
    const clearAllBtn = document.getElementById('clearAllBtn');
    if (clearAllBtn) {
        clearAllBtn.addEventListener('click', () => {
            showBulkDeleteConfirmation('all');
        });
    }
    const clearCompletedBtn = document.getElementById('clearCompletedBtn');
    if (clearCompletedBtn) {
        clearCompletedBtn.addEventListener('click', () => {
            showBulkDeleteConfirmation('completed');
        });
    }
}

// حفظ المهام في التخزين المحلي
function saveTasks() {
    try {
        localStorage.setItem('tasks', JSON.stringify(tasks));
        localStorage.setItem('lastUpdated', new Date().toISOString());
    } catch (error) {
        console.error('خطأ في حفظ المهام:', error);
        showNotification('حدث خطأ في حفظ المهام', 'error');
    }
}

// فلترة حسب القسم
function renderCategoryFilters() {
    if (!elements.categoryFilterContainer) return;
    const categories = ['all', ...Array.from(new Set(tasks.map(t => t.category || 'بدون تصنيف')))].filter(Boolean);
    elements.categoryFilterContainer.innerHTML = categories.map(cat => `
        <button class="category-filter-btn${currentCategoryFilter === cat ? ' active' : ''}" data-category="${cat}">
            ${cat === 'all' ? 'كل الأقسام' : cat}
        </button>
    `).join('');
}

function updateActiveCategoryFilter() {
    if (!elements.categoryFilterContainer) return;
    elements.categoryFilterContainer.querySelectorAll('.category-filter-btn').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.category === currentCategoryFilter);
    });
}

// إخفاء نافذة التأكيد
function hideDeleteConfirmation() {
    const dialog = document.getElementById('deleteConfirmation');
    if (dialog) dialog.style.display = 'none';
    pendingDeleteAction = null;
}

// ربط أزرار التأكيد
function setupDeleteConfirmationEvents() {
    const confirmBtn = document.getElementById('confirmDelete');
    const cancelBtn = document.getElementById('cancelDelete');
    if (confirmBtn) {
        confirmBtn.onclick = null;
        confirmBtn.onclick = () => {
            hideDeleteConfirmation();
            setTimeout(() => {
                if (pendingDeleteAction === 'all') {
                    clearAllTasks();
                } else if (pendingDeleteAction === 'completed') {
                    clearCompletedTasks();
                }
            }, 100);
        };
    }
    if (cancelBtn) {
        cancelBtn.onclick = null;
        cancelBtn.onclick = hideDeleteConfirmation;
    }
}

function updateStatsBar() {
    if (!elements.statsBar) return;
    const total = tasks.length;
    const completed = tasks.filter(t => t.completed).length;
    const active = total - completed;
    elements.statsBar.innerHTML = `
        <div class="stat-box stat-total">المهام الكلية <span>${total}</span></div>
        <div class="stat-box stat-completed">المكتملة <span>${completed}</span></div>
        <div class="stat-box stat-active">النشطة <span>${active}</span></div>
    `;
    const stats = elements.statsBar.querySelectorAll('.stat-box span');
    stats.forEach(span => {
        span.animate([
            { transform: 'scale(1.2)', color: '#1976d2' },
            { transform: 'scale(1)', color: '' }
        ], { duration: 350, fill: 'forwards' });
    });
}

function setupSideMenuEvents() {
    const menuToggle = document.getElementById('menuToggle');
    const sideMenu = document.getElementById('sideMenu');
    const closeBtn = document.getElementById('closeSideMenu');
    const overlay = document.getElementById('sidebarOverlay');

    function openMenu() {
        sideMenu.classList.add('active');
        document.body.classList.add('side-menu-open');
        overlay.style.display = 'block';
        // تأثير لمعة
        sideMenu.style.boxShadow = '0 0 40px 0 rgba(25,118,210,0.18), 0 8px 32px 0 rgba(0,0,0,0.18)';
        sideMenu.style.transition = 'box-shadow 0.4s cubic-bezier(0.4,0,0.2,1), transform 0.4s cubic-bezier(0.4,0,0.2,1)';
        sideMenu.style.transform = 'translateX(0) scale(1.03)';
        setTimeout(() => {
            sideMenu.style.transform = 'translateX(0) scale(1)';
        }, 250);
    }
    function closeMenu() {
        sideMenu.classList.remove('active');
        document.body.classList.remove('side-menu-open');
        overlay.style.display = 'none';
        sideMenu.style.boxShadow = '';
        sideMenu.style.transform = '';
    }
    if (menuToggle) menuToggle.addEventListener('click', openMenu);
    if (closeBtn) closeBtn.addEventListener('click', closeMenu);
    if (overlay) overlay.addEventListener('click', closeMenu);
    // إغلاق القائمة عند الضغط خارجها
    document.addEventListener('mousedown', (e) => {
        if (sideMenu.classList.contains('active') && !sideMenu.contains(e.target) && e.target !== menuToggle) {
            closeMenu();
        }
    });
    // تحسين التنقل بلوحة المفاتيح
    sideMenu.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') closeMenu();
    });
}

// تصدير المهام كملف نصي
function exportTasksAsTXT() {
    if (!tasks.length) {
        showNotification('لا توجد مهام لتصديرها', 'info');
        return;
    }
    let txt = 'قائمة المهام:\n';
    txt += '-----------------------------\n';
    tasks.forEach((task, idx) => {
        txt += `#${idx + 1}\n`;
        txt += `النص: ${task.text}\n`;
        txt += `القسم: ${task.category || 'بدون تصنيف'}\n`;
        txt += `الحالة: ${task.completed ? 'مكتملة' : 'غير مكتملة'}\n`;
        txt += `تاريخ الإضافة: ${task.createdAt ? new Date(task.createdAt).toLocaleString('ar-EG') : ''}\n`;
        txt += '-----------------------------\n';
    });
    const blob = new Blob([txt], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'قائمة_المهام.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    showNotification('تم تصدير المهام بنجاح كملف نصي', 'success');
}

// تصدير المهام كملف CSV
function exportTasksAsCSV() {
    if (!tasks.length) {
        showNotification('لا توجد مهام لتصديرها', 'info');
        return;
    }
    let csv = 'النص,القسم,الحالة,تاريخ الإضافة\n';
    tasks.forEach(task => {
        csv += `"${task.text.replace(/"/g, '""')}","${(task.category || 'بدون تصنيف').replace(/"/g, '""')}","${task.completed ? 'مكتملة' : 'غير مكتملة'}","${task.createdAt ? new Date(task.createdAt).toLocaleString('ar-EG') : ''}"\n`;
    });
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'قائمة_المهام.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    showNotification('تم تصدير المهام بنجاح كملف CSV', 'success');
}

// تصدير المهام كملف JSON (منسق)
function exportTasksAsJSON() {
    if (!tasks.length) {
        showNotification('لا توجد مهام لتصديرها', 'info');
        return;
    }
    const exportData = tasks.map(task => ({
        النص: task.text,
        القسم: task.category || 'بدون تصنيف',
        مكتملة: !!task.completed,
        "تاريخ الإضافة": task.createdAt ? new Date(task.createdAt).toLocaleString('ar-EG') : ''
    }));
    const dataStr = JSON.stringify(exportData, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'قائمة_المهام.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    showNotification('تم تصدير المهام بنجاح كملف JSON', 'success');
}

// استيراد المهام من ملف JSON
function importTasksFromJSON() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json,application/json';
    input.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (!file) return;
        const reader = new FileReader();
        reader.onload = (event) => {
            try {
                const imported = JSON.parse(event.target.result);
                if (Array.isArray(imported)) {
                    tasks = imported;
                    saveTasks();
                    renderTasks();
                    showNotification('تم استيراد المهام بنجاح', 'success');
                } else {
                    showNotification('ملف غير صالح', 'error');
                }
            } catch (err) {
                showNotification('حدث خطأ أثناء الاستيراد', 'error');
            }
        };
        reader.readAsText(file);
    });
    input.click();
}

// نافذة حول التطبيق
function showAboutDialog() {
    alert('تطبيق قائمة المهام المتقدم\nإصدار متطور مع ميزات متقدمة وإمكانيات تصدير واستيراد.\nتم التطوير بواسطة الذكاء الاصطناعي.');
}

// دالة تحويل ملف إلى base64
function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = reject;
        reader.readAsDataURL(file);
    });
}

// إضافة مستمع عام واحد لإغلاق النماذج عند النقر خارجها
function globalCloseForms(e) {
    document.querySelectorAll('.task-edit-form.active, .delete-confirmation.active').forEach(form => {
        if (!form.contains(e.target)) {
            form.classList.remove('active');
        }
    });
}

// خاصية سحب وتوسيع القائمة الجانبية
function setupSidebarResize() {
    const sideMenu = document.getElementById('sideMenu');
    if (!sideMenu) return;
    // إضافة مقبض السحب
    let dragger = document.getElementById('sidebarDragger');
    if (!dragger) {
        dragger = document.createElement('div');
        dragger.id = 'sidebarDragger';
        dragger.title = 'اسحب لتوسيع القائمة';
        sideMenu.appendChild(dragger);
    }
    dragger.style.position = 'absolute';
    dragger.style.top = '0';
    dragger.style.left = '-7px';
    dragger.style.width = '14px';
    dragger.style.height = '100%';
    dragger.style.cursor = 'ew-resize';
    dragger.style.zIndex = '10000';
    dragger.style.background = 'rgba(25,118,210,0.07)';
    dragger.style.borderRadius = '8px 0 0 8px';
    let isDragging = false;
    let startX = 0;
    let startWidth = 0;
    dragger.addEventListener('mousedown', (e) => {
        isDragging = true;
        startX = e.clientX;
        startWidth = sideMenu.offsetWidth;
        document.body.style.userSelect = 'none';
    });
    document.addEventListener('mousemove', (e) => {
        if (!isDragging) return;
        // عكس اتجاه السحب: السحب لليسار يكبر القائمة
        let newWidth = startWidth - (e.clientX - startX);
        newWidth = Math.max(180, Math.min(newWidth, 500));
        sideMenu.style.width = newWidth + 'px';
    });
    document.addEventListener('mouseup', () => {
        if (isDragging) {
            isDragging = false;
            localStorage.setItem('sidebarWidth', sideMenu.offsetWidth);
            document.body.style.userSelect = '';
        }
    });
    // عند تحميل الصفحة، استرجع العرض المحفوظ
    const savedWidth = localStorage.getItem('sidebarWidth');
    if (savedWidth) {
        sideMenu.style.width = savedWidth + 'px';
    }
}

// ========== تخصيص الألوان والثيمات - منطق تفعيل ==========

// عناصر تخصيص الألوان والثيمات
const themePresets = document.querySelectorAll('.theme-preset');
const customColorPicker = document.getElementById('customColorPicker');
const colorPreview = document.getElementById('colorPreview');
const modeBtns = document.querySelectorAll('.mode-btn');
const advancedColorsCheckbox = document.getElementById('advancedColors');
const advancedColorsSection = document.getElementById('advancedColorsSection');
const backgroundColorPicker = document.getElementById('backgroundColorPicker');
const textColorPicker = document.getElementById('textColorPicker');
const borderColorPicker = document.getElementById('borderColorPicker');
const opacitySlider = document.getElementById('opacitySlider');
const opacityValue = document.getElementById('opacityValue');
const livePreview = document.getElementById('livePreview');

// إعدادات الألوان والثيمات
let themeSettings = {
  preset: 'default',
  primaryColor: '#1976d2',
  mode: 'auto',
  advanced: {
    backgroundColor: '#ffffff',
    textColor: '#333333',
    borderColor: '#e0e0e0',
    opacity: 1
  }
};

// ========== تفعيل الثيمات الجاهزة ==========
function activateThemePreset(presetName) {
  // إزالة الفئة النشطة من جميع الثيمات
  themePresets.forEach(preset => preset.classList.remove('active'));
  
  // إضافة الفئة النشطة للثيم المختار
  const selectedPreset = document.querySelector(`[data-theme="${presetName}"]`);
  if (selectedPreset) {
    selectedPreset.classList.add('active');
  }
  
  // تطبيق الثيم
  document.documentElement.setAttribute('data-theme-preset', presetName);
  themeSettings.preset = presetName;
  
  // تحديث اللون الرئيسي حسب الثيم
  const presetColors = {
    'default': '#1976d2',
    'ocean': '#006064',
    'forest': '#2e7d32',
    'sunset': '#ff5722',
    'purple': '#7b1fa2'
  };
  
  if (presetColors[presetName]) {
    themeSettings.primaryColor = presetColors[presetName];
    primaryColorSelect.value = themeSettings.primaryColor;
    customColorPicker.value = themeSettings.primaryColor;
    updateColorPreview();
    applyThemeSettings();
  }
}

// ربط أحداث الثيمات الجاهزة
themePresets.forEach(preset => {
  preset.addEventListener('click', () => {
    const themeName = preset.getAttribute('data-theme');
    activateThemePreset(themeName);
    applySettingsToWebsite(); // تطبيق فوري
  });
});

// ========== تفعيل اختيار اللون المخصص ==========
function updateColorPreview() {
  if (colorPreview) {
    colorPreview.style.background = themeSettings.primaryColor;
  }
}

function applyCustomColor(color) {
  themeSettings.primaryColor = color;
  themeSettings.preset = 'custom';
  
  // إزالة الفئة النشطة من الثيمات الجاهزة
  themePresets.forEach(preset => preset.classList.remove('active'));
  
  updateColorPreview();
  applyThemeSettings();
}

// ربط أحداث اختيار اللون
if (customColorPicker) {
  customColorPicker.addEventListener('change', (e) => {
    applyCustomColor(e.target.value);
    applySettingsToWebsite(); // تطبيق فوري
  });
}

if (primaryColorSelect) {
  primaryColorSelect.addEventListener('change', (e) => {
    applyCustomColor(e.target.value);
    applySettingsToWebsite(); // تطبيق فوري
  });
}

// ========== تفعيل أزرار الوضع ==========
function activateMode(mode) {
  // إزالة الفئة النشطة من جميع الأزرار
  modeBtns.forEach(btn => btn.classList.remove('active'));
  
  // إضافة الفئة النشطة للزر المختار
  const selectedBtn = document.querySelector(`[data-mode="${mode}"]`);
  if (selectedBtn) {
    selectedBtn.classList.add('active');
  }
  
  // تطبيق الوضع
  themeSettings.mode = mode;
  applyThemeMode();
}

function applyThemeMode() {
  if (themeSettings.mode === 'dark') {
    document.documentElement.setAttribute('data-theme', 'dark');
  } else if (themeSettings.mode === 'light') {
    document.documentElement.setAttribute('data-theme', 'light');
  } else {
    // تلقائي حسب النظام
    if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
      document.documentElement.setAttribute('data-theme', 'dark');
    } else {
      document.documentElement.setAttribute('data-theme', 'light');
    }
  }
}

// ربط أحداث أزرار الوضع
modeBtns.forEach(btn => {
  btn.addEventListener('click', () => {
    const mode = btn.getAttribute('data-mode');
    activateMode(mode);
    applySettingsToWebsite(); // تطبيق فوري
  });
});

// ========== تفعيل التخصيص المتقدم ==========
if (advancedColorsCheckbox) {
  advancedColorsCheckbox.addEventListener('change', (e) => {
    if (e.target.checked) {
      advancedColorsSection.style.display = 'block';
      themeSettings.advanced.enabled = true;
    } else {
      advancedColorsSection.style.display = 'none';
      themeSettings.advanced.enabled = false;
      // إعادة تعيين الألوان للافتراضية
      resetAdvancedColors();
    }
  });
}

function resetAdvancedColors() {
  themeSettings.advanced = {
    backgroundColor: '#ffffff',
    textColor: '#333333',
    borderColor: '#e0e0e0',
    opacity: 1
  };
  
  if (backgroundColorPicker) backgroundColorPicker.value = themeSettings.advanced.backgroundColor;
  if (textColorPicker) textColorPicker.value = themeSettings.advanced.textColor;
  if (borderColorPicker) borderColorPicker.value = themeSettings.advanced.borderColor;
  if (opacitySlider) opacitySlider.value = themeSettings.advanced.opacity;
  if (opacityValue) opacityValue.textContent = '100%';
  
  applyAdvancedColors();
}

function applyAdvancedColors() {
  if (!themeSettings.advanced.enabled) return;
  
  document.documentElement.style.setProperty('--bg-color', themeSettings.advanced.backgroundColor);
  document.documentElement.style.setProperty('--text-color', themeSettings.advanced.textColor);
  document.documentElement.style.setProperty('--border-color', themeSettings.advanced.borderColor);
  document.documentElement.style.setProperty('--opacity', themeSettings.advanced.opacity);
  
  // تطبيق الشفافية على العناصر
  const elements = document.querySelectorAll('.task-card, .sidebar, .header');
  elements.forEach(el => {
    el.style.opacity = themeSettings.advanced.opacity;
  });
}

// ربط أحداث التخصيص المتقدم
if (backgroundColorPicker) {
  backgroundColorPicker.addEventListener('change', (e) => {
    themeSettings.advanced.backgroundColor = e.target.value;
    applyAdvancedColors();
    applySettingsToWebsite(); // تطبيق فوري
  });
}

if (textColorPicker) {
  textColorPicker.addEventListener('change', (e) => {
    themeSettings.advanced.textColor = e.target.value;
    applyAdvancedColors();
    applySettingsToWebsite(); // تطبيق فوري
  });
}

if (borderColorPicker) {
  borderColorPicker.addEventListener('change', (e) => {
    themeSettings.advanced.borderColor = e.target.value;
    applyAdvancedColors();
    applySettingsToWebsite(); // تطبيق فوري
  });
}

if (opacitySlider) {
  opacitySlider.addEventListener('input', (e) => {
    const opacity = e.target.value;
    themeSettings.advanced.opacity = opacity;
    if (opacityValue) {
      opacityValue.textContent = Math.round(opacity * 100) + '%';
    }
    applyAdvancedColors();
    applySettingsToWebsite(); // تطبيق فوري
  });
}

// ========== تطبيق جميع إعدادات الثيم ==========
function applyThemeSettings() {
  // تحديث متغيرات CSS في :root
  const vars = getThemeVars(themeSettings.preset, themeSettings.advanced, themeSettings.mode);
  setRootThemeVars(vars);
  // تحديث المعاينة المباشرة
  updateLivePreview();
}

// ========== تحديث المعاينة المباشرة ==========
function updateLivePreview() {
  if (!livePreview) return;
  
  const previewHeader = livePreview.querySelector('.preview-header');
  const previewSidebar = livePreview.querySelector('.preview-sidebar');
  const previewTask = livePreview.querySelector('.preview-task');
  const previewButton = livePreview.querySelector('.preview-button');
  
  if (previewHeader) {
    previewHeader.style.background = themeSettings.primaryColor;
  }
  
  if (previewButton) {
    previewButton.style.background = themeSettings.primaryColor;
  }
  
  if (themeSettings.advanced.enabled) {
    if (previewSidebar) {
      previewSidebar.style.background = themeSettings.advanced.backgroundColor;
      previewSidebar.style.color = themeSettings.advanced.textColor;
    }
    
    if (previewTask) {
      previewTask.style.background = themeSettings.advanced.backgroundColor;
      previewTask.style.color = themeSettings.advanced.textColor;
      previewTask.style.borderColor = themeSettings.advanced.borderColor;
    }
  }
}

// ========== حفظ/تحميل إعدادات الثيم ==========
function saveThemeSettings() {
  localStorage.setItem('tdl_theme_settings', JSON.stringify(themeSettings));
}

function loadThemeSettings() {
  const saved = localStorage.getItem('tdl_theme_settings');
  if (saved) {
    try {
      themeSettings = { ...themeSettings, ...JSON.parse(saved) };
    } catch {}
  }
  
  // تطبيق الإعدادات المحفوظة
  activateThemePreset(themeSettings.preset);
  activateMode(themeSettings.mode);
  
  if (themeSettings.advanced.enabled) {
    if (advancedColorsCheckbox) advancedColorsCheckbox.checked = true;
    if (advancedColorsSection) advancedColorsSection.style.display = 'block';
    applyAdvancedColors();
  }
  
  applyThemeSettings();
}

// ========== تفعيل عند التحميل ==========
document.addEventListener('DOMContentLoaded', () => {
  loadSettings();
  loadThemeSettings();
  applySettings();
  applyThemeSettings();
  bindSettingsEvents();
  updateColorPreview();
  updateLivePreview();
});

// ========== تطبيق الإعدادات على الموقع الفعلي ==========
function applySettingsToWebsite() {
  applyThemeSettings();
  // تحديث حجم الخط، الحواف، الظلال، الحركات
  const fontSize = settings.fontSize === 'small' ? '14px' : settings.fontSize === 'large' ? '18px' : '16px';
  document.body.style.fontSize = fontSize;
  document.body.classList.toggle('rounded', settings.roundedBorders);
  document.body.classList.toggle('shadows', settings.shadows);
  document.body.classList.toggle('animations', settings.animations);
}

// ========== تحديث متغيرات CSS للثيم بشكل شامل ==========
function setRootThemeVars(vars) {
  for (const key in vars) {
    document.documentElement.style.setProperty(key, vars[key]);
  }
}

function getThemeVars(preset, advanced, mode) {
  // القيم الافتراضية لكل ثيم
  const presets = {
    default: {
      '--primary': '#1976d2',
      '--text-color': '#333',
      '--bg-color': '#fff',
      '--border-color': '#e0e0e0',
      '--sidebar-bg': '#f5f5f5',
      '--task-bg': '#fff',
      '--shadow': '0 2px 8px rgba(0,0,0,0.1)'
    },
    ocean: {
      '--primary': '#006064',
      '--text-color': '#004d40',
      '--bg-color': '#e0f2f1',
      '--border-color': '#b2dfdb',
      '--sidebar-bg': '#e8f5e8',
      '--task-bg': '#f1f8e9',
      '--shadow': '0 2px 8px rgba(0, 96, 100, 0.15)'
    },
    forest: {
      '--primary': '#2e7d32',
      '--text-color': '#1b5e20',
      '--bg-color': '#e8f5e8',
      '--border-color': '#c8e6c9',
      '--sidebar-bg': '#f1f8e9',
      '--task-bg': '#f9fbe7',
      '--shadow': '0 2px 8px rgba(46, 125, 50, 0.15)'
    },
    sunset: {
      '--primary': '#ff5722',
      '--text-color': '#bf360c',
      '--bg-color': '#fff3e0',
      '--border-color': '#ffcc02',
      '--sidebar-bg': '#fff8e1',
      '--task-bg': '#fffde7',
      '--shadow': '0 2px 8px rgba(255, 87, 34, 0.15)'
    },
    purple: {
      '--primary': '#7b1fa2',
      '--text-color': '#4a148c',
      '--bg-color': '#f3e5f5',
      '--border-color': '#e1bee7',
      '--sidebar-bg': '#f8f0fc',
      '--task-bg': '#faf5ff',
      '--shadow': '0 2px 8px rgba(123, 31, 162, 0.15)'
    },
    custom: {
      '--primary': themeSettings.primaryColor,
      '--text-color': '#333',
      '--bg-color': '#fff',
      '--border-color': '#e0e0e0',
      '--sidebar-bg': '#f5f5f5',
      '--task-bg': '#fff',
      '--shadow': '0 2px 8px rgba(0,0,0,0.1)'
    }
  };
  let vars = { ...presets[preset] };
  // تخصيص متقدم
  if (advanced && advanced.enabled) {
    vars['--bg-color'] = advanced.backgroundColor;
    vars['--text-color'] = advanced.textColor;
    vars['--border-color'] = advanced.borderColor;
    vars['--opacity'] = advanced.opacity;
  }
  // الوضع الليلي
  if (mode === 'dark') {
    vars['--bg-color'] = '#23272f';
    vars['--text-color'] = '#90caf9';
    vars['--sidebar-bg'] = '#2c313a';
    vars['--task-bg'] = '#23272f';
    vars['--border-color'] = '#444';
  }
  return vars;
}

// ========== وظائف الإشعارات ==========
// function showNotification(msg, type = 'info') {
//   // مكان الإشعار
//   let position = settings.notifPosition || 'bottom';
//   let duration = 2000;
//   if (settings.notifDuration === 'short') duration = 1200;
//   if (settings.notifDuration === 'long') duration = 4000;
//   // صوت
//   if (settings.notifSound) {
//     const audio = new Audio('https://cdn.pixabay.com/audio/2022/07/26/audio_124bfae1b2.mp3');
//     audio.volume = 0.2;
//     audio.play();
//   }
//   // إشعار بسيط (يمكنك تطويره لاحقًا)
//   let notif = document.createElement('div');
//   notif.className = `custom-notif notif-${type} notif-pos-${position}`;
//   notif.textContent = msg;
//   document.body.appendChild(notif);
//   setTimeout(() => notif.remove(), duration);
// }

// ========== وظائف المهام ==========
// تأكيد الحذف
function confirmDeleteTask(cb) {
  if (settings.confirmDelete) {
    if (confirm('هل أنت متأكد أنك تريد حذف هذه المهمة؟')) cb();
  } else {
    cb();
  }
}
// تفعيل التراجع عن الحذف
let lastDeletedTask = null;
function deleteTaskWithUndo(taskId) {
  const task = getTaskById(taskId);
  if (!task) return;
  confirmDeleteTask(() => {
    lastDeletedTask = { ...task };
    deleteTask(taskId);
    if (settings.undoDelete) {
      showNotification('تم حذف المهمة. <button id="undoDeleteBtn">تراجع</button>', 'warning');
      setTimeout(() => {
        const btn = document.getElementById('undoDeleteBtn');
        if (btn) btn.onclick = () => {
          restoreTask(lastDeletedTask);
          showNotification('تم استرجاع المهمة!', 'success');
        };
      }, 100);
    }
  });
}
// طريقة العرض الافتراضية
function setTasksView(view) {
  // view: 'list' | 'grid'
  document.body.setAttribute('data-tasks-view', view);
}
// إضافة مهمة كمكتملة مباشرة
function addTaskWithStatus(text, completed) {
  addTask(text, settings.addCompleted ? true : completed);
}

// ========== وظائف البحث ==========
let searchDebounce;
function setInstantSearch(enabled) {
  const searchInput = document.getElementById('searchInput');
  if (!searchInput) return;
  if (enabled) {
    searchInput.oninput = function() {
      clearTimeout(searchDebounce);
      searchDebounce = setTimeout(() => {
        performSearch(searchInput.value, settings.searchScope);
      }, 200);
    };
  } else {
    searchInput.oninput = null;
  }
}
function performSearch(query, scope) {
  // scope: 'text' | 'all'
  // ابحث في نص المهمة فقط أو كل الحقول
  // ... منطق البحث حسب التطبيق ...
}

// ========== وظائف الواجهة ==========
// حجم الخط، الحواف، الظلال، الحركات كلها مفعلة في applySettingsToWebsite()

// ========== وظائف الحساب ==========
const currentUsername = document.getElementById('currentUsername');
const changeUsernameBtn = document.getElementById('changeUsernameBtn');
const changePasswordBtn = document.getElementById('changePasswordBtn');
const logoutAllBtn = document.getElementById('logoutAllBtn');

if (changeUsernameBtn) {
  changeUsernameBtn.onclick = function() {
    const newName = prompt('أدخل اسم المستخدم الجديد:');
    if (newName && newName.trim().length > 2) {
      localStorage.setItem('tdl_username', newName.trim());
      if (currentUsername) currentUsername.textContent = newName.trim();
      showNotification('تم تغيير اسم المستخدم!', 'success');
    }
  };
}
if (changePasswordBtn) {
  changePasswordBtn.onclick = function() {
    const newPass = prompt('أدخل كلمة المرور الجديدة:');
    if (newPass && newPass.length >= 4) {
      localStorage.setItem('tdl_password', newPass);
      showNotification('تم تغيير كلمة المرور!', 'success');
    }
  };
}
if (logoutAllBtn) {
  logoutAllBtn.onclick = function() {
    if (confirm('هل تريد تسجيل الخروج من جميع الأجهزة؟')) {
      localStorage.removeItem('tdl_token');
      showNotification('تم تسجيل الخروج من جميع الأجهزة!', 'success');
      setTimeout(() => location.reload(), 800);
    }
  };
}

// ========== وظائف متقدمة ==========
if (exportTasksBtn) {
  exportTasksBtn.onclick = function() {
    if (typeof exportTasks === 'function') exportTasks();
    else showNotification('تم تصدير المهام (تجريبي).', 'info');
  };
}
if (importTasksBtn) {
  importTasksBtn.onclick = function() {
    if (typeof importTasks === 'function') importTasks();
    else showNotification('تم استيراد المهام (تجريبي).', 'info');
  };
}
if (resetSettingsBtn) {
  resetSettingsBtn.onclick = function() {
    if (confirm('هل أنت متأكد أنك تريد إعادة تعيين جميع الإعدادات؟')) {
      localStorage.clear();
      showNotification('تمت إعادة تعيين الإعدادات!', 'success');
      setTimeout(() => location.reload(), 800);
    }
  };
}
if (deleteAllTasksBtn) {
  deleteAllTasksBtn.onclick = function() {
    if (confirm('هل أنت متأكد أنك تريد حذف جميع المهام؟')) {
      if (typeof deleteAllTasks === 'function') deleteAllTasks();
      else showNotification('تم حذف جميع المهام (تجريبي).', 'warning');
    }
  };
}

// ========== حول التطبيق ==========
const supportLink = document.getElementById('supportLink');
const privacyLink = document.getElementById('privacyLink');
if (supportLink) supportLink.onclick = e => { e.preventDefault(); showNotification('للدعم: <EMAIL>', 'info'); };
if (privacyLink) privacyLink.onclick = e => { e.preventDefault(); showNotification('سياسة الخصوصية: جميع بياناتك محلية وآمنة.', 'info'); };

// ========== تحديث اسم المستخدم عند التحميل ==========
document.addEventListener('DOMContentLoaded', () => {
  const savedName = localStorage.getItem('tdl_username');
  if (savedName && currentUsername) currentUsername.textContent = savedName;
});

// ========== تفعيل زر تبديل الوضع الليلي في الهيدر ==========
const themeToggle = document.getElementById('themeToggle');
const themeToggleIcon = themeToggle ? themeToggle.querySelector('i') : null;
const themeToggleSidebar = document.getElementById('themeToggleSidebar');
const themeToggleSidebarIcon = themeToggleSidebar ? themeToggleSidebar.querySelector('i') : null;

function setDarkMode(isDark) {
  if (isDark) {
    document.documentElement.setAttribute('data-theme', 'dark');
    localStorage.setItem('darkMode', 'true');
  } else {
    document.documentElement.setAttribute('data-theme', 'light');
    localStorage.setItem('darkMode', 'false');
  }
  updateThemeToggleButton();
}

function updateThemeToggleButton() {
  if (!themeToggle || !themeToggleIcon) return;
  const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
  if (isDark) {
    themeToggleIcon.className = 'fas fa-sun';
    themeToggle.title = 'تبديل إلى الوضع النهاري';
    if (themeToggleSidebarIcon) {
      themeToggleSidebarIcon.className = 'fas fa-sun';
      themeToggleSidebar.title = 'تبديل إلى الوضع النهاري';
    }
  } else {
    themeToggleIcon.className = 'fas fa-moon';
    themeToggle.title = 'تبديل إلى الوضع الليلي';
    if (themeToggleSidebarIcon) {
      themeToggleSidebarIcon.className = 'fas fa-moon';
      themeToggleSidebar.title = 'تبديل إلى الوضع الليلي';
    }
  }
}

function toggleThemeFromHeader() {
  const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
  setDarkMode(!isDark);
  showNotification(isDark ? 'تم التبديل إلى الوضع النهاري' : 'تم التبديل إلى الوضع الليلي', 'success');
}

if (themeToggle) {
  themeToggle.addEventListener('click', toggleThemeFromHeader);
}
if (themeToggleSidebar) {
  themeToggleSidebar.addEventListener('click', toggleThemeFromHeader);
}

// عند تحميل الصفحة، طبق الوضع المحفوظ أو الافتراضي
if (localStorage.getItem('darkMode') === 'true' || (!localStorage.getItem('darkMode') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
  setDarkMode(true);
} else {
  setDarkMode(false);
}

// ========== تحسينات الأداء والديناميكية ==========

// تحسين الأداء باستخدام requestAnimationFrame
function smoothUpdate(callback) {
  requestAnimationFrame(callback);
}

// تحسين البحث باستخدام debounce محسن
function createDebounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// تحسين إضافة/حذف المهام مع تأثيرات حركية
function addTaskWithAnimation(text, category = 'عمل', completed = false) {
  const task = {
    id: Date.now(),
    text: text,
    category: category,
    completed: completed,
    createdAt: new Date().toISOString()
  };
  
  tasks.push(task);
  saveTasks();
  
  // إضافة مع تأثير حركي
  const taskElement = createTaskElement(task);
  taskElement.style.opacity = '0';
  taskElement.style.transform = 'translateY(-20px)';
  
  const taskList = document.getElementById('taskList');
  const emptyState = document.getElementById('emptyState');
  
  if (emptyState) emptyState.style.display = 'none';
  
  taskList.insertBefore(taskElement, taskList.firstChild);
  
  // تأثير ظهور سلس
  smoothUpdate(() => {
    taskElement.style.transition = 'all 0.3s ease';
    taskElement.style.opacity = '1';
    taskElement.style.transform = 'translateY(0)';
  });
  
  updateStats();
  showNotification('تم إضافة المهمة بنجاح!', 'success');
}

function deleteTaskWithAnimation(taskId) {
  const taskElement = document.querySelector(`[data-task-id="${taskId}"]`);
  if (!taskElement) return;
  
  // تأثير حذف سلس
  taskElement.style.transition = 'all 0.3s ease';
  taskElement.style.opacity = '0';
  taskElement.style.transform = 'translateX(100px)';
  
  setTimeout(() => {
    tasks = tasks.filter(task => task.id !== taskId);
    saveTasks();
    taskElement.remove();
    updateStats();
    
    // إظهار الحالة الفارغة إذا لم تتبق مهام
    if (tasks.length === 0) {
      const emptyState = document.getElementById('emptyState');
      if (emptyState) {
        emptyState.style.display = 'block';
        emptyState.style.animation = 'fadeIn 0.5s ease';
      }
    }
    
    showNotification('تم حذف المهمة!', 'warning');
  }, 300);
}

// تحسين البحث الفوري
const debouncedSearch = createDebounce((query) => {
  const taskElements = document.querySelectorAll('.task-card');
  const searchScope = settings.searchScope || 'all';
  
  taskElements.forEach(element => {
    const taskText = element.querySelector('.task-text').textContent.toLowerCase();
    const taskCategory = element.querySelector('.task-category').textContent.toLowerCase();
    const queryLower = query.toLowerCase();
    
    let shouldShow = false;
    
    if (searchScope === 'all') {
      shouldShow = taskText.includes(queryLower) || taskCategory.includes(queryLower);
    } else {
      shouldShow = taskText.includes(queryLower);
    }
    
    if (shouldShow || query === '') {
      element.style.display = 'block';
      element.style.animation = 'slideIn 0.3s ease';
    } else {
      element.style.display = 'none';
    }
  });
}, 150);

// تحسين تصفية الأقسام
function filterByCategory(category) {
  const taskElements = document.querySelectorAll('.task-card');
  const catBtns = document.querySelectorAll('.cat-btn');
  
  // تحديث الأزرار النشطة
  catBtns.forEach(btn => btn.classList.remove('active'));
  document.querySelector(`[data-category="${category}"]`).classList.add('active');
  
  taskElements.forEach((element, index) => {
    const taskCategory = element.querySelector('.task-category').textContent;
    const shouldShow = category === 'all' || taskCategory === category;
    
    if (shouldShow) {
      element.style.display = 'block';
      element.style.animationDelay = `${index * 50}ms`;
      element.style.animation = 'slideIn 0.3s ease';
    } else {
      element.style.display = 'none';
    }
  });
}

// تحسين تبديل طريقة العرض
function toggleViewMode(view) {
  const taskList = document.getElementById('taskList');
  const viewBtns = document.querySelectorAll('.view-toggle-btn');
  
  // تحديث الأزرار النشطة
  viewBtns.forEach(btn => btn.classList.remove('active'));
  document.querySelector(`[data-view="${view}"]`).classList.add('active');
  
  // تطبيق طريقة العرض
  taskList.setAttribute('data-view', view);
  
  // تأثير انتقالي
  taskList.style.opacity = '0.5';
  setTimeout(() => {
    taskList.style.opacity = '1';
    taskList.style.transition = 'opacity 0.3s ease';
  }, 150);
}

// تحسين الإحصائيات مع تأثيرات
function updateStatsWithAnimation() {
    const totalTasks = tasks.length;
    const completedTasks = tasks.filter(task => task.completed).length;
    const activeTasks = totalTasks - completedTasks;
    // تحديث شريط الإحصائيات
    if (elements.statsBar) {
        const statBoxes = elements.statsBar.querySelectorAll('.stat-box span');
        if (statBoxes.length >= 3) {
            animateNumber(statBoxes[0], parseInt(statBoxes[0].textContent) || 0, totalTasks);
            animateNumber(statBoxes[1], parseInt(statBoxes[1].textContent) || 0, completedTasks);
            animateNumber(statBoxes[2], parseInt(statBoxes[2].textContent) || 0, activeTasks);
            // تأثير لمعة عند التحديث
            statBoxes.forEach(span => {
                span.parentElement.classList.add('stat-glow');
                setTimeout(() => {
                    span.parentElement.classList.remove('stat-glow');
                }, 500);
            });
        }
    }
    // تحديث عداد المهام في الهيدر إذا كان موجوداً
    if (elements.taskCount) {
        elements.taskCount.innerHTML = `
            <span class="active">
                <i class="fas fa-bolt"></i> ${activeTasks} نشطة
            </span>
            <span class="completed">
                <i class="fas fa-check-circle"></i> ${completedTasks} مكتملة
            </span>
            <span class="total">
                <i class="fas fa-list-ol"></i> ${totalTasks} إجمالي المهام
            </span>
        `;
    }
}

function animateNumber(element, start, end) {
    const duration = 800;
    const startTime = performance.now();
    
    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // استخدام دالة easeOutQuart للحصول على تأثير سلس
        const easeProgress = 1 - Math.pow(1 - progress, 4);
        const current = Math.round(start + (end - start) * easeProgress);
        
        element.textContent = current;
        
        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }
    
    requestAnimationFrame(update);
}

// تحسين دالة حفظ المهام
// function saveTasks() {
//     try {
//         localStorage.setItem('tasks', JSON.stringify(tasks));
//         localStorage.setItem('lastUpdated', new Date().toISOString());
//     } catch (error) {
//         console.error('خطأ في حفظ المهام:', error);
//         showNotification('حدث خطأ في حفظ المهام', 'error');
//     }
// }

// تحسين دالة تحميل المهام
function loadTasks() {
    try {
        const savedTasks = localStorage.getItem('tasks');
        if (savedTasks) {
            tasks = JSON.parse(savedTasks);
            // تحديث المهام القديمة لتتضمن الأولوية إذا لم تكن موجودة
            tasks.forEach(task => {
                if (!task.priority) {
                    task.priority = 'medium';
                }
                if (!task.category) {
                    task.category = 'عام';
                }
            });
        }
    } catch (error) {
        console.error('خطأ في تحميل المهام:', error);
        tasks = [];
        showNotification('حدث خطأ في تحميل المهام', 'error');
    }
}

// دالة تبديل حالة إكمال المهمة
function toggleTaskCompletion(taskId) {
    const task = tasks.find(t => t.id === taskId);
    if (!task) return;
    
    // تبديل حالة الإكمال
    task.completed = !task.completed;
    task.completedAt = task.completed ? new Date().toISOString() : null;
    
    // حفظ التغييرات
    saveTasks();
    
    // تحديث العنصر في الواجهة
    const taskElement = document.querySelector(`[data-task-id="${taskId}"]`);
    if (taskElement) {
        // تحديث الكلاس
        taskElement.classList.toggle('completed', task.completed);
        
        // تحديث زر الإكمال
        const completeBtn = taskElement.querySelector('.complete-task-btn');
        if (completeBtn) {
            completeBtn.innerHTML = task.completed ? 
                '<i class="fas fa-check-circle"></i>' : 
                '<i class="far fa-circle"></i>';
            completeBtn.title = task.completed ? 'إلغاء الإكمال' : 'إكمال المهمة';
        }
        
        // تحديث نص المهمة
        const taskText = taskElement.querySelector('.task-text');
        if (taskText) {
            taskText.style.textDecoration = task.completed ? 'line-through' : 'none';
            taskText.style.opacity = task.completed ? '0.6' : '1';
        }
        
        // تأثير بصري
        taskElement.style.transition = 'all 0.3s ease';
        if (task.completed) {
            taskElement.style.transform = 'scale(0.98)';
            setTimeout(() => {
                taskElement.style.transform = 'scale(1)';
            }, 150);
        }
    }
    
    // تحديث الإحصائيات
    updateStatsWithAnimation();
    
    // إشعار
    const message = task.completed ? 'تم إكمال المهمة بنجاح! 🎉' : 'تم إلغاء إكمال المهمة';
    showNotification(message, 'success');
}

// دالة التهيئة النهائية
function finalizeInitialization() {
    // تحميل المهام والإعدادات
    loadTasks();
    loadSettings();
    loadThemeSettings();
    
    // تطبيق الإعدادات المحفوظة
    applySettings();
    applySettingsToWebsite();
    
    // تهيئة العناصر
    initElements();
    
    // إعداد مستمعي الأحداث
    setupEventListeners();
    setupSideMenuEvents();
    setupSidebarResize();
    setupDeleteConfirmationEvents();
    bindSettingsEvents();
    
    // تحميل وعرض المهام
    loadAndRenderTasks();
    
    // تطبيق الوضع الليلي الافتراضي
    if (localStorage.getItem('darkMode') === 'true' || 
        (!localStorage.getItem('darkMode') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.documentElement.setAttribute('data-theme', 'dark');
        updateThemeToggleButton();
    }
    
    // تحميل طريقة العرض المحفوظة
    loadSavedViewMode();
    
    // تحديث الإحصائيات
    updateStatsWithAnimation();
    
    // إظهار رسالة ترحيب
    setTimeout(() => {
        if (tasks.length === 0) {
            showNotification('مرحباً بك في تطبيق المهام المتقدم! 🎉', 'info');
        }
    }, 1000);
    
    console.log('✅ تم تهيئة التطبيق بنجاح!');
}

// تحميل طريقة العرض المحفوظة
function loadSavedViewMode() {
    const savedView = localStorage.getItem('viewMode');
    if (savedView && (savedView === 'list' || savedView === 'grid')) {
        toggleViewMode(savedView);
    }
}

// تفعيل الفلاتر في القائمة الجانبية
function setupSidebarFilters() {
    const filterButtons = document.querySelectorAll('.nav-btn[data-filter]');
    
    filterButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            // إزالة الفئة النشطة من جميع الأزرار
            filterButtons.forEach(b => b.classList.remove('active'));
            // إضافة الفئة النشطة للزر المحدد
            btn.classList.add('active');
            
            const filter = btn.getAttribute('data-filter');
            applyFilter(filter);
        });
    });
}

// تفعيل الأقسام في القائمة الجانبية
function setupSidebarCategories() {
    const categoryButtons = document.querySelectorAll('.cat-btn[data-category]');
    
    categoryButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            // إزالة الفئة النشطة من جميع الأزرار
            categoryButtons.forEach(b => b.classList.remove('active'));
            // إضافة الفئة النشطة للزر المحدد
            btn.classList.add('active');
            
            const category = btn.getAttribute('data-category');
            applyCategoryFilter(category);
        });
    });
}

// تفعيل أزرار العرض (قائمة/شبكة)
function setupViewToggle() {
    const viewButtons = document.querySelectorAll('.view-toggle-btn[data-view]');
    
    viewButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            // إزالة الفئة النشطة من جميع الأزرار
            viewButtons.forEach(b => b.classList.remove('active'));
            // إضافة الفئة النشطة للزر المحدد
            btn.classList.add('active');
            
            const view = btn.getAttribute('data-view');
            toggleViewMode(view);
        });
    });
}

// تطبيق الفلتر
function applyFilter(filter) {
    const taskElements = document.querySelectorAll('.task-card');
    let visibleCount = 0;
    
    taskElements.forEach(element => {
        const isCompleted = element.classList.contains('completed');
        
        switch(filter) {
            case 'all':
                element.style.display = 'block';
                visibleCount++;
                break;
            case 'active':
                if (!isCompleted) {
                    element.style.display = 'block';
                    visibleCount++;
                } else {
                    element.style.display = 'none';
                }
                break;
            case 'completed':
                if (isCompleted) {
                    element.style.display = 'block';
                    visibleCount++;
                } else {
                    element.style.display = 'none';
                }
                break;
        }
    });
    
    // إظهار/إخفاء الحالة الفارغة
    const emptyState = document.getElementById('emptyState');
    if (emptyState) {
        if (visibleCount === 0) {
            emptyState.style.display = 'flex';
            emptyState.innerHTML = `
                <i class="fas fa-filter"></i>
                <p>لا توجد مهام ${filter === 'active' ? 'نشطة' : filter === 'completed' ? 'مكتملة' : ''}</p>
            `;
        } else {
            emptyState.style.display = 'none';
        }
    }
    
    // إشعار
    const filterNames = {
        'all': 'كل المهام',
        'active': 'النشطة',
        'completed': 'المكتملة'
    };
    showNotification(`تم عرض ${filterNames[filter]} (${visibleCount} مهمة)`, 'info');
}

// تطبيق فلتر الأقسام
function applyCategoryFilter(category) {
    const taskElements = document.querySelectorAll('.task-card');
    let visibleCount = 0;
    
    taskElements.forEach(element => {
        const elementCategory = element.getAttribute('data-category');
        
        if (category === 'all' || elementCategory === category) {
            element.style.display = 'block';
            visibleCount++;
        } else {
            element.style.display = 'none';
        }
    });
    
    // إظهار/إخفاء الحالة الفارغة
    const emptyState = document.getElementById('emptyState');
    if (emptyState) {
        if (visibleCount === 0) {
            emptyState.style.display = 'flex';
            emptyState.innerHTML = `
                <i class="fas fa-folder"></i>
                <p>لا توجد مهام في قسم "${category}"</p>
            `;
        } else {
            emptyState.style.display = 'none';
        }
    }
    
    // إشعار
    const categoryNames = {
        'all': 'كل الأقسام',
        'عمل': 'قسم العمل',
        'شخصي': 'القسم الشخصي',
        'دراسة': 'قسم الدراسة'
    };
    showNotification(`تم عرض ${categoryNames[category]} (${visibleCount} مهمة)`, 'info');
}

// تبديل طريقة العرض
// function toggleViewMode(view) {
//     const taskList = document.getElementById('taskList');
//     if (!taskList) return;
    
//     // إزالة الفئة النشطة من جميع أزرار العرض
//     document.querySelectorAll('.view-toggle-btn').forEach(btn => {
//         btn.classList.remove('active');
//     });
    
//     // إضافة الفئة النشطة للزر المحدد
//     const activeBtn = document.querySelector(`.view-toggle-btn[data-view="${view}"]`);
//     if (activeBtn) {
//         activeBtn.classList.add('active');
//     }
    
//     if (view === 'grid') {
//         taskList.setAttribute('data-view', 'grid');
//         taskList.style.display = 'grid';
//         taskList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(300px, 1fr))';
//         taskList.style.gap = '1rem';
//     } else {
//         taskList.setAttribute('data-view', 'list');
//         taskList.style.display = 'flex';
//         taskList.style.flexDirection = 'column';
//         taskList.style.gap = '0.5rem';
//     }
    
//     // حفظ التفضيل
//     localStorage.setItem('viewMode', view);
    
//     // إشعار
//     const viewNames = {
//         'list': 'عرض القائمة',
//         'grid': 'عرض الشبكة'
//     };
//     showNotification(`تم التبديل إلى ${viewNames[view]}`, 'info');
// }

// تشغيل التهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', finalizeInitialization);

// ===== واجهة البحث الجديدة =====

// فتح واجهة البحث
function openSearchInterface() {
    const searchInterface = document.getElementById('searchInterface');
    searchInterface.classList.add('show');
    document.body.style.overflow = 'hidden';
    
    // التركيز على البحث السريع افتراضياً
    setTimeout(() => {
        const quickSearchInput = document.getElementById('quickSearchInput');
        if (quickSearchInput) {
            quickSearchInput.focus();
        }
    }, 300);
}

// إغلاق واجهة البحث
function closeSearchInterface() {
    const searchInterface = document.getElementById('searchInterface');
    searchInterface.classList.remove('show');
    document.body.style.overflow = '';
    
    // مسح النتائج
    clearQuickSearch();
    clearAdvancedSearch();
}

// تبديل نوع البحث
function switchSearchType(type) {
    console.log('Switching to:', type); // للتأكد من عمل الدالة
    
    // إزالة الفئة النشطة من جميع الأزرار
    document.querySelectorAll('.search-type-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // إضافة الفئة النشطة للزر المحدد
    const targetBtn = document.querySelector(`[data-type="${type}"]`);
    if (targetBtn) {
        targetBtn.classList.add('active');
    }
    
    // إخفاء جميع المحتويات
    document.querySelectorAll('.search-content').forEach(content => {
        content.classList.remove('active');
        content.style.display = 'none';
    });
    
    // إظهار المحتوى المحدد
    if (type === 'quick') {
        const quickContent = document.getElementById('quickSearchContent');
        if (quickContent) {
            quickContent.classList.add('active');
            quickContent.style.display = 'block';
            setTimeout(() => {
                const input = document.getElementById('quickSearchInput');
                if (input) input.focus();
            }, 100);
        }
    } else if (type === 'advanced') {
        const advancedContent = document.getElementById('advancedSearchContent');
        if (advancedContent) {
            advancedContent.classList.add('active');
            advancedContent.style.display = 'block';
            setTimeout(() => {
                const input = document.getElementById('advancedSearchInput');
                if (input) input.focus();
            }, 100);
        }
    }
}

// مسح البحث السريع
function clearQuickSearch() {
    const quickSearchInput = document.getElementById('quickSearchInput');
    const quickResultsList = document.getElementById('quickResultsList');
    const quickResultsCount = document.getElementById('quickResultsCount');
    
    quickSearchInput.value = '';
    quickResultsList.innerHTML = '';
    quickResultsCount.textContent = '0 نتيجة';
    
    // إخفاء النتائج
    document.getElementById('quickSearchResults').style.display = 'none';
}

// مسح البحث المتقدم
function clearAdvancedSearch() {
    const advancedSearchInput = document.getElementById('advancedSearchInput');
    const advancedResultsList = document.getElementById('advancedResultsList');
    const advancedResultsCount = document.getElementById('advancedResultsCount');
    
    advancedSearchInput.value = '';
    advancedResultsList.innerHTML = '';
    advancedResultsCount.textContent = '0 نتيجة';
    
    // إعادة تعيين الفلاتر
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector('[data-filter="all"]').classList.add('active');
    
    // إعادة تعيين الخيارات
    document.getElementById('searchCompleted').checked = false;
    document.getElementById('searchPending').checked = true;
    document.getElementById('exactMatch').checked = false;
}

// البحث السريع
function performQuickSearch(query) {
    if (!query.trim()) {
        clearQuickSearch();
        return;
    }
    
    const tasks = JSON.parse(localStorage.getItem('tasks')) || [];
    const results = tasks.filter(task => {
        const taskText = task.text.toLowerCase();
        const searchQuery = query.toLowerCase();
        return taskText.includes(searchQuery);
    });
    
    displayQuickSearchResults(results, query);
}

// عرض نتائج البحث السريع
function displayQuickSearchResults(results, query) {
    const quickResultsList = document.getElementById('quickResultsList');
    const quickResultsCount = document.getElementById('quickResultsCount');
    const quickSearchResults = document.getElementById('quickSearchResults');
    
    quickResultsCount.textContent = `${results.length} نتيجة`;
    
    if (results.length === 0) {
        quickResultsList.innerHTML = `
            <div class="no-results">
                <i class="fas fa-search"></i>
                <p>لا توجد نتائج للبحث: "${query}"</p>
                <small>جرب كلمات مختلفة أو تحقق من الإملاء</small>
            </div>
        `;
    } else {
        quickResultsList.innerHTML = results.map((task, index) => {
            const highlightedText = task.text.replace(
                new RegExp(query, 'gi'),
                match => `<mark>${match}</mark>`
            );
            
            return `
                <div class="quick-result-item" data-task-id="${task.id}">
                    <div class="quick-result-content">
                        <div class="quick-result-number">${index + 1}</div>
                        <div class="quick-result-text">${highlightedText}</div>
                    </div>
                    <div class="quick-result-actions">
                        <button class="quick-result-btn complete" onclick="completeTaskFromSearch('${task.id}')" title="إكمال المهمة">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="quick-result-btn edit" onclick="editTaskFromSearch('${task.id}')" title="تعديل المهمة">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="quick-result-btn delete" onclick="deleteTaskFromSearch('${task.id}')" title="حذف المهمة">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        }).join('');
        
        // إضافة تأثير ظهور تدريجي للنتائج
        const resultItems = quickResultsList.querySelectorAll('.quick-result-item');
        resultItems.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';
            setTimeout(() => {
                item.style.transition = 'all 0.3s ease';
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, index * 50);
        });
    }
    
    quickSearchResults.style.display = 'block';
}

// البحث المتقدم
function performAdvancedSearch(query) {
    if (!query.trim()) {
        clearAdvancedSearch();
        return;
    }
    
    const tasks = JSON.parse(localStorage.getItem('tasks')) || [];
    const searchCompleted = document.getElementById('searchCompleted').checked;
    const searchPending = document.getElementById('searchPending').checked;
    const exactMatch = document.getElementById('exactMatch').checked;
    const activeFilter = document.querySelector('.filter-btn.active').dataset.filter;
    
    let results = tasks.filter(task => {
        // فلترة حسب حالة المهمة
        if (task.completed && !searchCompleted) return false;
        if (!task.completed && !searchPending) return false;
        
        // البحث حسب النوع المحدد
        let searchText = '';
        switch (activeFilter) {
            case 'title':
                searchText = task.text;
                break;
            case 'category':
                searchText = task.category || '';
                break;
            case 'priority':
                searchText = task.priority || '';
                break;
            default:
                searchText = `${task.text} ${task.category || ''} ${task.priority || ''}`;
        }
        
        const searchQuery = query.toLowerCase();
        const taskText = searchText.toLowerCase();
        
        if (exactMatch) {
            return taskText === searchQuery;
        } else {
            return taskText.includes(searchQuery);
        }
    });
    
    displayAdvancedSearchResults(results, query);
}

// عرض نتائج البحث المتقدم
function displayAdvancedSearchResults(results, query) {
    const advancedResultsList = document.getElementById('advancedResultsList');
    const advancedResultsCount = document.getElementById('advancedResultsCount');
    
    advancedResultsCount.textContent = `${results.length} نتيجة`;
    
    if (results.length === 0) {
        advancedResultsList.innerHTML = `
            <div class="no-results">
                <i class="fas fa-search"></i>
                <p>لا توجد نتائج للبحث: "${query}"</p>
                <small>جرب كلمات مختلفة أو تحقق من الإملاء</small>
            </div>
        `;
    } else {
        advancedResultsList.innerHTML = results.map((task, index) => {
            const highlightedText = task.text.replace(
                new RegExp(query, 'gi'),
                match => `<mark>${match}</mark>`
            );
            
            return `
                <div class="search-result-item ${task.completed ? 'completed' : ''}" data-task-id="${task.id}">
                    <div class="result-content">
                        <div class="result-text">
                            <div class="result-number">${index + 1}</div>
                            <div class="result-title">${highlightedText}</div>
                        </div>
                        <div class="result-meta">
                            ${task.category ? `<div class="result-category"><i class="fas fa-tag"></i>${task.category}</div>` : ''}
                            ${task.priority ? `<div class="result-priority ${task.priority}"><i class="fas fa-exclamation-triangle"></i>${task.priority}</div>` : ''}
                            <div class="result-status">
                                <i class="fas fa-${task.completed ? 'check-circle' : 'clock'}"></i>
                                ${task.completed ? 'مكتملة' : 'معلقة'}
                            </div>
                        </div>
                    </div>
                    <div class="result-actions">
                        <button class="result-action-btn complete" onclick="completeTaskFromSearch('${task.id}')" title="إكمال المهمة">
                            <i class="fas fa-check"></i>
                            إكمال
                        </button>
                        <button class="result-action-btn edit" onclick="editTaskFromSearch('${task.id}')" title="تعديل المهمة">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </button>
                        <button class="result-action-btn delete" onclick="deleteTaskFromSearch('${task.id}')" title="حذف المهمة">
                            <i class="fas fa-trash"></i>
                            حذف
                        </button>
                    </div>
                </div>
            `;
        }).join('');
        
        // إضافة تأثير ظهور تدريجي للنتائج
        const resultItems = advancedResultsList.querySelectorAll('.search-result-item');
        resultItems.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';
            setTimeout(() => {
                item.style.transition = 'all 0.3s ease';
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, index * 50);
        });
    }
}

// إكمال مهمة من البحث
function completeTaskFromSearch(taskId) {
    console.log('Completing task:', taskId); // للتأكد من عمل الدالة
    
    const tasks = JSON.parse(localStorage.getItem('tasks')) || [];
    const taskIndex = tasks.findIndex(task => task.id === taskId);
    
    if (taskIndex !== -1) {
        tasks[taskIndex].completed = !tasks[taskIndex].completed;
        localStorage.setItem('tasks', JSON.stringify(tasks));
        
        // تحديث الواجهة
        renderTasks();
        updateStatsBar();
        
        // إعادة البحث لعرض النتائج المحدثة
        const quickSearchInput = document.getElementById('quickSearchInput');
        const advancedSearchInput = document.getElementById('advancedSearchInput');
        
        if (quickSearchInput && quickSearchInput.value.trim()) {
            performQuickSearch(quickSearchInput.value);
        }
        if (advancedSearchInput && advancedSearchInput.value.trim()) {
            performAdvancedSearch(advancedSearchInput.value);
        }
        
        showNotification('تم تحديث حالة المهمة بنجاح', 'success');
    } else {
        showNotification('لم يتم العثور على المهمة', 'error');
    }
}

// تعديل مهمة من البحث
function editTaskFromSearch(taskId) {
    console.log('Editing task:', taskId); // للتأكد من عمل الدالة
    
    const tasks = JSON.parse(localStorage.getItem('tasks')) || [];
    const task = tasks.find(task => task.id === taskId);
    
    if (task) {
        // إغلاق واجهة البحث
        closeSearchInterface();
        
        // البحث عن المهمة في الواجهة وتفعيل التعديل
        const taskElement = document.querySelector(`[data-task-id="${taskId}"]`);
        if (taskElement) {
            const taskTitle = taskElement.querySelector('.task-title');
            if (taskTitle) {
                taskTitle.click(); // تفعيل التعديل
            } else {
                // إذا لم نجد العنوان، نبحث عن النص
                const taskText = taskElement.querySelector('.task-text');
                if (taskText) {
                    taskText.click();
                }
            }
        }
        
        showNotification('يمكنك الآن تعديل المهمة', 'info');
    } else {
        showNotification('لم يتم العثور على المهمة', 'error');
    }
}

// حذف مهمة من البحث
function deleteTaskFromSearch(taskId) {
    console.log('Deleting task:', taskId); // للتأكد من عمل الدالة
    
    if (confirm('هل أنت متأكد من حذف هذه المهمة؟')) {
        const tasks = JSON.parse(localStorage.getItem('tasks')) || [];
        const filteredTasks = tasks.filter(task => task.id !== taskId);
        localStorage.setItem('tasks', JSON.stringify(filteredTasks));
        
        // تحديث الواجهة
        renderTasks();
        updateStatsBar();
        
        // إعادة البحث لعرض النتائج المحدثة
        const quickSearchInput = document.getElementById('quickSearchInput');
        const advancedSearchInput = document.getElementById('advancedSearchInput');
        
        if (quickSearchInput && quickSearchInput.value.trim()) {
            performQuickSearch(quickSearchInput.value);
        }
        if (advancedSearchInput && advancedSearchInput.value.trim()) {
            performAdvancedSearch(advancedSearchInput.value);
        }
        
        showNotification('تم حذف المهمة بنجاح', 'success');
    }
}

// تصدير نتائج البحث
function exportSearchResults() {
    const quickSearchInput = document.getElementById('quickSearchInput');
    const advancedSearchInput = document.getElementById('advancedSearchInput');
    
    let query = '';
    let results = [];
    
    if (quickSearchInput.value.trim()) {
        query = quickSearchInput.value;
        const tasks = JSON.parse(localStorage.getItem('tasks')) || [];
        results = tasks.filter(task => 
            task.text.toLowerCase().includes(query.toLowerCase())
        );
    } else if (advancedSearchInput.value.trim()) {
        query = advancedSearchInput.value;
        // نفس منطق البحث المتقدم
        const tasks = JSON.parse(localStorage.getItem('tasks')) || [];
        const searchCompleted = document.getElementById('searchCompleted').checked;
        const searchPending = document.getElementById('searchPending').checked;
        const exactMatch = document.getElementById('exactMatch').checked;
        const activeFilter = document.querySelector('.filter-btn.active').dataset.filter;
        
        results = tasks.filter(task => {
            if (task.completed && !searchCompleted) return false;
            if (!task.completed && !searchPending) return false;
            
            let searchText = '';
            switch (activeFilter) {
                case 'title':
                    searchText = task.text;
                    break;
                case 'category':
                    searchText = task.category || '';
                    break;
                case 'priority':
                    searchText = task.priority || '';
                    break;
                default:
                    searchText = `${task.text} ${task.category || ''} ${task.priority || ''}`;
            }
            
            const searchQuery = query.toLowerCase();
            const taskText = searchText.toLowerCase();
            
            if (exactMatch) {
                return taskText === searchQuery;
            } else {
                return taskText.includes(searchQuery);
            }
        });
    }
    
    if (results.length === 0) {
        showNotification('لا توجد نتائج للتصدير', 'warning');
        return;
    }
    
    // إنشاء ملف CSV
    const csvContent = [
        ['النص', 'التصنيف', 'الأولوية', 'الحالة', 'التاريخ'],
        ...results.map(task => [
            task.text,
            task.category || '',
            task.priority || '',
            task.completed ? 'مكتملة' : 'معلقة',
            task.date || ''
        ])
    ].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `نتائج_البحث_${query}_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showNotification('تم تصدير النتائج بنجاح', 'success');
}

// إعداد أحداث البحث
function setupSearchInterface() {
    console.log('Setting up search interface...'); // للتأكد من عمل الدالة
    
    // إعداد أحداث أزرار نوع البحث
    const quickSearchBtn = document.getElementById('quickSearchBtn');
    const advancedSearchBtn = document.getElementById('advancedSearchBtn');
    
    if (quickSearchBtn) {
        quickSearchBtn.addEventListener('click', () => {
            console.log('Quick search button clicked');
            switchSearchType('quick');
        });
    }
    
    if (advancedSearchBtn) {
        advancedSearchBtn.addEventListener('click', () => {
            console.log('Advanced search button clicked');
            switchSearchType('advanced');
        });
    }
    
    // البحث السريع
    const quickSearchInput = document.getElementById('quickSearchInput');
    if (quickSearchInput) {
        // البحث عند الكتابة
        quickSearchInput.addEventListener('input', debounce((e) => {
            performQuickSearch(e.target.value);
        }, 300));
        
        // البحث عند الضغط على Enter
        quickSearchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performQuickSearch(e.target.value);
            }
        });
        
        // البحث عند النقر خارج الصندوق
        quickSearchInput.addEventListener('blur', (e) => {
            setTimeout(() => {
                if (e.target.value.trim()) {
                    performQuickSearch(e.target.value);
                }
            }, 200);
        });
    }
    
    // زر مسح البحث السريع
    const clearQuickSearchBtn = document.getElementById('clearQuickSearchBtn');
    if (clearQuickSearchBtn) {
        clearQuickSearchBtn.addEventListener('click', () => {
            console.log('Clear quick search button clicked');
            clearQuickSearch();
        });
    }
    
    // البحث المتقدم
    const advancedSearchInput = document.getElementById('advancedSearchInput');
    if (advancedSearchInput) {
        // البحث عند الكتابة
        advancedSearchInput.addEventListener('input', debounce((e) => {
            performAdvancedSearch(e.target.value);
        }, 300));
        
        // البحث عند الضغط على Enter
        advancedSearchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performAdvancedSearch(e.target.value);
            }
        });
    }
    
    // زر مسح البحث المتقدم
    const clearAdvancedSearchBtn = document.getElementById('clearAdvancedSearchBtn');
    if (clearAdvancedSearchBtn) {
        clearAdvancedSearchBtn.addEventListener('click', () => {
            console.log('Clear advanced search button clicked');
            clearAdvancedSearch();
        });
    }
    
    // أحداث الفلاتر في البحث المتقدم
    document.querySelectorAll('#advancedSearchContent .filter-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            document.querySelectorAll('#advancedSearchContent .filter-btn').forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            
            const advancedSearchInput = document.getElementById('advancedSearchInput');
            if (advancedSearchInput && advancedSearchInput.value.trim()) {
                performAdvancedSearch(advancedSearchInput.value);
            }
        });
    });
    
    // أحداث الخيارات في البحث المتقدم
    document.querySelectorAll('#advancedSearchContent .search-option').forEach(option => {
        option.addEventListener('change', () => {
            const advancedSearchInput = document.getElementById('advancedSearchInput');
            if (advancedSearchInput && advancedSearchInput.value.trim()) {
                performAdvancedSearch(advancedSearchInput.value);
            }
        });
    });
    
    // إغلاق واجهة البحث عند النقر على زر الإغلاق
    const closeSearchBtn = document.getElementById('closeSearchInterfaceBtn');
    if (closeSearchBtn) {
        closeSearchBtn.addEventListener('click', () => {
            console.log('Close search interface button clicked');
            closeSearchInterface();
        });
    }
    
    // إغلاق واجهة البحث عند النقر خارجها
    document.getElementById('searchInterface').addEventListener('click', (e) => {
        if (e.target.id === 'searchInterface') {
            closeSearchInterface();
        }
    });
    
    // إغلاق واجهة البحث عند الضغط على Escape
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            closeSearchInterface();
        }
    });
    
    // زر تصدير نتائج البحث
    const exportSearchResultsBtn = document.getElementById('exportSearchResultsBtn');
    if (exportSearchResultsBtn) {
        exportSearchResultsBtn.addEventListener('click', () => {
            console.log('Export search results button clicked');
            exportSearchResults();
        });
    }
    
    // تحسين التفاعل مع أزرار البحث
    setupSearchButtonInteractions();
}

// تحسين التفاعل مع أزرار البحث
function setupSearchButtonInteractions() {
    console.log('Setting up search button interactions...'); // للتأكد من عمل الدالة
    
    // أزرار البحث السريع
    document.addEventListener('click', (e) => {
        if (e.target.closest('.quick-result-btn')) {
            const btn = e.target.closest('.quick-result-btn');
            const taskId = btn.closest('.quick-result-item').dataset.taskId;
            
            console.log('Quick result button clicked:', btn.className, 'Task ID:', taskId);
            
            if (btn.classList.contains('complete')) {
                completeTaskFromSearch(taskId);
            } else if (btn.classList.contains('edit')) {
                editTaskFromSearch(taskId);
            } else if (btn.classList.contains('delete')) {
                deleteTaskFromSearch(taskId);
            }
        }
        
        // أزرار البحث المتقدم
        if (e.target.closest('.result-action-btn')) {
            const btn = e.target.closest('.result-action-btn');
            const taskId = btn.closest('.search-result-item').dataset.taskId;
            
            console.log('Advanced result button clicked:', btn.className, 'Task ID:', taskId);
            
            if (btn.classList.contains('complete')) {
                completeTaskFromSearch(taskId);
            } else if (btn.classList.contains('edit')) {
                editTaskFromSearch(taskId);
            } else if (btn.classList.contains('delete')) {
                deleteTaskFromSearch(taskId);
            }
        }
    });
    
    // تحسين التنقل في البحث
    setupSearchNavigation();
    
    // تحسين التفاعل مع الفلاتر
    setupSearchFilters();
}

// تحسين التنقل في البحث
function setupSearchNavigation() {
    // التنقل بالكيبورد في البحث السريع
    const quickSearchInput = document.getElementById('quickSearchInput');
    if (quickSearchInput) {
        quickSearchInput.addEventListener('keydown', (e) => {
            const results = document.querySelectorAll('.quick-result-item');
            const currentIndex = Array.from(results).findIndex(item => item.classList.contains('selected'));
            
            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    navigateQuickResults(currentIndex, 1, results);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    navigateQuickResults(currentIndex, -1, results);
                    break;
                case 'Enter':
                    if (currentIndex >= 0) {
                        e.preventDefault();
                        const selectedItem = results[currentIndex];
                        const taskId = selectedItem.dataset.taskId;
                        // يمكن إضافة إجراء هنا مثل فتح المهمة
                    }
                    break;
            }
        });
    }
    
    // التنقل بالكيبورد في البحث المتقدم
    const advancedSearchInput = document.getElementById('advancedSearchInput');
    if (advancedSearchInput) {
        advancedSearchInput.addEventListener('keydown', (e) => {
            const results = document.querySelectorAll('.search-result-item');
            const currentIndex = Array.from(results).findIndex(item => item.classList.contains('selected'));
            
            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    navigateAdvancedResults(currentIndex, 1, results);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    navigateAdvancedResults(currentIndex, -1, results);
                    break;
                case 'Enter':
                    if (currentIndex >= 0) {
                        e.preventDefault();
                        const selectedItem = results[currentIndex];
                        const taskId = selectedItem.dataset.taskId;
                        // يمكن إضافة إجراء هنا
                    }
                    break;
            }
        });
    }
}

// التنقل في نتائج البحث السريع
function navigateQuickResults(currentIndex, direction, results) {
    if (results.length === 0) return;
    
    // إزالة التحديد الحالي
    if (currentIndex >= 0) {
        results[currentIndex].classList.remove('selected');
    }
    
    // تحديد العنصر الجديد
    let newIndex;
    if (direction > 0) {
        newIndex = currentIndex < results.length - 1 ? currentIndex + 1 : 0;
    } else {
        newIndex = currentIndex > 0 ? currentIndex - 1 : results.length - 1;
    }
    
    results[newIndex].classList.add('selected');
    results[newIndex].scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

// التنقل في نتائج البحث المتقدم
function navigateAdvancedResults(currentIndex, direction, results) {
    if (results.length === 0) return;
    
    // إزالة التحديد الحالي
    if (currentIndex >= 0) {
        results[currentIndex].classList.remove('selected');
    }
    
    // تحديد العنصر الجديد
    let newIndex;
    if (direction > 0) {
        newIndex = currentIndex < results.length - 1 ? currentIndex + 1 : 0;
    } else {
        newIndex = currentIndex > 0 ? currentIndex - 1 : results.length - 1;
    }
    
    results[newIndex].classList.add('selected');
    results[newIndex].scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

// تحسين التفاعل مع الفلاتر
function setupSearchFilters() {
    // تحسين أزرار الفلترة في البحث المتقدم
    document.querySelectorAll('#advancedSearchContent .filter-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            
            // إزالة التحديد من جميع الأزرار
            document.querySelectorAll('#advancedSearchContent .filter-btn').forEach(b => {
                b.classList.remove('active');
                b.style.transform = 'scale(1)';
            });
            
            // تحديد الزر المختار
            btn.classList.add('active');
            btn.style.transform = 'scale(1.05)';
            
            // إعادة البحث
            const advancedSearchInput = document.getElementById('advancedSearchInput');
            if (advancedSearchInput && advancedSearchInput.value.trim()) {
                performAdvancedSearch(advancedSearchInput.value);
            }
            
            // إعادة تعيين التحويل بعد فترة
            setTimeout(() => {
                btn.style.transform = 'scale(1)';
            }, 200);
        });
    });
    
    // تحسين خيارات البحث
    document.querySelectorAll('#advancedSearchContent .search-option input').forEach(option => {
        option.addEventListener('change', (e) => {
            // تأثير بصري عند التغيير
            const label = e.target.closest('.search-option');
            label.style.transform = 'scale(1.02)';
            setTimeout(() => {
                label.style.transform = 'scale(1)';
            }, 150);
            
            // إعادة البحث
            const advancedSearchInput = document.getElementById('advancedSearchInput');
            if (advancedSearchInput && advancedSearchInput.value.trim()) {
                performAdvancedSearch(advancedSearchInput.value);
            }
        });
    });
}
