<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4a6cf7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2541b2;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="10" flood-color="rgba(0,0,0,0.3)" />
    </filter>
  </defs>
  
  <!-- Background -->
  <rect x="10" y="10" rx="80" ry="80" width="492" height="492" fill="url(#grad1)" filter="url(#shadow)" />
  
  <!-- Checkmark and list lines -->
  <g fill="#ffffff" transform="translate(90, 90) scale(0.65)">
    <!-- Checkmark circle -->
    <circle cx="180" cy="180" r="120" fill="rgba(255,255,255,0.15)" stroke="#ffffff" stroke-width="20" />
    
    <!-- Checkmark -->
    <path d="M130,180 L170,220 L240,140" fill="none" stroke="#ffffff" stroke-width="25" stroke-linecap="round" stroke-linejoin="round" />
    
    <!-- List lines -->
    <rect x="300" y="120" width="150" height="20" rx="10" ry="10" />
    <rect x="300" y="180" width="150" height="20" rx="10" ry="10" />
    <rect x="300" y="240" width="150" height="20" rx="10" ry="10" />
  </g>
  
  <!-- Arabic text for TDL -->
  <text x="256" y="400" font-family="Arial, sans-serif" font-size="60" font-weight="bold" text-anchor="middle" fill="#ffffff" direction="rtl" xml:lang="ar">قائمة المهام</text>
</svg>