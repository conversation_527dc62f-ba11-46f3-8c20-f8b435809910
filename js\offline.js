// TDL V1.0 - Offline Functionality Management

export class OfflineManager {
  constructor() {
    this.isOnline = navigator.onLine;
    this.pendingActions = [];
    this.syncInProgress = false;
    
    // تحميل الإجراءات المعلقة من التخزين المحلي
    this.loadPendingActions();
    
    // إعداد مستمعي الأحداث للاتصال بالإنترنت
    this.setupEventListeners();
  }
  
  // إعداد مستمعي الأحداث
  setupEventListeners() {
    // مراقبة حالة الاتصال بالإنترنت
    window.addEventListener('online', () => this.handleOnlineStatus(true));
    window.addEventListener('offline', () => this.handleOnlineStatus(false));
    
    // محاولة المزامنة عند تحميل الصفحة إذا كان هناك إجراءات معلقة
    window.addEventListener('load', () => {
      if (this.isOnline && this.pendingActions.length > 0) {
        this.syncPendingActions();
      }
    });
  }
  
  // التعامل مع تغيير حالة الاتصال
  handleOnlineStatus(isOnline) {
    this.isOnline = isOnline;
    
    if (isOnline) {
      console.log('متصل بالإنترنت الآن');
      this.showConnectivityNotification(true);
      
      // محاولة مزامنة الإجراءات المعلقة
      if (this.pendingActions.length > 0) {
        this.syncPendingActions();
      }
    } else {
      console.log('غير متصل بالإنترنت');
      this.showConnectivityNotification(false);
    }
  }
  
  // إظهار إشعار بحالة الاتصال
  showConnectivityNotification(isOnline) {
    if (typeof showNotification === 'function') {
      if (isOnline) {
        showNotification(
          'متصل بالإنترنت', 
          'تم استعادة الاتصال بالإنترنت. سيتم مزامنة التغييرات المعلقة.', 
          'success', 
          true, 
          3000
        );
      } else {
        showNotification(
          'غير متصل بالإنترنت', 
          'أنت الآن في وضع عدم الاتصال. ستتم مزامنة التغييرات عند استعادة الاتصال.', 
          'warning', 
          true, 
          5000
        );
      }
    }
  }
  
  // إضافة إجراء معلق للمزامنة لاحقاً
  addPendingAction(action) {
    // إضافة طابع زمني للإجراء
    action.timestamp = new Date().getTime();
    
    // إضافة الإجراء إلى القائمة
    this.pendingActions.push(action);
    
    // حفظ الإجراءات المعلقة
    this.savePendingActions();
    
    console.log('تمت إضافة إجراء معلق:', action);
    
    // محاولة المزامنة إذا كان متصلاً بالإنترنت
    if (this.isOnline) {
      this.syncPendingActions();
    }
    
    return action;
  }
  
  // حفظ الإجراءات المعلقة في التخزين المحلي
  savePendingActions() {
    try {
      localStorage.setItem('pendingActions', JSON.stringify(this.pendingActions));
    } catch (error) {
      console.error('خطأ في حفظ الإجراءات المعلقة:', error);
    }
  }
  
  // تحميل الإجراءات المعلقة من التخزين المحلي
  loadPendingActions() {
    try {
      const savedActions = localStorage.getItem('pendingActions');
      this.pendingActions = savedActions ? JSON.parse(savedActions) : [];
      console.log(`تم تحميل ${this.pendingActions.length} إجراء معلق`);
    } catch (error) {
      console.error('خطأ في تحميل الإجراءات المعلقة:', error);
      this.pendingActions = [];
    }
  }
  
  // مزامنة الإجراءات المعلقة مع الخادم
  async syncPendingActions() {
    // تجنب تشغيل عمليات مزامنة متعددة في نفس الوقت
    if (this.syncInProgress || !this.isOnline || this.pendingActions.length === 0) {
      return;
    }
    
    this.syncInProgress = true;
    console.log('بدء مزامنة الإجراءات المعلقة...');
    
    // في هذه النسخة، نحن نستخدم التخزين المحلي فقط، لذا سنقوم بمحاكاة المزامنة
    // في تطبيق حقيقي، هنا ستكون عملية إرسال البيانات إلى الخادم
    
    try {
      // ترتيب الإجراءات حسب الطابع الزمني
      this.pendingActions.sort((a, b) => a.timestamp - b.timestamp);
      
      // معالجة كل إجراء معلق
      const actionsToRemove = [];
      
      for (const action of this.pendingActions) {
        // محاكاة معالجة الإجراء (في تطبيق حقيقي، هنا سيكون إرسال الإجراء إلى الخادم)
        await this.processPendingAction(action);
        
        // إضافة الإجراء إلى قائمة الإجراءات التي سيتم إزالتها
        actionsToRemove.push(action);
      }
      
      // إزالة الإجراءات التي تمت معالجتها
      this.pendingActions = this.pendingActions.filter(action => 
        !actionsToRemove.some(a => a.id === action.id)
      );
      
      // حفظ الإجراءات المعلقة المتبقية
      this.savePendingActions();
      
      console.log('تمت مزامنة الإجراءات المعلقة بنجاح');
      
      // إظهار إشعار بنجاح المزامنة إذا كانت هناك إجراءات تمت معالجتها
      if (actionsToRemove.length > 0 && typeof showNotification === 'function') {
        showNotification(
          'تمت المزامنة', 
          `تمت مزامنة ${actionsToRemove.length} من التغييرات بنجاح.`, 
          'success', 
          true, 
          3000
        );
      }
    } catch (error) {
      console.error('خطأ في مزامنة الإجراءات المعلقة:', error);
      
      // إظهار إشعار بفشل المزامنة
      if (typeof showNotification === 'function') {
        showNotification(
          'فشل المزامنة', 
          'حدث خطأ أثناء مزامنة التغييرات. سيتم إعادة المحاولة لاحقاً.', 
          'error', 
          true, 
          5000
        );
      }
    } finally {
      this.syncInProgress = false;
    }
  }
  
  // معالجة إجراء معلق واحد
  async processPendingAction(action) {
    return new Promise((resolve) => {
      // محاكاة تأخير الشبكة
      setTimeout(() => {
        console.log('تمت معالجة الإجراء:', action);
        resolve(true);
      }, 300);
    });
  }
  
  // إنشاء معرف فريد للإجراء
  generateActionId() {
    return `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  // إضافة مهمة في وضع عدم الاتصال
  addTaskOffline(task) {
    // إضافة المهمة إلى التخزين المحلي مباشرة
    const tasks = this.getTasksFromLocalStorage();
    tasks.push(task);
    this.saveTasksToLocalStorage(tasks);
    
    // إضافة إجراء معلق للمزامنة لاحقاً
    this.addPendingAction({
      id: this.generateActionId(),
      type: 'ADD_TASK',
      data: task
    });
    
    return task;
  }
  
  // تحديث مهمة في وضع عدم الاتصال
  updateTaskOffline(taskId, updates) {
    // تحديث المهمة في التخزين المحلي مباشرة
    const tasks = this.getTasksFromLocalStorage();
    const taskIndex = tasks.findIndex(t => t.id === taskId);
    
    if (taskIndex !== -1) {
      tasks[taskIndex] = { ...tasks[taskIndex], ...updates };
      this.saveTasksToLocalStorage(tasks);
      
      // إضافة إجراء معلق للمزامنة لاحقاً
      this.addPendingAction({
        id: this.generateActionId(),
        type: 'UPDATE_TASK',
        data: {
          taskId,
          updates
        }
      });
      
      return tasks[taskIndex];
    }
    
    return null;
  }
  
  // حذف مهمة في وضع عدم الاتصال
  deleteTaskOffline(taskId) {
    // حذف المهمة من التخزين المحلي مباشرة
    const tasks = this.getTasksFromLocalStorage();
    const updatedTasks = tasks.filter(t => t.id !== taskId);
    this.saveTasksToLocalStorage(updatedTasks);
    
    // إضافة إجراء معلق للمزامنة لاحقاً
    this.addPendingAction({
      id: this.generateActionId(),
      type: 'DELETE_TASK',
      data: {
        taskId
      }
    });
    
    return true;
  }
  
  // الحصول على المهام من التخزين المحلي
  getTasksFromLocalStorage() {
    try {
      const tasksJson = localStorage.getItem('tasks');
      return tasksJson ? JSON.parse(tasksJson) : [];
    } catch (error) {
      console.error('خطأ في قراءة المهام من التخزين المحلي:', error);
      return [];
    }
  }
  
  // حفظ المهام في التخزين المحلي
  saveTasksToLocalStorage(tasks) {
    try {
      localStorage.setItem('tasks', JSON.stringify(tasks));
    } catch (error) {
      console.error('خطأ في حفظ المهام في التخزين المحلي:', error);
    }
  }
}

// إنشاء نسخة واحدة من مدير وضع عدم الاتصال
const offlineManager = new OfflineManager();

// تصدير الكائن للاستخدام في الملفات الأخرى
window.offlineManager = offlineManager;