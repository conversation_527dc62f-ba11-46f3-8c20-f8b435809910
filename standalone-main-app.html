<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TDL - مدير المهام المتقدم</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap');

        :root {
            --primary: #6366f1;
            --secondary: #8b5cf6;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --info: #06b6d4;
            --dark: #1a1a2e;
            --darker: #0f0f23;
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.1);
            --text-primary: #e0e0e0;
            --text-secondary: #a0a0a0;
            --text-muted: #6b7280;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--darker) 0%, var(--dark) 50%, #16213e 100%);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .app-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .app-header {
            text-align: center;
            margin-bottom: 3rem;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 2rem;
        }

        .app-header h1 {
            font-size: 3rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary), var(--info));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .user-welcome {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 1.2rem;
        }

        .user-details h3 {
            color: var(--text-primary);
            margin-bottom: 0.3rem;
        }

        .user-details p {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .user-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn.primary {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
        }

        .btn.danger {
            background: linear-gradient(135deg, var(--danger), #dc2626);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 2rem;
        }

        .tasks-section {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 2rem;
        }

        .sidebar {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 2rem;
            height: fit-content;
        }

        .section-title {
            color: var(--primary);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.3rem;
        }

        .task-input {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .task-input input {
            flex: 1;
            padding: 1rem;
            border: 2px solid var(--glass-border);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-primary);
            font-size: 1rem;
        }

        .task-input input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }

        .task-list {
            list-style: none;
        }

        .task-item {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
        }

        .task-item:hover {
            background: rgba(255, 255, 255, 0.05);
            transform: translateX(5px);
        }

        .task-item.completed {
            opacity: 0.6;
            text-decoration: line-through;
        }

        .task-checkbox {
            width: 20px;
            height: 20px;
            border: 2px solid var(--primary);
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .task-checkbox.checked {
            background: var(--primary);
            color: white;
        }

        .task-text {
            flex: 1;
            color: var(--text-primary);
        }

        .task-actions {
            display: flex;
            gap: 0.5rem;
        }

        .task-btn {
            padding: 0.5rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .task-btn.edit {
            background: var(--info);
            color: white;
        }

        .task-btn.delete {
            background: var(--danger);
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.3rem;
        }

        .stat-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .alert {
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            border-left: 4px solid;
        }

        .alert.success {
            background: rgba(16, 185, 129, 0.1);
            border-color: var(--success);
            color: var(--success);
        }

        .alert.error {
            background: rgba(239, 68, 68, 0.1);
            border-color: var(--danger);
            color: var(--danger);
        }

        .alert.info {
            background: rgba(6, 182, 212, 0.1);
            border-color: var(--info);
            color: var(--info);
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .user-welcome {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .task-input {
                flex-direction: column;
            }

            .app-header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- رأس التطبيق -->
        <div class="app-header">
            <h1><i class="fas fa-tasks"></i> TDL</h1>
            <p>مدير المهام المتقدم</p>
        </div>

        <!-- ترحيب المستخدم -->
        <div class="user-welcome">
            <div class="user-info">
                <div class="user-avatar" id="userAvatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <h3 id="userName">مرحباً بك!</h3>
                    <p id="userEmail">يرجى تسجيل الدخول</p>
                </div>
            </div>
            <div class="user-actions">
                <button class="btn primary" onclick="goToAuth()">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
                <button class="btn danger" onclick="logout()" style="display: none;" id="logoutBtn">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="main-content">
            <!-- قسم المهام -->
            <div class="tasks-section">
                <h2 class="section-title">
                    <i class="fas fa-list-check"></i>
                    مهامي
                </h2>

                <!-- إدخال مهمة جديدة -->
                <div class="task-input">
                    <input type="text" id="taskInput" placeholder="أضف مهمة جديدة...">
                    <button class="btn primary" onclick="addTask()">
                        <i class="fas fa-plus"></i>
                        إضافة
                    </button>
                </div>

                <!-- قائمة المهام -->
                <ul class="task-list" id="taskList">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </ul>

                <div id="noTasksMessage" class="alert info" style="display: none;">
                    <i class="fas fa-info-circle"></i>
                    لا توجد مهام حالياً. أضف مهمة جديدة للبدء!
                </div>
            </div>

            <!-- الشريط الجانبي -->
            <div class="sidebar">
                <!-- إحصائيات -->
                <h3 class="section-title">
                    <i class="fas fa-chart-bar"></i>
                    الإحصائيات
                </h3>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="totalTasks">0</div>
                        <div class="stat-label">إجمالي المهام</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="completedTasks">0</div>
                        <div class="stat-label">مكتملة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="pendingTasks">0</div>
                        <div class="stat-label">معلقة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="completionRate">0%</div>
                        <div class="stat-label">معدل الإنجاز</div>
                    </div>
                </div>

                <!-- أدوات -->
                <h3 class="section-title">
                    <i class="fas fa-tools"></i>
                    الأدوات
                </h3>
                
                <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                    <button class="btn primary" onclick="clearCompleted()">
                        <i class="fas fa-broom"></i>
                        مسح المكتملة
                    </button>
                    <button class="btn danger" onclick="clearAll()">
                        <i class="fas fa-trash"></i>
                        مسح الكل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // نظام إدارة المهام المدمج
        class TaskManager {
            constructor() {
                this.tasks = this.loadTasks();
                this.currentUser = this.getCurrentUser();
                this.init();
            }

            init() {
                this.updateUserInfo();
                this.renderTasks();
                this.updateStats();
                this.setupEventListeners();
            }

            // تحميل المستخدم الحالي
            getCurrentUser() {
                try {
                    const userData = localStorage.getItem('tdl_current_user');
                    return userData ? JSON.parse(userData) : null;
                } catch (error) {
                    console.error('خطأ في تحميل بيانات المستخدم:', error);
                    return null;
                }
            }

            // تحميل المهام
            loadTasks() {
                try {
                    const tasks = localStorage.getItem('tdl_tasks');
                    return tasks ? JSON.parse(tasks) : [];
                } catch (error) {
                    console.error('خطأ في تحميل المهام:', error);
                    return [];
                }
            }

            // حفظ المهام
            saveTasks() {
                try {
                    localStorage.setItem('tdl_tasks', JSON.stringify(this.tasks));
                    this.updateUserTasks();
                } catch (error) {
                    console.error('خطأ في حفظ المهام:', error);
                }
            }

            // تحديث مهام المستخدم
            updateUserTasks() {
                if (this.currentUser) {
                    try {
                        const users = JSON.parse(localStorage.getItem('tdl_users') || '{}');
                        if (users[this.currentUser.username]) {
                            users[this.currentUser.username].tasks = this.tasks;
                            users[this.currentUser.username].stats = {
                                totalTasks: this.tasks.length,
                                completedTasks: this.tasks.filter(task => task.completed).length,
                                loginCount: users[this.currentUser.username].stats?.loginCount || 0
                            };
                            localStorage.setItem('tdl_users', JSON.stringify(users));
                        }
                    } catch (error) {
                        console.error('خطأ في تحديث بيانات المستخدم:', error);
                    }
                }
            }

            // تحديث معلومات المستخدم
            updateUserInfo() {
                const userNameEl = document.getElementById('userName');
                const userEmailEl = document.getElementById('userEmail');
                const userAvatarEl = document.getElementById('userAvatar');
                const logoutBtn = document.getElementById('logoutBtn');

                if (this.currentUser) {
                    userNameEl.textContent = `مرحباً ${this.currentUser.fullName}!`;
                    userEmailEl.textContent = this.currentUser.email;

                    // عرض الأحرف الأولى من الاسم
                    const initials = this.currentUser.fullName.split(' ').map(name => name[0]).join('').toUpperCase();
                    userAvatarEl.textContent = initials;

                    logoutBtn.style.display = 'flex';
                    document.querySelector('.btn.primary').style.display = 'none';

                    // تحميل مهام المستخدم
                    this.loadUserTasks();
                } else {
                    userNameEl.textContent = 'مرحباً بك!';
                    userEmailEl.textContent = 'يرجى تسجيل الدخول';
                    userAvatarEl.innerHTML = '<i class="fas fa-user"></i>';
                    logoutBtn.style.display = 'none';
                    document.querySelector('.btn.primary').style.display = 'flex';
                }
            }

            // تحميل مهام المستخدم
            loadUserTasks() {
                if (this.currentUser) {
                    try {
                        const users = JSON.parse(localStorage.getItem('tdl_users') || '{}');
                        const user = users[this.currentUser.username];
                        if (user && user.tasks) {
                            this.tasks = user.tasks;
                        }
                    } catch (error) {
                        console.error('خطأ في تحميل مهام المستخدم:', error);
                    }
                }
            }

            // إعداد مستمعي الأحداث
            setupEventListeners() {
                const taskInput = document.getElementById('taskInput');
                taskInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.addTask();
                    }
                });
            }

            // إضافة مهمة جديدة
            addTask() {
                const taskInput = document.getElementById('taskInput');
                const taskText = taskInput.value.trim();

                if (!taskText) {
                    this.showAlert('يرجى إدخال نص المهمة', 'error');
                    return;
                }

                if (!this.currentUser) {
                    this.showAlert('يرجى تسجيل الدخول أولاً', 'error');
                    return;
                }

                const newTask = {
                    id: Date.now(),
                    text: taskText,
                    completed: false,
                    createdAt: new Date().toISOString(),
                    userId: this.currentUser.username
                };

                this.tasks.push(newTask);
                this.saveTasks();
                this.renderTasks();
                this.updateStats();

                taskInput.value = '';
                this.showAlert('تم إضافة المهمة بنجاح!', 'success');
            }

            // تبديل حالة المهمة
            toggleTask(taskId) {
                const task = this.tasks.find(t => t.id === taskId);
                if (task) {
                    task.completed = !task.completed;
                    task.completedAt = task.completed ? new Date().toISOString() : null;
                    this.saveTasks();
                    this.renderTasks();
                    this.updateStats();
                }
            }

            // حذف مهمة
            deleteTask(taskId) {
                if (confirm('هل أنت متأكد من حذف هذه المهمة؟')) {
                    this.tasks = this.tasks.filter(t => t.id !== taskId);
                    this.saveTasks();
                    this.renderTasks();
                    this.updateStats();
                    this.showAlert('تم حذف المهمة', 'info');
                }
            }

            // تعديل مهمة
            editTask(taskId) {
                const task = this.tasks.find(t => t.id === taskId);
                if (task) {
                    const newText = prompt('تعديل المهمة:', task.text);
                    if (newText && newText.trim()) {
                        task.text = newText.trim();
                        task.updatedAt = new Date().toISOString();
                        this.saveTasks();
                        this.renderTasks();
                        this.showAlert('تم تعديل المهمة', 'success');
                    }
                }
            }

            // عرض المهام
            renderTasks() {
                const taskList = document.getElementById('taskList');
                const noTasksMessage = document.getElementById('noTasksMessage');

                if (this.tasks.length === 0) {
                    taskList.innerHTML = '';
                    noTasksMessage.style.display = 'block';
                    return;
                }

                noTasksMessage.style.display = 'none';

                taskList.innerHTML = this.tasks.map(task => `
                    <li class="task-item ${task.completed ? 'completed' : ''}">
                        <div class="task-checkbox ${task.completed ? 'checked' : ''}"
                             onclick="taskManager.toggleTask(${task.id})">
                            ${task.completed ? '<i class="fas fa-check"></i>' : ''}
                        </div>
                        <div class="task-text">${task.text}</div>
                        <div class="task-actions">
                            <button class="task-btn edit" onclick="taskManager.editTask(${task.id})" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="task-btn delete" onclick="taskManager.deleteTask(${task.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </li>
                `).join('');
            }

            // تحديث الإحصائيات
            updateStats() {
                const total = this.tasks.length;
                const completed = this.tasks.filter(task => task.completed).length;
                const pending = total - completed;
                const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0;

                document.getElementById('totalTasks').textContent = total;
                document.getElementById('completedTasks').textContent = completed;
                document.getElementById('pendingTasks').textContent = pending;
                document.getElementById('completionRate').textContent = completionRate + '%';
            }

            // مسح المهام المكتملة
            clearCompleted() {
                const completedCount = this.tasks.filter(task => task.completed).length;

                if (completedCount === 0) {
                    this.showAlert('لا توجد مهام مكتملة لمسحها', 'info');
                    return;
                }

                if (confirm(`هل أنت متأكد من مسح ${completedCount} مهمة مكتملة؟`)) {
                    this.tasks = this.tasks.filter(task => !task.completed);
                    this.saveTasks();
                    this.renderTasks();
                    this.updateStats();
                    this.showAlert(`تم مسح ${completedCount} مهمة مكتملة`, 'success');
                }
            }

            // مسح جميع المهام
            clearAll() {
                if (this.tasks.length === 0) {
                    this.showAlert('لا توجد مهام لمسحها', 'info');
                    return;
                }

                if (confirm('هل أنت متأكد من مسح جميع المهام؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                    this.tasks = [];
                    this.saveTasks();
                    this.renderTasks();
                    this.updateStats();
                    this.showAlert('تم مسح جميع المهام', 'info');
                }
            }

            // عرض تنبيه
            showAlert(message, type = 'info') {
                const alert = document.createElement('div');
                alert.className = `alert ${type}`;
                alert.innerHTML = `<i class="fas fa-info-circle"></i> ${message}`;
                alert.style.position = 'fixed';
                alert.style.top = '20px';
                alert.style.right = '20px';
                alert.style.zIndex = '9999';
                alert.style.minWidth = '300px';

                document.body.appendChild(alert);

                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 3000);
            }
        }

        // إنشاء مدير المهام
        let taskManager;

        // الانتقال لصفحة التسجيل
        function goToAuth() {
            window.location.href = 'standalone-auth.html';
        }

        // تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('tdl_session');
                localStorage.removeItem('tdl_current_user');
                window.location.href = 'standalone-auth.html';
            }
        }

        // إضافة مهمة
        function addTask() {
            taskManager.addTask();
        }

        // مسح المكتملة
        function clearCompleted() {
            taskManager.clearCompleted();
        }

        // مسح الكل
        function clearAll() {
            taskManager.clearAll();
        }

        // تهيئة التطبيق
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 تحميل التطبيق الرئيسي...');

            try {
                taskManager = new TaskManager();
                console.log('✅ تم تحميل التطبيق بنجاح');

                // رسالة ترحيب للمستخدم الجديد
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.get('welcome') === 'true') {
                    setTimeout(() => {
                        taskManager.showAlert('مرحباً بك في TDL! يمكنك الآن إضافة مهامك الأولى', 'success');
                    }, 1000);
                }
            } catch (error) {
                console.error('❌ خطأ في تحميل التطبيق:', error);
                alert('حدث خطأ في تحميل التطبيق، يرجى إعادة تحميل الصفحة');
            }
        });
    </script>
</body>
</html>

    <script>
        // نظام إدارة المهام المدمج
        class TaskManager {
            constructor() {
                this.tasks = this.loadTasks();
                this.currentUser = this.getCurrentUser();
                this.init();
            }

            init() {
                this.updateUserInfo();
                this.renderTasks();
                this.updateStats();
                this.setupEventListeners();
            }

            // تحميل المستخدم الحالي
            getCurrentUser() {
                try {
                    const userData = localStorage.getItem('tdl_current_user');
                    return userData ? JSON.parse(userData) : null;
                } catch (error) {
                    console.error('خطأ في تحميل بيانات المستخدم:', error);
                    return null;
                }
            }

            // تحميل المهام
            loadTasks() {
                try {
                    const tasks = localStorage.getItem('tdl_tasks');
                    return tasks ? JSON.parse(tasks) : [];
                } catch (error) {
                    console.error('خطأ في تحميل المهام:', error);
                    return [];
                }
            }

            // حفظ المهام
            saveTasks() {
                try {
                    localStorage.setItem('tdl_tasks', JSON.stringify(this.tasks));
                    this.updateUserTasks();
                } catch (error) {
                    console.error('خطأ في حفظ المهام:', error);
                }
            }

            // تحديث مهام المستخدم
            updateUserTasks() {
                if (this.currentUser) {
                    try {
                        const users = JSON.parse(localStorage.getItem('tdl_users') || '{}');
                        if (users[this.currentUser.username]) {
                            users[this.currentUser.username].tasks = this.tasks;
                            users[this.currentUser.username].stats = {
                                totalTasks: this.tasks.length,
                                completedTasks: this.tasks.filter(task => task.completed).length,
                                loginCount: users[this.currentUser.username].stats?.loginCount || 0
                            };
                            localStorage.setItem('tdl_users', JSON.stringify(users));
                        }
                    } catch (error) {
                        console.error('خطأ في تحديث بيانات المستخدم:', error);
                    }
                }
            }

            // تحديث معلومات المستخدم
            updateUserInfo() {
                const userNameEl = document.getElementById('userName');
                const userEmailEl = document.getElementById('userEmail');
                const userAvatarEl = document.getElementById('userAvatar');
                const logoutBtn = document.getElementById('logoutBtn');

                if (this.currentUser) {
                    userNameEl.textContent = `مرحباً ${this.currentUser.fullName}!`;
                    userEmailEl.textContent = this.currentUser.email;

                    // عرض الأحرف الأولى من الاسم
                    const initials = this.currentUser.fullName.split(' ').map(name => name[0]).join('').toUpperCase();
                    userAvatarEl.textContent = initials;

                    logoutBtn.style.display = 'flex';
                    document.querySelector('.btn.primary').style.display = 'none';

                    // تحميل مهام المستخدم
                    this.loadUserTasks();
                } else {
                    userNameEl.textContent = 'مرحباً بك!';
                    userEmailEl.textContent = 'يرجى تسجيل الدخول';
                    userAvatarEl.innerHTML = '<i class="fas fa-user"></i>';
                    logoutBtn.style.display = 'none';
                    document.querySelector('.btn.primary').style.display = 'flex';
                }
            }

            // تحميل مهام المستخدم
            loadUserTasks() {
                if (this.currentUser) {
                    try {
                        const users = JSON.parse(localStorage.getItem('tdl_users') || '{}');
                        const user = users[this.currentUser.username];
                        if (user && user.tasks) {
                            this.tasks = user.tasks;
                        }
                    } catch (error) {
                        console.error('خطأ في تحميل مهام المستخدم:', error);
                    }
                }
            }

            // إعداد مستمعي الأحداث
            setupEventListeners() {
                const taskInput = document.getElementById('taskInput');
                taskInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.addTask();
                    }
                });
            }

            // إضافة مهمة جديدة
            addTask() {
                const taskInput = document.getElementById('taskInput');
                const taskText = taskInput.value.trim();

                if (!taskText) {
                    this.showAlert('يرجى إدخال نص المهمة', 'error');
                    return;
                }

                if (!this.currentUser) {
                    this.showAlert('يرجى تسجيل الدخول أولاً', 'error');
                    return;
                }

                const newTask = {
                    id: Date.now(),
                    text: taskText,
                    completed: false,
                    createdAt: new Date().toISOString(),
                    userId: this.currentUser.username
                };

                this.tasks.push(newTask);
                this.saveTasks();
                this.renderTasks();
                this.updateStats();

                taskInput.value = '';
                this.showAlert('تم إضافة المهمة بنجاح!', 'success');
            }

            // تبديل حالة المهمة
            toggleTask(taskId) {
                const task = this.tasks.find(t => t.id === taskId);
                if (task) {
                    task.completed = !task.completed;
                    task.completedAt = task.completed ? new Date().toISOString() : null;
                    this.saveTasks();
                    this.renderTasks();
                    this.updateStats();
                }
            }

            // حذف مهمة
            deleteTask(taskId) {
                if (confirm('هل أنت متأكد من حذف هذه المهمة؟')) {
                    this.tasks = this.tasks.filter(t => t.id !== taskId);
                    this.saveTasks();
                    this.renderTasks();
                    this.updateStats();
                    this.showAlert('تم حذف المهمة', 'info');
                }
            }

            // تعديل مهمة
            editTask(taskId) {
                const task = this.tasks.find(t => t.id === taskId);
                if (task) {
                    const newText = prompt('تعديل المهمة:', task.text);
                    if (newText && newText.trim()) {
                        task.text = newText.trim();
                        task.updatedAt = new Date().toISOString();
                        this.saveTasks();
                        this.renderTasks();
                        this.showAlert('تم تعديل المهمة', 'success');
                    }
                }
            }

            // عرض المهام
            renderTasks() {
                const taskList = document.getElementById('taskList');
                const noTasksMessage = document.getElementById('noTasksMessage');

                if (this.tasks.length === 0) {
                    taskList.innerHTML = '';
                    noTasksMessage.style.display = 'block';
                    return;
                }

                noTasksMessage.style.display = 'none';

                taskList.innerHTML = this.tasks.map(task => `
                    <li class="task-item ${task.completed ? 'completed' : ''}">
                        <div class="task-checkbox ${task.completed ? 'checked' : ''}"
                             onclick="taskManager.toggleTask(${task.id})">
                            ${task.completed ? '<i class="fas fa-check"></i>' : ''}
                        </div>
                        <div class="task-text">${task.text}</div>
                        <div class="task-actions">
                            <button class="task-btn edit" onclick="taskManager.editTask(${task.id})" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="task-btn delete" onclick="taskManager.deleteTask(${task.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </li>
                `).join('');
            }

            // تحديث الإحصائيات
            updateStats() {
                const total = this.tasks.length;
                const completed = this.tasks.filter(task => task.completed).length;
                const pending = total - completed;
                const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0;

                document.getElementById('totalTasks').textContent = total;
                document.getElementById('completedTasks').textContent = completed;
                document.getElementById('pendingTasks').textContent = pending;
                document.getElementById('completionRate').textContent = completionRate + '%';
            }

            // مسح المهام المكتملة
            clearCompleted() {
                const completedCount = this.tasks.filter(task => task.completed).length;

                if (completedCount === 0) {
                    this.showAlert('لا توجد مهام مكتملة لمسحها', 'info');
                    return;
                }

                if (confirm(`هل أنت متأكد من مسح ${completedCount} مهمة مكتملة؟`)) {
                    this.tasks = this.tasks.filter(task => !task.completed);
                    this.saveTasks();
                    this.renderTasks();
                    this.updateStats();
                    this.showAlert(`تم مسح ${completedCount} مهمة مكتملة`, 'success');
                }
            }

            // مسح جميع المهام
            clearAll() {
                if (this.tasks.length === 0) {
                    this.showAlert('لا توجد مهام لمسحها', 'info');
                    return;
                }

                if (confirm('هل أنت متأكد من مسح جميع المهام؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                    this.tasks = [];
                    this.saveTasks();
                    this.renderTasks();
                    this.updateStats();
                    this.showAlert('تم مسح جميع المهام', 'info');
                }
            }

            // عرض تنبيه
            showAlert(message, type = 'info') {
                const alert = document.createElement('div');
                alert.className = `alert ${type}`;
                alert.innerHTML = `<i class="fas fa-info-circle"></i> ${message}`;
                alert.style.position = 'fixed';
                alert.style.top = '20px';
                alert.style.right = '20px';
                alert.style.zIndex = '9999';
                alert.style.minWidth = '300px';

                document.body.appendChild(alert);

                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 3000);
            }
        }

        // إنشاء مدير المهام
        let taskManager;

        // الانتقال لصفحة التسجيل
        function goToAuth() {
            window.location.href = 'standalone-auth.html';
        }

        // تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('tdl_session');
                localStorage.removeItem('tdl_current_user');
                window.location.href = 'standalone-auth.html';
            }
        }

        // إضافة مهمة
        function addTask() {
            taskManager.addTask();
        }

        // مسح المكتملة
        function clearCompleted() {
            taskManager.clearCompleted();
        }

        // مسح الكل
        function clearAll() {
            taskManager.clearAll();
        }

        // تهيئة التطبيق
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 تحميل التطبيق الرئيسي...');

            try {
                taskManager = new TaskManager();
                console.log('✅ تم تحميل التطبيق بنجاح');

                // رسالة ترحيب للمستخدم الجديد
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.get('welcome') === 'true') {
                    setTimeout(() => {
                        taskManager.showAlert('مرحباً بك في TDL! يمكنك الآن إضافة مهامك الأولى', 'success');
                    }, 1000);
                }
            } catch (error) {
                console.error('❌ خطأ في تحميل التطبيق:', error);
                alert('حدث خطأ في تحميل التطبيق، يرجى إعادة تحميل الصفحة');
            }
        });
    </script>
</body>
</html>
