<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TDL V1.0 - تسجيل الدخول</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="auth-body">
    <!-- خلفية متحركة -->
    <div class="auth-background">
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
            <div class="shape shape-5"></div>
        </div>
    </div>

    <!-- حاوي التطبيق -->
    <div class="auth-container">
        <!-- شعار التطبيق -->
        <div class="auth-header">
            <div class="app-logo">
                <i class="fas fa-tasks"></i>
                <h1>TDL V1.0</h1>
                <p>مدير المهام المتقدم</p>
            </div>
        </div>

        <!-- نموذج تسجيل الدخول -->
        <div class="auth-form-container" id="loginForm">
            <div class="auth-form">
                <h2><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</h2>
                
                <form id="loginFormElement">
                    <div class="form-group">
                        <label for="loginUsername">
                            <i class="fas fa-user"></i>
                            اسم المستخدم
                        </label>
                        <input type="text" id="loginUsername" name="username" required 
                               placeholder="أدخل اسم المستخدم">
                    </div>

                    <div class="form-group">
                        <label for="loginPassword">
                            <i class="fas fa-lock"></i>
                            كلمة المرور
                        </label>
                        <div class="password-input">
                            <input type="password" id="loginPassword" name="password" required 
                                   placeholder="أدخل كلمة المرور">
                            <button type="button" class="toggle-password" data-target="loginPassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" id="rememberMe">
                            <span class="checkmark"></span>
                            تذكرني
                        </label>
                    </div>

                    <button type="submit" class="auth-btn primary">
                        <i class="fas fa-sign-in-alt"></i>
                        تسجيل الدخول
                    </button>
                </form>

                <div class="auth-footer">
                    <p>ليس لديك حساب؟ 
                        <a href="#" id="showRegister">إنشاء حساب جديد</a>
                    </p>
                </div>
            </div>
        </div>

        <!-- نموذج التسجيل -->
        <div class="auth-form-container hidden" id="registerForm">
            <div class="auth-form">
                <h2><i class="fas fa-user-plus"></i> إنشاء حساب جديد</h2>
                
                <form id="registerFormElement">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="registerFullName">
                                <i class="fas fa-id-card"></i>
                                الاسم الكامل
                            </label>
                            <input type="text" id="registerFullName" name="fullName" required 
                                   placeholder="أدخل اسمك الكامل">
                        </div>

                        <div class="form-group">
                            <label for="registerUsername">
                                <i class="fas fa-user"></i>
                                اسم المستخدم
                            </label>
                            <input type="text" id="registerUsername" name="username" required 
                                   placeholder="اختر اسم مستخدم">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="registerEmail">
                            <i class="fas fa-envelope"></i>
                            البريد الإلكتروني
                        </label>
                        <input type="email" id="registerEmail" name="email" required 
                               placeholder="أدخل بريدك الإلكتروني">
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="registerPassword">
                                <i class="fas fa-lock"></i>
                                كلمة المرور
                            </label>
                            <div class="password-input">
                                <input type="password" id="registerPassword" name="password" required 
                                       placeholder="أدخل كلمة مرور قوية">
                                <button type="button" class="toggle-password" data-target="registerPassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="confirmPassword">
                                <i class="fas fa-lock"></i>
                                تأكيد كلمة المرور
                            </label>
                            <div class="password-input">
                                <input type="password" id="confirmPassword" name="confirmPassword" required 
                                       placeholder="أعد إدخال كلمة المرور">
                                <button type="button" class="toggle-password" data-target="confirmPassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- اختيار الصورة الرمزية -->
                    <div class="form-group">
                        <label>
                            <i class="fas fa-image"></i>
                            الصورة الرمزية
                        </label>
                        <div class="avatar-selection">
                            <div class="avatar-preview" id="avatarPreview">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="avatar-options">
                                <button type="button" class="avatar-btn" data-type="upload">
                                    <i class="fas fa-upload"></i>
                                    رفع صورة
                                </button>
                                <button type="button" class="avatar-btn" data-type="generate">
                                    <i class="fas fa-magic"></i>
                                    إنشاء تلقائي
                                </button>
                            </div>
                            <input type="file" id="avatarUpload" accept="image/*" style="display: none;">
                        </div>
                    </div>

                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" id="agreeTerms" required>
                            <span class="checkmark"></span>
                            أوافق على <a href="#" class="terms-link">الشروط والأحكام</a>
                        </label>
                    </div>

                    <button type="submit" class="auth-btn primary">
                        <i class="fas fa-user-plus"></i>
                        إنشاء الحساب
                    </button>
                </form>

                <div class="auth-footer">
                    <p>لديك حساب بالفعل؟ 
                        <a href="#" id="showLogin">تسجيل الدخول</a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- رسائل التنبيه -->
    <div id="authNotification" class="auth-notification hidden">
        <div class="notification-content">
            <i class="notification-icon"></i>
            <span class="notification-message"></span>
        </div>
    </div>

    <!-- مؤشر التحميل -->
    <div id="authLoader" class="auth-loader hidden">
        <div class="loader-content">
            <div class="spinner"></div>
            <p>جاري المعالجة...</p>
        </div>
    </div>

    <!-- الملفات الجافا سكريبت -->
    <script type="module" src="js/user-manager.js"></script>
    <script type="module" src="js/auth-interface.js"></script>
</body>
</html>
