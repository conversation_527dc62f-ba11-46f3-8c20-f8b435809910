<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 192 192">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4a6cf7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2541b2;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="rgba(0,0,0,0.3)" />
    </filter>
  </defs>
  
  <!-- Background -->
  <circle cx="96" cy="96" r="88" fill="url(#grad1)" filter="url(#shadow)" />
  
  <!-- Plus symbol -->
  <g fill="none" stroke="#ffffff" stroke-width="12" stroke-linecap="round">
    <line x1="96" y1="58" x2="96" y2="134" />
    <line x1="58" y1="96" x2="134" y2="96" />
  </g>
  
  <!-- Task list icon -->
  <g fill="#ffffff" opacity="0.8" transform="translate(58, 58) scale(0.4)">
    <rect x="100" y="40" width="80" height="12" rx="6" ry="6" />
    <rect x="100" y="80" width="80" height="12" rx="6" ry="6" />
    <rect x="100" y="120" width="80" height="12" rx="6" ry="6" />
    
    <rect x="40" y="34" width="24" height="24" rx="4" ry="4" />
    <rect x="40" y="74" width="24" height="24" rx="4" ry="4" />
    <rect x="40" y="114" width="24" height="24" rx="4" ry="4" />
  </g>
</svg>