# دليل المساهمة - تطبيق المهام المتقدم

شكراً لاهتمامك بالمساهمة في تطبيق المهام المتقدم! هذا الدليل سيساعدك على البدء.

## 🤝 كيفية المساهمة

### الإبلاغ عن الأخطاء
1. تحقق من أن الخطأ لم يتم الإبلاغ عنه مسبقاً
2. استخدم قالب الإبلاغ عن الأخطاء
3. وصف الخطأ بالتفصيل مع خطوات إعادة الإنتاج

### اقتراح ميزات جديدة
1. تحقق من أن الميزة لم يتم اقتراحها مسبقاً
2. اشرح الميزة وفوائدها بالتفصيل
3. اقترح كيفية تنفيذها إذا أمكن

### إرسال تحسينات
1. Fork المشروع
2. أنشئ فرع جديد للميزة
3. اكتب الكود مع التعليقات
4. اختبر التغييرات
5. أرسل Pull Request

## 🛠️ إعداد بيئة التطوير

### المتطلبات
- متصفح حديث
- محرر نصوص (VS Code مفضل)
- خادم محلي (Python أو Node.js)

### التثبيت
```bash
# Clone المشروع
git clone https://github.com/tdl-team/advanced-todo-app.git

# انتقل للمجلد
cd advanced-todo-app

# شغل الخادم المحلي
python -m http.server 8000
# أو
npm start
```

## 📝 معايير الكود

### JavaScript
- استخدم ES6+ syntax
- اكتب تعليقات باللغة العربية
- اتبع نمط التسمية camelCase
- استخدم const و let بدلاً من var

### CSS
- استخدم متغيرات CSS
- اتبع نمط BEM للتسمية
- اكتب CSS متجاوب
- استخدم Flexbox و Grid

### HTML
- استخدم semantic tags
- اكتب HTML صالح
- أضف alt للنصوص للصور
- استخدم ARIA labels

## 🧪 الاختبار

### اختبار الوظائف
- اختبر إضافة المهام
- اختبر حذف المهام
- اختبر تعديل المهام
- اختبر البحث والتصفية
- اختبر الإعدادات

### اختبار التوافق
- اختبر في Chrome
- اختبر في Firefox
- اختبر في Safari
- اختبر في الأجهزة المحمولة

### اختبار الأداء
- تحقق من سرعة التحميل
- تحقق من استهلاك الذاكرة
- تحقق من التفاعل السلس

## 📋 قالب Pull Request

```markdown
## وصف التغييرات
وصف مختصر للتغييرات المضافة

## نوع التغيير
- [ ] إصلاح خطأ
- [ ] ميزة جديدة
- [ ] تحسين الأداء
- [ ] تحسين الواجهة
- [ ] توثيق

## الاختبار
- [ ] تم اختبار التغييرات محلياً
- [ ] تم اختبار التوافق مع المتصفحات
- [ ] تم اختبار الأجهزة المحمولة

## لقطات شاشة (إذا كان مناسباً)
أضف لقطات شاشة للتغييرات البصرية

## ملاحظات إضافية
أي معلومات إضافية مهمة
```

## 🎯 مجالات المساهمة

### الواجهة الأمامية
- تحسين التصميم
- إضافة تأثيرات حركية
- تحسين تجربة المستخدم
- إضافة ميزات تفاعلية

### الوظائف
- إضافة ميزات جديدة
- تحسين الأداء
- إصلاح الأخطاء
- تحسين الكود

### التوثيق
- تحسين README
- إضافة أمثلة
- كتابة دليل المستخدم
- تحديث CHANGELOG

### الاختبار
- كتابة اختبارات
- تحسين التوافق
- اختبار الأداء
- اختبار الأمان

## 📞 التواصل

- **Issues**: للإبلاغ عن الأخطاء واقتراح الميزات
- **Discussions**: للمناقشات العامة
- **Pull Requests**: لإرسال التحسينات

## 🙏 شكر وتقدير

شكراً لجميع المساهمين الذين يساعدون في تطوير هذا المشروع!

---

**معاً نجعل تطبيق المهام المتقدم أفضل! 🚀** 